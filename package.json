{"private": true, "scripts": {"dev": "vite", "build": "vite build", "prod": "vite build", "production": "vite build", "build-text-card": "cd nova-components/TextCard && npm run dev", "build-text-card-prod": "cd nova-components/TextCard && npm run prod"}, "devDependencies": {"@shufo/prettier-plugin-blade": "^1.14.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.15", "@vitejs/plugin-vue2": "^2.3.1", "autoprefixer": "^10.4.20", "axios": "^1.6.8", "cross-env": "^7.0.3", "laravel-mix": "^6.0.49", "laravel-vite-plugin": "^0.8.1", "livewire-sortable": "^1.0.0", "lodash": "^4.17.21", "postcss": "^8.4.47", "postcss-import": "^16.0.1", "prettier": "^3.2.5", "resolve-url-loader": "^5.0.0", "sass": "^1.30.0", "sass-loader": "^8.0.2", "tailwindcss": "^3.4.14", "vite": "^4.5.5", "vue": "^2.6.14", "vue-template-compiler": "^0.1.0"}, "dependencies": {"@alpinejs/collapse": "^3.14.3", "@alpinejs/sort": "^3.14.3", "@alpinejs/ui": "^3.13.5-beta.0", "@popperjs/core": "^2.5.4", "@ryangjchandler/alpine-clipboard": "^2.3.0", "@tiptap/core": "^2.9.1", "@tiptap/extension-color": "^2.9.1", "@tiptap/extension-link": "^2.9.1", "@tiptap/extension-table": "^2.9.1", "@tiptap/extension-table-cell": "^2.9.1", "@tiptap/extension-table-header": "^2.9.1", "@tiptap/extension-table-row": "^2.9.1", "@tiptap/extension-text-style": "^2.9.1", "@tiptap/extension-underline": "^2.9.1", "@tiptap/starter-kit": "^2.9.1", "alpinejs": "^3.13.10", "build": "^0.1.4", "choices.js": "^10.2.0", "cropperjs": "^1.6.2", "es6-promise": "^4.2.8", "filepond": "^4.31.4", "filepond-plugin-file-encode": "^2.1.14", "filepond-plugin-file-validate-size": "^2.2.8", "filepond-plugin-file-validate-type": "^1.2.9", "filepond-plugin-image-exif-orientation": "^1.0.11", "filepond-plugin-image-preview": "^4.6.12", "flatpickr": "^4.6.13", "imagesloaded": "^4.1.4", "infinite-scroll": "^3.0.6", "js-cookie": "^2.2.1", "laravel-mix-purgecss": "^6.0.0", "masonry-layout": "^4.2.2", "popper.js": "^1.16.1", "rollup-plugin-copy": "^3.5.0", "scriptjs": "^2.5.9", "simplebar": "^5.3.9", "tippy.js": "^6.3.7", "trix": "^1.3.2", "vue-select": "^3.20.3", "zenscroll": "^4.0.2"}, "browserslist": ["last 1 major version", ">= 1%", "ie >= 10", "edge >= 12", "ff >= 38", "chrome >= 45", "safari >= 9", "Opera >= 30", "ios >= 9", "android >= 4.4", "ie_mob >= 11"], "type": "module"}