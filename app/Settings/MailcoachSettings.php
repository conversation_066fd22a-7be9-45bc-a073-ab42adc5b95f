<?php

namespace App\Settings;

use App\Enums\SystemSettings\SystemSettingGroupEnum;
use <PERSON><PERSON>\LaravelSettings\Settings;

class MailcoachSettings extends Settings
{
    public static function enabled(): bool
    {
        $enabled = config('services.mailcoach.enabled');

        assert(is_bool($enabled));

        return $enabled;
    }

    public static function list(): string
    {
        $list = config('services.mailcoach.list_id');

        assert(is_string($list));

        return $list;
    }

    public static function token(): string
    {
        $token = config('services.mailcoach.token');

        assert(is_string($token));

        return $token;
    }

    public static function url(): string
    {
        $url = config('services.mailcoach.url');

        assert(is_string($url));

        return $url;
    }

    public static function group(): string
    {
        return SystemSettingGroupEnum::MAILCOACH->value;
    }
}
