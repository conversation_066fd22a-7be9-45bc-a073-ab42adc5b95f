<?php

namespace App\Settings;

use App\Enums\SystemSettings\SystemSettingGroupEnum;
use App\Features\IhreApotheken;
use App\Features\IhreApothekenPreflight;
use Carbon\Carbon;
use Spatie\LaravelSettings\Settings;

class IaSettings extends Settings implements HasPurgeablesInterface
{
    public Carbon $enabledAt;

    public Carbon $preflightEnabledUntil;

    public string $applicationId;

    public string $termsOfUseExistingCustomersId;

    public string $termsOfUseNewCustomersId;

    public string $privacyPolicyId;

    public static function group(): string
    {
        return SystemSettingGroupEnum::IA->value;
    }

    public function purgeables(): array
    {
        return [
            IhreApotheken::class,
            IhreApothekenPreflight::class,
        ];
    }
}
