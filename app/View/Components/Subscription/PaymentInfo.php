<?php

namespace App\View\Components\Subscription;

use App\Pharmacy;
use App\Subscription;
use Illuminate\View\Component;

class PaymentInfo extends Component
{
    public Subscription $subscription;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(Subscription $subscription)
    {
        $this->subscription = $subscription;
    }

    public function getOrderModelForPharmacy(Pharmacy $pharmacy)
    {
        return $this->subscription->makeOrderForCurrentCycle($pharmacy);
    }

    public function calcPrice()
    {
        return $this->subscription->calcPriceForSubscribable(currentPharmacy());
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.subscription.payment-info');
    }
}
