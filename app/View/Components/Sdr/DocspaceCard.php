<?php

namespace App\View\Components\Sdr;

use App\DocSpace;
use Illuminate\View\Component;

class <PERSON>spaceCard extends Component
{
    public DocSpace $docSpace;

    public function __construct($docSpace)
    {
        $this->docSpace = $docSpace;
    }

    public function render(): \Illuminate\Contracts\View\View|\Closure|string
    {
        return view('components.sdr.docspace-card', [
            'docSpace' => $this->docSpace,
            'menuoptions' => $this->setDocSpaceOptionsForUser(),
        ]);
    }

    protected function setDocSpaceOptionsForUser(): array
    {
        $options = [];
        if (! is_null($this->docSpace)) {
            if (user()->can('sdr.openInRiseUI', [$this->docSpace])) {
                $options[] = [
                    'name' => 'Öffnen',
                    'link' => config('sdr.uiUrl').'/documents?docSpaceId='.$this->docSpace->sdr_doc_space_id,
                    'icon' => 'arrow-top-right-on-square',
                    'target' => '_blank',
                ];
            }

            if (user()->can('update', $this->docSpace)) {
                $options[] = [
                    'name' => 'Details',
                    'link' => route(
                        'sdr.doc-spaces.show',
                        [
                            'pharmacy' => $this->docSpace->pharmacy,
                            'docSpace' => $this->docSpace->id,
                        ]
                    ),
                    'icon' => 'eye',
                ];
                $options[] = [
                    'name' => 'Bearbeiten',
                    'link' => route(
                        'sdr.doc-spaces.show',
                        [
                            'pharmacy' => $this->docSpace->pharmacy,
                            'docSpace' => $this->docSpace->id,
                            'details' => 0,
                        ]
                    ),
                    'icon' => 'pencil',
                ];
            }
        }

        return $options;
    }
}
