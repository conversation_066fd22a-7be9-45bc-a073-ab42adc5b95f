<?php

namespace App\Livewire;

use App\BillingAddress;
use App\Observers\BillingAddressObserver;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;
use Livewire\Component;

class BillingAddresses extends Component
{
    use AuthorizesRequests;

    public Collection $billingAddresses;

    public function mount(): void
    {
        assert(user() !== null);
        $this->billingAddresses = user()->billingAddressesAll();
    }

    /**
     * @throws AuthorizationException
     */
    public function updatePharmacyBillingAddress(array $billingAddressPharmaciesMap): void
    {
        foreach ($billingAddressPharmaciesMap as ['value' => $id, 'items' => $pharmacyArray]) {
            $billingAddress = $this->billingAddresses->firstWhere('id', $id);
            assert($billingAddress instanceof BillingAddress);

            $this->authorize('update', $billingAddress);

            $pharmacyIds = collect($pharmacyArray)->pluck('value');
            $billingAddress->pharmacies()->saveMany(user()->pharmacies->whereIn('id', $pharmacyIds));
        }
        $this->billingAddresses->flatMap->refresh();
        $this->billingAddresses->each(function ($billingAddress) {
            assert($billingAddress instanceof BillingAddress);
            BillingAddressObserver::updating($billingAddress);
        });
    }

    /**
     * @throws AuthorizationException
     */
    public function associateAllPharmacies(BillingAddress $billingAddress): void
    {
        $this->authorize('update', $billingAddress);

        $billingAddress->pharmacies()->saveMany(user()->pharmacies);
        BillingAddressObserver::updating($billingAddress);
        $this->billingAddresses->flatMap->refresh();
    }

    /**
     * @throws AuthorizationException
     */
    public function deleteBillingAddress(BillingAddress $billingAddress): void
    {
        $this->authorize('delete', $billingAddress);

        $billingAddress->delete();
        assert(user() !== null);
        $this->billingAddresses = user()->billingAddressesAll();

        $this->dispatch('close-modal');
    }

    public function render()
    {
        return view('livewire.billing-addresses');
    }

    public function exception(Exception $e, callable $stopPropagation): void
    {
        if ($e instanceof ValidationException) {
            return;
        }
        $stopPropagation();
        report($e);
        notify('Es ist ein Fehler aufgetreten. Unsere Techniker wurden informiert. Versuchen Sie es später erneut.', 'error', seconds: 10);
        $this->redirect(route('users.billing-addresses'));
    }
}
