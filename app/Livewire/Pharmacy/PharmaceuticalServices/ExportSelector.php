<?php

namespace App\Livewire\Pharmacy\PharmaceuticalServices;

use App\Enums\PharmaceuticalService\PharmaceuticalServiceStatus;
use App\Enums\PharmaceuticalServiceInvoiceStatus;
use App\Jobs\GeneratePharmaceuticalServiceInvoice;
use App\PharmaceuticalServiceInvoice;
use App\Pharmacy;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Livewire\Component;
use Livewire\WithPagination;

class ExportSelector extends Component
{
    use WithPagination;

    public Pharmacy $pharmacy;

    protected $startYear = 2022;

    protected $startMonth = 11;

    public $year = '';

    public $month = '';

    public $yearRange = [];

    public $monthRange = [];

    public $open = false;

    public $timeRangeRadio = 1;

    public $export_end = null;

    public $export_start = null;

    public $currentlyExporting = true;

    public function paginationView()
    {
        return 'livewire.partials.pagination';
    }

    public function mount()
    {
        $this->getYearRange();
        $this->year = now()->subMonthNoOverflow()->year;
        $this->getMonthRange();
        $this->month = now()->subMonthNoOverflow()->month;
        $this->pollForQueueEntries();
    }

    public function downloadExportsMonthly()
    {
        abort_unless(user()->can('store', [PharmaceuticalServiceInvoice::class, $this->pharmacy]), 403);

        $month = Carbon::createFromDate($this->year, $this->month)->startOfDay();
        $startDate = clone $month;
        $startDate = $startDate->setDay($month->startOfMonth()->day);
        $endDate = clone $month;
        $endDate = $endDate->setDay($month->endOfMonth()->day);

        $this->executeDownload($startDate, $endDate);
    }

    public function downloadExportsCustomTime()
    {
        abort_unless(user()->can('store', [PharmaceuticalServiceInvoice::class, $this->pharmacy]), 403);

        $this->validate([
            'export_start' => ['required', 'date', 'after:'.now()->subYear()->subDay()->format('d.m.Y')],
            'export_end' => ['required', 'date', 'before:'.now()->addDay()->format('d.m.Y'), 'after:export_start'],
        ]);

        $this->executeDownload($this->export_start, $this->export_end);
    }

    private function executeDownload($startDate, $endDate)
    {
        $download = $this
            ->pharmacy
            ->influenzaVaccinationInvoices()
            ->where('start_date', $startDate)
            ->where('end_date', $endDate)
            ->where('status', PharmaceuticalServiceInvoiceStatus::GENERATING)
            ->first();

        if ($download) {
            $this->notify('Bitte warten Sie bis Ihre Abrechnung erstellt wurde.', 'error');

            return;
        }

        $download = $this->pharmacy->pharmaceuticalServiceInvoices()->create([
            'user_id' => user()->id,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);

        $count = $this->pharmacy->pharmaceuticalServices()
            ->whereBetween('date', [$startDate, $endDate])
            ->where('status', PharmaceuticalServiceStatus::FINISHED)
            ->whereNull('reasons_to_abort')
            ->count();

        GeneratePharmaceuticalServiceInvoice::dispatch($download)->onQueue($count > 2000 ? 'covid-vaccination-invoices' : 'covid-vaccination-invoices-fast');

        $this->open = true;

        $this->resetPage();

        $this->currentlyExporting = true;
    }

    public function pollForQueueEntries()
    {
        $this->currentlyExporting = $this->pharmacy->pharmaceuticalServiceInvoices()->status(PharmaceuticalServiceInvoiceStatus::GENERATING)->exists();
    }

    /**
     * Year and month selector:
     * These methods generate the dropdown of year and month.
     */
    public function getMonthRange()
    {
        if (! $this->year) {
            $this->monthRange = collect([]);

            return;
        }

        if ($this->year == $this->startYear) {
            if (now()->year == $this->startYear) {
                $this->monthRange = collect(CarbonPeriod::create($this->year.'-'.$this->startMonth, '1 Month', $this->year.'-'.now()->month)->toArray())->map(fn ($dateTime) => (int) $dateTime->format('m'));

                return;
            }

            if (now()->subMonthNoOverflow()->year > $this->year) {
                $this->monthRange = collect(CarbonPeriod::create($this->year.'-'.$this->startMonth, '1 Month', $this->startYear.'-12')->toArray())->map(fn ($dateTime) => (int) $dateTime->format('m'));

                return;
            }
        }

        if (now()->subMonthNoOverflow()->year == $this->year) {
            $this->monthRange = collect(CarbonPeriod::create($this->year.'-01', '1 Month', $this->year.'-'.now()->subMonthNoOverflow()->month)->toArray())->map(fn ($dateTime) => (int) $dateTime->format('m'));

            return;
        }

        $this->monthRange = collect(CarbonPeriod::create($this->year.'-01', '1 Month', $this->year.'-12')->toArray())->map(fn ($dateTime) => (int) $dateTime->format('m'));
    }

    public function getYearRange()
    {
        if (now()->subMonthNoOverflow()->year != $this->startYear) {
            $this->yearRange = collect(CarbonPeriod::create($this->startYear.'-01', '1 Year', now()->subMonthNoOverflow()->year.'-01')->toArray())->map(fn ($dateTime) => (int) $dateTime->format('Y'))->toArray();
        } else {
            $this->yearRange = [$this->startYear];
        }
    }

    public function updatedYear()
    {
        $this->getMonthRange();

        if (! $this->monthRange->contains($this->month)) {
            $this->month = $this->monthRange->first();
        }
    }

    public function render()
    {
        return view('livewire.pharmacy.pharmaceutical-services.export-selector', [
            'downloads' => $this->pharmacy->pharmaceuticalServiceInvoices()->orderByDesc('id')->paginate(8),
        ]);
    }
}
