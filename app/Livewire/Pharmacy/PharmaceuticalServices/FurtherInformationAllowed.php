<?php

namespace App\Livewire\Pharmacy\PharmaceuticalServices;

use App\PharmaceuticalService;
use Illuminate\View\View;
use Livewire\Component;

class FurtherInformationAllowed extends Component
{
    public bool $furtherInformationAllowed = false;

    public PharmaceuticalService $pharmaceuticalService;

    public function mount(): void
    {
        $this->furtherInformationAllowed = $this->pharmaceuticalService->further_information_allowed;
    }

    public function updatedFurtherInformationAllowed(): void
    {
        $this->pharmaceuticalService->further_information_allowed = $this->furtherInformationAllowed;

        $this->pharmaceuticalService->save();
    }

    public function render(): View
    {
        return view('livewire.pharmacy.pharmaceutical-services.further-information-allowed');
    }
}
