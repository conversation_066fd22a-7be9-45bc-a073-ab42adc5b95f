<?php

namespace App\Livewire\Pharmacy\DocSpace;

use App\DocSpace;
use App\Jobs\RefreshDocSpaceUsageJob;
use App\Pharmacy;
use App\User;
use Livewire\Component;

class Index extends Component
{
    public Pharmacy $pharmacy;

    public $docSpaces = [];

    public function mount(Pharmacy $pharmacy)
    {
        $this->pharmacy = $pharmacy;

        if (! (auth()->user() instanceof User)) {
            throw new \RuntimeException('User not found');
        }

        $this->docSpaces = $pharmacy->docSpaces()
            ->when(! user()?->can('sdr.viewAny', $pharmacy), function ($query) {
                /** @phpstan-ignore-next-line - phpstan can not resolve users on docSpaceGroup model */
                return $query->whereHas('docSpaceGroups.users', fn ($x) => $x->where('id', user()->id));
            })
            ->get();

        $this->docSpaces->each(fn (DocSpace $docSpace) => RefreshDocSpaceUsageJob::dispatchSync($docSpace));
    }

    public function render()
    {
        return view('livewire.pharmacy.doc-space.index');
    }
}
