<?php

namespace App\Livewire;

use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class MatrixBootstrap extends Component
{
    protected $listeners = [FeatureRecommendation::EVENT_BEFORE_SAVE => 'beforeSave'];

    public function beforeSave(): void
    {
        $this->dispatch(FeatureRecommendation::EVENT_SAVE);
    }

    public function render(): Factory|View|Application
    {
        return view('livewire.featureRecommendation.matrix-bootstrap');
    }
}
