<?php

namespace App\Livewire;

use App\Support\Feature\ChatEnd2EndAndApoPortalRelease;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use Livewire\Attributes\On;
use Livewire\Component;

class FeatureRecommendation extends Component
{
    public const EVENT_BEFORE_SAVE = 'before-save-feature-recommendation';

    public const EVENT_SAVE = 'save-feature-recommendation';

    public const EVENT_CLOSE = 'ignore-feature-recommendation';

    public const EVENT_FORCE_OPEN = 'force-open-feature-recommendation';

    /** @var \App\Support\FeatureRecommendation */
    public $featureName = '';

    public $title = '';

    public ?string $buttonText = null;

    public ?string $livewireContentComponentName;

    public $modalOpen = false;

    public array $recommendations = [
        ChatEnd2EndAndApoPortalRelease::class,
    ];

    public function mount(): void
    {
        $this->showModal();
    }

    #[On(self::EVENT_FORCE_OPEN)]
    public function showModal(?int $featureId = null): void
    {
        if ($this->modalOpen) {
            $this->skipRender();

            return;
        }

        foreach ($this->recommendations as $item) {
            /** @var \App\Support\FeatureRecommendation $item */
            if (! $item::show()) {
                continue;
            }

            if ($featureId !== null ? $item::getFeatureId()->value === $featureId : ! $item::passed()) {
                $this->featureName = $item;

                $feature = app($item);

                $this->title = $feature->getTitle();
                $this->livewireContentComponentName = $feature->getLivewireContentComponentName();
                $this->buttonText = $feature->getButtonText();

                $this->modalOpen = true;

                $this->openModal('feature-recommendation-modal');

                break;
            }
        }
    }

    #[On(self::EVENT_CLOSE)]
    #[On(self::EVENT_SAVE)]
    public function save(): RedirectResponse|Application|Redirector
    {
        if ($this->featureName === '') {
            return $this->reloadPage();
        }

        $this->featureName::save();

        return $this->reloadPage();
    }

    public function render(): Factory|View|Application
    {
        return view('livewire.featureRecommendation.index');
    }

    public function reloadPage(): Application|RedirectResponse|Redirector
    {
        return redirect(request()?->header('Referer') ?? route('dashboard'));
    }
}
