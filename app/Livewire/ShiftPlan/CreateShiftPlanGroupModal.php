<?php

namespace App\Livewire\ShiftPlan;

use App\Domains\ShiftPlan\Domain\Actions\ShiftPlanGroup\CreateShiftPlanGroupAction;
use App\Domains\ShiftPlan\Domain\Data\ShiftPlanGroupData;
use App\ShiftPlan;
use App\ShiftPlanGroup;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use Illuminate\View\View;
use Livewire\Attributes\On;
use Livewire\Component;

class CreateShiftPlanGroupModal extends Component
{
    public ShiftPlan $shiftPlan;

    public string $title = '';

    public string $color = '';

    public function addGroup(): RedirectResponse|Redirector
    {
        $this->validate([
            'title' => 'required|string|max:255',
            'color' => 'required|hex_color',
        ]);

        $shiftPlanGroups = ShiftPlanGroup::query()
            ->with('shiftPlanGroupUsers')
            ->where('shift_plan_id', $this->shiftPlan->id)
            ->orderBy('sort_order')
            ->get();

        $maxSortOrder = $shiftPlanGroups->max('sort_order');

        $data = ShiftPlanGroupData::from([
            'shift_plan_id' => $this->shiftPlan->id,
            'name' => $this->title,
            'color' => $this->color,
            'sort_order' => $maxSortOrder + 1,
        ]);

        CreateShiftPlanGroupAction::execute($data);

        $this->cleanUp();
        $this->dispatch('shiftplan-group-created');

        return redirect()->route('shiftplans.view', $this->shiftPlan);
    }

    public function render(): View
    {
        return view('livewire.shift-plan.create-shift-plan-group-modal');
    }

    #[On('shiftplan-group.modal.on-close')]
    public function cleanUp(): void
    {
        $this->reset(['title', 'color']);
    }

    public function onCloseModal(): void
    {
        $this->cleanUp();
        $this->closeModal('createShiftPlanGroup');
    }
}
