<?php

namespace App\Livewire;

use App\Domains\Subscription\Application\FeatureAccess\ChatFeatureAccess;
use App\Pharmacy;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Livewire\Component;

class ChatEnd2EndAndApoPortalRelease extends Component
{
    public Collection $pharmacies;

    public array $selectedPharmacies = [];

    protected $listeners = [FeatureRecommendation::EVENT_BEFORE_SAVE => 'beforeSave'];

    protected $rules = [
        'selectedPharmacies' => ['required', 'array', 'min:1'],
        'selectedPharmacies.*' => ['required', 'integer'],
    ];

    protected $messages = [
        'selectedPharmacies.required' => 'Mindestens eine Apotheke muss ausgewählt sein.',
    ];

    public function mount(): void
    {
        $this->pharmacies = self::getPharmaciesWhereUserCanActivateChat();
        $this->selectedPharmacies = self::getSelectedPharmacies();
    }

    public function beforeSave(): void
    {
        if ($this->pharmacies->count() > 0) {
            $this->validate();
        }

        foreach ($this->pharmacies as $pharmacy) {
            if (collect($this->selectedPharmacies)->contains($pharmacy->id)) {
                $pharmacy->update(['uses_chat' => true, 'uses_patient_chat' => true, 'show_in_apoguide' => true]);
            }
        }

        $this->dispatch(FeatureRecommendation::EVENT_SAVE);
        $this->skipRender();
    }

    public function render(): Factory|View|Application
    {
        return view('livewire.featureRecommendation.chat-end-2-end-and-apo-portal-release');
    }

    public static function getPharmaciesWhereUserCanActivateChat(): Collection
    {
        return user()?->pharmacies()
            ->withFeatureAccess(ChatFeatureAccess::class)
            ->where(fn (Builder $builder) => $builder // @phpstan-ignore-line
                ->where('uses_chat', false)
                ->orWhere('uses_patient_chat', false))
            ->get()
            ->filter(fn (Pharmacy $pharmacy) => user()?->can('activateChat', $pharmacy) && user()?->can('chat', [user(), $pharmacy])) ?? collect();
    }

    public static function getSelectedPharmacies(): array
    {
        return self::getPharmaciesWhereUserCanActivateChat()->where('uses_patient_chat', true)->pluck('id')->toArray();
    }

    public static function showCheckboxGroup(): bool
    {
        return self::getPharmaciesWhereUserCanActivateChat()->count() > 0;
    }

    public static function getButtonText(): ?string
    {
        return self::showCheckboxGroup() ? 'Chat aktivieren' : null;
    }
}
