<?php

namespace App\Livewire\Ngda\WizardSteps;

use App\Livewire\Wizard\WizardStep;

class NotLegitimizedStep extends WizardStep
{
    public string $title = 'Verknüpfung fehlgeschlagen';

    public ?string $subtitle = 'Ihre Apotheke konnte nicht mit Ihrer N-ID verknüpft werden.';

    public ?int $stepNumber = 2;

    public int $totalSteps = 2;

    /**
     * @var string[]
     */
    protected $listeners = ['linking-nid-wizard-modal-closed' => 'closeModal'];

    public function closeModal(): void
    {
        $this->redirect(route('pharmacies.edit', ['pharmacy' => currentPharmacy()]));
    }
}
