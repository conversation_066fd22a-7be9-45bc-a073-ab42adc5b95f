<?php

namespace App;

use App\Enums\IbmRegistrationStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IbmRegistration extends Model
{
    use HasFactory;

    protected $fillable = [
        'pharmacy_id',
        'order_pharmacy_name',
        'order_ibm_id',
        'order_date',
        'status',
        'registered_at',
    ];

    protected $casts = [
        'order_date' => 'date',
        'registered_at' => 'datetime',
    ];

    public function pharmacy()
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function isIbmRegistered()
    {
        return $this->status === IbmRegistrationStatusEnum::REGISTERED;
    }
}
