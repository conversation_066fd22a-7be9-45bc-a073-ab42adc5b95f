<?php

namespace App;

use App\Enums\InvoiceStatusEnum;
use App\Support\Payment\PaymentApi;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Support\Carbon;
use InvalidArgumentException;

/**
 * @deprecated
 */
class Invoice extends Model
{
    use HasFactory;

    protected $guarded = ['order_id', 'invoice_id'];

    /**
     * @deprecated
     */
    protected function statusCode(): Attribute
    {
        // SQLite allows us to store strings in an integer column, so we need to validate the value.
        return Attribute::make(
            set: static function (mixed $value) {
                throw_unless(is_int($value), new InvalidArgumentException('Status code must be an integer.'));

                return $value;
            }
        );
    }

    public static function booted(): void
    {
        static::creating(static function (Invoice $invoice) {
            $invoice->order_id = $invoice->getOrderIdAttribute();
            $invoice->invoice_id = $invoice->getInvoiceIdAttribute();
        });
    }

    /**
     * @deprecated
     */
    public function getOrderIdAttribute(): string
    {
        return $this->attributes['order_id'] ?? $this->generateBillingId('B');
    }

    /**
     * @deprecated
     */
    public function getInvoiceIdAttribute(): string
    {
        return $this->attributes['invoice_id'] ?? $this->generateBillingId('R');
    }

    /**
     * @deprecated
     */
    public function getCompletedAtAttribute(): ?Carbon
    {
        return $this->status_code === InvoiceStatusEnum::COMPLETED->value ? $this->updated_at : null;
    }

    /**
     * @deprecated
     */
    private function generateBillingId(string $prefix): string
    {
        $paymentApi = resolve(PaymentApi::class);

        return $prefix.'-'.sprintf('%05d', self::all()->count() + 1).'-'.now()->year.'-'.$paymentApi->billingAddressIdSuffix();
    }

    /**
     * @deprecated
     */
    public function pharmacies(): HasManyThrough
    {
        return $this->hasManyThrough(
            Pharmacy::class,
            SubscriptionOrder::class,
            'invoice_id',
            'id',
            'invoice_id',
            'orderable_id'
        );
    }

    /**
     * @deprecated
     */
    public function recipient(): HasOneThrough
    {
        return $this->hasOneThrough(
            User::class,
            BillingAddress::class,
            'id',
            'id',
            'billing_address_id',
            'user_id'
        );
    }

    /**
     * @deprecated
     */
    public function subscriptionOrders(): HasMany
    {
        return $this->hasMany(SubscriptionOrder::class);
    }

    /**
     * @deprecated
     */
    public function billingAddress(): BelongsTo
    {
        return $this->belongsTo(BillingAddress::class);
    }

    /**
     * @deprecated
     */
    public function resentInvoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'resent_as_invoice_id');
    }

    /**
     * @deprecated
     */
    public function canceledInvoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'id', 'resent_as_invoice_id');
    }

    /**
     * @deprecated
     */
    public function isCanceled(): bool
    {
        return $this->status_code === InvoiceStatusEnum::CANCELED->value;
    }
}
