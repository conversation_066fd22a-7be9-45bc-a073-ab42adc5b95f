<?php

namespace App\Jobs;

use App\Helper\ApomondoApi;
use App\Pharmacy;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendPharmacyToApomondo implements ShouldBeUniqueUntilProcessing, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private Pharmacy $pharmacy;

    /**
     * The number of seconds after which the job's unique lock will be released.
     *
     * @var int
     */
    public $uniqueFor = 60 * 60;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Pharmacy $pharmacy)
    {
        $this->pharmacy = $pharmacy;
    }

    public function uniqueId()
    {
        return $this->pharmacy->id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        app(ApomondoApi::class)->updatePharmacy($this->pharmacy);
        UpdateCalendarTopicsAtApomondo::dispatch($this->pharmacy);
    }
}
