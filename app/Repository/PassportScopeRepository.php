<?php

namespace App\Repository;

use App\Misc\PassportClient;
use <PERSON><PERSON>\Passport\Bridge\Scope;
use <PERSON><PERSON>\Passport\Bridge\ScopeRepository as BaseScopeRepository;
use <PERSON><PERSON>\Passport\Passport;
use League\OAuth2\Server\Entities\ClientEntityInterface;
use League\OAuth2\Server\Entities\ScopeEntityInterface;

class PassportScopeRepository extends BaseScopeRepository
{
    public function finalizeScopes(array $scopes, $grantType, ClientEntityInterface $clientEntity, $userIdentifier = null)
    {
        $finalizedScopes = [];

        $client = PassportClient::query()->where('id', $clientEntity->getIdentifier())->first();

        //        if(count($scopes) == 0) {
        $finalizedScopes = collect($client->allowed_scopes ?? [])->map(function ($identifier) {
            return new Scope($identifier);
        })->toArray();
        //        } else {
        //            foreach ($scopes as $scope) {
        //                if (in_array($scope->getIdentifier(), ($client->allowed_scopes ?? [])))
        //                    array_push($finalizedScopes, $scope);
        //            }
        //        }

        return $finalizedScopes;
    }

    /**
     * @return ScopeEntityInterface[]
     */
    private function getAllScopes(ClientEntityInterface $clientEntity)
    {
        $scopes = [];
        foreach (Passport::scopes() as $passportScope) {
            array_push($scopes, $this->getScopeEntityByIdentifier($passportScope->id));
        }

        return $scopes;
    }
}
