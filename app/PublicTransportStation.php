<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class PublicTransportStation
 *
 * @mixin IdeHelperPublicTransportStation
 */
class PublicTransportStation extends Model
{
    protected $touches = ['pharmacy'];

    public const STOPS = [
        'TRAINS' => [
            'id' => 1,
            'key' => 'trains',
        ], // includes metro, s-train and train
        'TRAM' => [
            'id' => 2,
            'key' => 'tram',
        ],
        'BUS' => [
            'id' => 3,
            'key' => 'bus',
        ],
    ];

    protected $guarded = [];

    public static function getStopOptions()
    {
        return [
            1 => 'trains',
            2 => 'tram',
            3 => 'bus',
        ];
    }

    public function getStopKey()
    {
        return self::getStopOptions()[$this->type];
    }

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }
}
