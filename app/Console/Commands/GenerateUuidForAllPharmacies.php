<?php

namespace App\Console\Commands;

use App\Pharmacy;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateUuidForAllPharmacies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pharmacies:generate-uuid';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = Pharmacy::query()
            ->whereNull('uuid')
            ->count();

        $x = 0;

        Pharmacy::query()
            ->whereNull('uuid')
            ->chunkById(200, function ($pharmacies) use (&$x, $count) {
                /** @var Pharmacy $pharmacy */
                foreach ($pharmacies as $pharmacy) {
                    $x++;
                    $this->info('Generating UUID for Pharmacy '.$pharmacy->id.', '.$x.'/'.$count);
                    $pharmacy->uuid = Str::uuid();
                    $pharmacy->timestamps = false;

                    $pharmacy->saveQuietly();
                }
            });
    }
}
