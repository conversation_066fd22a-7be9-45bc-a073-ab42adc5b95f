<?php

namespace App\Console\Commands;

use App\User;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class GenerateOrganisationUuidForAllUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:generate-organisation-uuid';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = User::query()
            ->onlyOwner()
            ->count();

        $x = 0;

        User::query()
            ->onlyOwner()
            ->chunkById(200, function ($users) use (&$x, $count) {
                /** @var User $user */
                foreach ($users as $user) {
                    $uuid = Str::uuid();
                    $this->info('Generating UUID for User '.$user->id.', '.$x.'/'.$count);
                    if (! $user->organisation_uuid) {
                        $user->organisation_uuid = $uuid;
                        $user->timestamps = false;
                        $user->saveQuietly();
                    } else {
                        $uuid = $user->organisation_uuid;
                    }

                    foreach ($user->pharmacies as $pharmacy) {
                        foreach ($pharmacy->users as $pharmacyUser) {
                            if ($pharmacyUser->isNot($user)) {
                                $pharmacyUser->organisation_uuid = $uuid;
                                $pharmacyUser->timestamps = false;
                                $pharmacyUser->saveQuietly();
                            }
                        }
                    }
                    $user->saveQuietly();
                }
            });

        User::query()
            ->whereHas('pharmacyProfile', function (Builder $builder) {
                return $builder->whereNotNull('company_user_id');
            })
            ->whereDoesntHave('pharmacies')
            ->whereNull('organisation_uuid')
            ->chunkById(200, function ($users) {
                /** @var User $user */
                foreach ($users as $user) {
                    $owner = $user->pharmacyProfile->companyHeadUser;

                    $user->organisation_uuid = $owner->organisation_uuid;
                    $user->timestamps = false;
                    $user->saveQuietly();
                }
            });
    }
}
