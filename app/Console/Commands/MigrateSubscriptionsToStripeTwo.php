<?php

namespace App\Console\Commands;

use App\Jobs\MigratePharmacySubscriptionToStripeJob;
use App\Pharmacy;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class MigrateSubscriptionsToStripeTwo extends Command
{
    protected $signature = 'subscriptions:migrate-to-stripe {--pharmacy=} {--finalize-now} {--count=}';

    public function handle(): void
    {

        if ($this->option('pharmacy')) {
            $pharmacy = Pharmacy::find($this->option('pharmacy'));
            if ($pharmacy) {
                MigratePharmacySubscriptionToStripeJob::dispatch($pharmacy)->onQueue('default');
            }

            return;
        }

        $query = Pharmacy::query()
            ->whereDoesntHave('subscriptions')
            ->where(function (Builder $query) {
                $query->whereHas('cardLinkOrder')
                    ->orWhereHas('integrations');
            });

        if ($this->option('count')) {
            $query->take($this->option('count'))
                ->get()
                ->each(function ($pharmacy) {
                    $this->info('migrating pharmacy '.$pharmacy->id);

                    return MigratePharmacySubscriptionToStripeJob::dispatch($pharmacy)->onQueue('default');
                });

            return;
        }

        $query->each(function ($pharmacy) {
            $this->info('migrating pharmacy '.$pharmacy->id);

            return MigratePharmacySubscriptionToStripeJob::dispatch($pharmacy)->onQueue('default');
        });
    }
}
