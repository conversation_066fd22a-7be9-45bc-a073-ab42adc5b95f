<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class GeneratePharmaciesTokenKeyCerts extends Command
{
    use HandlesCertificates;

    protected $signature = 'app:generate-pharmacies-token-key-certs';

    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        /** @var string $pharmaciesPrivateKeyPath */
        $pharmaciesPrivateKeyPath = config('services.apomondo.pharmacies_private_key_path');
        /** @var string $pharmaciesPublicKeyPath */
        $pharmaciesPublicKeyPath = config('services.apomondo.pharmacies_public_key_path');

        if (File::exists($pharmaciesPrivateKeyPath)
                && ! $this->confirm('There is already a certificate present. Do you want to overwrite the existing one?', true)) {
            return;
        }

        $this->createCertificate($pharmaciesPrivateKeyPath, $pharmaciesPublicKeyPath);

        $this->info('Neues Zertifikat und privater <PERSON><PERSON><PERSON><PERSON> wurden erfolgreich generiert.');
    }
}
