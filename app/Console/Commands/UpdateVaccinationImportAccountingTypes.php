<?php

namespace App\Console\Commands;

use App\Enums\VaccinationImport\AccountingTypeEnum;
use App\Helper\VaccinationImportHelper;
use App\VaccinationImport;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateVaccinationImportAccountingTypes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vaccination-import:update-accounting-type {--test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update accounting type of vaccination imports depending on X minute time difference.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('test')) {
            $this->info('Test mode active. No changes will be applied.');
        }

        VaccinationImport::query()
            ->where('accounting_type', AccountingTypeEnum::FOLLOW)
            ->whereDate('created_at', '<=', Carbon::make('2021-06-22 11:00:00')) // after this, new entries are directly saved using the new rules
            ->chunkById(200, function ($chunk) {
                VaccinationImportHelper::checkAccountingType($chunk, true, $this->option('test'));
            });
    }
}
