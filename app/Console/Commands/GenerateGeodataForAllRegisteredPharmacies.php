<?php

namespace App\Console\Commands;

use App\Actions\GetGeolocationForPharmacyAction;
use App\Exceptions\LocationNotFoundException;
use App\Pharmacy;
use Illuminate\Console\Command;

class GenerateGeodataForAllRegisteredPharmacies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pharmacy:generate-geodata';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = Pharmacy::query()
            ->whereNull(['latitude', 'longitude'])
            ->count();

        $x = 1;

        $this->error('Dieser Command muss noch auf die neue Adressen geupdated werden!');

        //        Pharmacy::query()
        //            ->whereNull(['latitude', 'longitude'])
        //            ->chunkById(20, function ($chunk) use (&$x, $count) {
        //                /** @var Pharmacy $pharmacy */
        //                foreach ($chunk as $pharmacy) {
        //                    $this->info($x . '/' . $count . ':' . $pharmacy->id);
        //                    $x++;
        //
        //                    try {
        //                        $pharmacy = (new GetGeolocationForPharmacyAction())->execute($pharmacy);
        //                        $pharmacy->timestamps = false;
        //                        $pharmacy->saveQuietly();
        //                    } catch (\Exception | LocationNotFoundException $e) {
        //                        report($e);
        //                        $this->error('Apotheke mit der ID ' . $pharmacy->id . ' hat keine Adresse gefunden...');
        //                    }
        //                }
        //            });
    }
}
