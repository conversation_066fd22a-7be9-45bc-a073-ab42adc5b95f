<?php

namespace App\Console\Commands;

use App\Association;
use App\User;
use App\UserPharmacyProfile;
use Illuminate\Console\Command;

class UserPharmaciesProfileMigration extends Command
{
    /**
     * @var string
     */
    protected $signature = 'users:migrate-association-to-pharmacy-profile';

    /**
     * @var string
     */
    protected $description = 'Copy user->association column to user pharmacy profile table';

    /**
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        User::chunk(50, function ($users) {
            foreach ($users as $user) {
                if ($user->associationProfile !== null || $user->pharmacyProfile !== null) {
                    continue;
                }

                /** @var UserPharmacyProfile $profile */
                UserPharmacyProfile::create([
                    'user_id' => $user->id,
                ]);
                $associationId = $user->association_id;
                if (Association::find($associationId) === null) {
                    $associationId = null;
                }
                $user->refresh();

                if (! ($user->pharmacyProfile instanceof UserPharmacyProfile)) {
                    throw new \RuntimeException('UserPharmacyProfile not found');
                }

                $user->pharmacyProfile->association_id = $associationId;
                $user->pharmacyProfile->save();
            }
        });
    }
}
