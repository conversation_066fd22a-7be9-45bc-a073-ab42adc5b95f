<?php

namespace App\Console\Commands;

use App\Actions\BillingAddresses\CreateBillingAddress;
use App\Pharmacy;
use Illuminate\Console\Command;
use Symfony\Component\Console\Command\Command as CommandAlias;

class CreateDefaultBillingAddresses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment:create-default-billing-addresses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add default billing addresses to all already existing pharmacies';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        Pharmacy::query()->whereNull('billing_address_id')->get()->each(function (Pharmacy $pharmacy) {
            if ($pharmacy->owner()) {
                (new CreateBillingAddress)->firstOrCreateForOrderable($pharmacy);
            } else {
                $this->info('Owner not found for '.$pharmacy->id);
            }
        });

        return CommandAlias::SUCCESS;
    }
}
