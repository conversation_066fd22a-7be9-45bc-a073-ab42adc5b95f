<?php

namespace App\Nova;

use App\Enums\PharmacyAddressTypeEnum;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class PharmacyAddress extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\PharmacyAddress::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [];

    public static function label()
    {
        return 'Apotheken Adressen';
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),

            //            Text::make('type')->readonly(),
            BelongsTo::make('Apotheke', 'pharmacy', Pharmacy::class)->readonly(),

            Select::make('Art', 'type')
                ->options(PharmacyAddressTypeEnum::getForDropdown())
                ->displayUsingLabels()
                ->readonly(),

            Text::make('Adresszusatz', 'optional_address_line')->nullable()->hideFromIndex(),

            Text::make('Straße', 'street')->required(),

            Text::make('Hausnummer', 'house_number')->required(),

            Number::make('PLZ', 'postcode')->rules(['required', 'digits:5']),

            Text::make('Stadt', 'city')->sortable()->required(),

            Text::make('Google Maps Link', function () {
                return '<a target="_blank" class="no-underline dim text-primary font-bold" href="https://www.google.com/maps/search/?api=1&query='.$this->latitude.'%2C'.$this->longitude.'">Ansehen</a>';
            })->asHtml(),

            Number::make('Latitude')->step(0.000001)->resolveUsing(fn ($value) => round($value, 6))->hideFromIndex()->nullable(),

            Number::make('Longitude')->step(0.000001)->resolveUsing(fn ($value) => round($value, 6))->hideFromIndex()->nullable(),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
