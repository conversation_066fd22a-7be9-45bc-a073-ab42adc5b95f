<?php

namespace App\Nova\Filters;

use App\Enums\CardLink\CardLinkOrderStatusEnum;
use Illuminate\Database\Eloquent\Builder;
use <PERSON><PERSON>\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class CardLinkTransmissionStatus extends Filter
{
    /**
     * The displayable name of the filter.
     *
     * @var string
     */
    public $name = 'Übertragungsstatus';

    /**
     * Apply the filter to the given query.
     *
     * @param  Builder  $query
     * @param  mixed  $value
     */
    public function apply(NovaRequest $request, $query, $value): Builder
    {
        $query->where(function ($query) use ($value) {
            if ($value === 'true') {
                $query->where('status', CardLinkOrderStatusEnum::Activating)
                    ->orWhere('status', CardLinkOrderStatusEnum::Activated);
            } else {
                $query->where('status', CardLinkOrderStatusEnum::Reserved)
                    ->orWhere('status', CardLinkOrderStatusEnum::Ordered);
            }
        });

        return $query;
    }

    /**
     * Get the filter's available options.
     *
     * @return array<string, bool>
     */
    public function options(NovaRequest $request): array
    {
        return [
            'nur übertragene Bestellungen' => true,
            'nur nicht-übertragene Bestellungen' => false,
        ];
    }
}
