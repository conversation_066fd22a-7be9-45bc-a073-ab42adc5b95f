<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Currency;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\MorphTo;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class SubscriptionOrder extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\SubscriptionOrder::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    public static $group = 'Mitgliedschaft';

    public static function label()
    {
        return 'Abrechnungen';
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make(__('ID'), 'id')->sortable()->readonly(),

            Text::make('plan')->readonly(),
            Date::make('started_at')->readonly(),
            Date::make('ended_at')->readonly(),
            Currency::make('total_price')->readonly(),
            Boolean::make('is_upgrade')->readonly(),

            MorphTo::make('orderable')->types([
                User::class,
                Pharmacy::class,
            ])->readonly(),

        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
