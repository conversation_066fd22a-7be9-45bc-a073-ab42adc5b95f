<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Card;
use <PERSON><PERSON>\Nova\Fields\Field;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Filters\Filter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Lenses\Lens;

class InstitutionIdentifier extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\InstitutionIdentifier::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'identifier';

    /**
     * The columns that should be searched.
     *
     * @var array<string>
     */
    public static $search = [
        'id',
        'identifier',
        'name',
    ];

    public static $displayInNavigation = false;

    public static function label()
    {
        return 'IK-Nummer';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Krankenkasse', 'name')->sortable(),

            Text::make('IK-Nummer', 'identifier')->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array<Card>
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the request.
     *
     * @return array<Filter>
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the request.
     *
     * @return array<Lens>
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the request.
     *
     * @return array<Action>
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
