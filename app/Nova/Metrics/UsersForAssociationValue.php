<?php

namespace App\Nova\Metrics;

use App\Domains\Subscription\Application\FeatureAccess\BaseFeatureAccess;
use App\User;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Value;

class UsersForAssociationValue extends Value
{
    private $association;

    public function __construct($association = null)
    {
        parent::__construct();
        $this->association = $association;
    }

    /**
     * Get the displayable name of the metric
     *
     * @return string
     */
    public function name()
    {
        return 'Neue Nutzer';
    }

    /**
     * Calculate the value of the metric.
     *
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $association = $this->association;
        $query = User::query()
            ->whereHas('pharmacies', function ($query) use ($association) {
                $pharmacyQuery = $query
                    ->withFeatureAccess(BaseFeatureAccess::class);

                if ($association === 'all_association_member') {
                    return $pharmacyQuery->whereNotNull('association_id');
                } elseif ($association === 'not_in_association') {
                    return $pharmacyQuery->whereNull('association_id');
                } elseif (! $association || $association === 'all') {
                    return $pharmacyQuery;
                } elseif ($association && $this->association !== 'all') {
                    return $pharmacyQuery->where('association_id', $association);
                }
            })
            ->orWhereHas('pharmacyProfile', function ($query) use ($association) {
                if ($association === 'all_association_member') {
                    return $query->whereNotNull('association_id');
                } elseif ($association === 'not_in_association') {
                    return $query->whereNull('association_id');
                } elseif (! $association || $association === 'all') {
                    return $query;
                } else {
                    return $query->where('association_id', $association);
                }
            });

        return $this->count($request, $query);
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            10 => '10 Tage',
            30 => '30 Tage',
            60 => '60 Tage',
            'ALL' => 'Seit Beginn',
        ];
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'users-for-association-value-'.($this->association ?: '');
    }
}
