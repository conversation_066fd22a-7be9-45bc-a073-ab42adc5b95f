<?php

namespace App\Nova\Metrics;

use App\Domains\Subscription\Application\FeatureAccess\ChatFeatureAccess;
use App\Pharmacy;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Partition;

class UsesChat extends Partition
{
    private $association = null;

    public function __construct($association = null)
    {
        parent::__construct();
        $this->association = $association;
    }

    /**
     * Get the displayable name of the metric
     *
     * @return string
     */
    public function name()
    {
        return 'Apotheke nutzt Chat';
    }

    /**
     * Calculate the value of the metric.
     *
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $query = Pharmacy::query();

        if ($this->association) {
            $query = $query
                ->withFeatureAccess(ChatFeatureAccess::class);

            if ($this->association === 'all_association_member') {
                $query = $query->whereNotNull('association_id');
            } elseif ($this->association === 'not_in_association') {
                $query = $query->whereNull('association_id');
            } elseif ($this->association !== 'all') {
                $query = $query->where('association_id', $this->association);
            }
        }

        $query2 = clone $query;

        $countYes = $query
            ->where('uses_chat', true)
            ->count();

        $countNo = $query2
            ->where('uses_chat', false)
            ->count();

        return $this->result([
            'Ja' => $countYes,
            'Nein' => $countNo,
        ]);
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'uses-chat-'.($this->association ?: '');
    }
}
