<?php

namespace App\Nova;

use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class UserPharmacyProfile extends Resource
{
    /** @var string */
    public static $model = \App\UserPharmacyProfile::class;

    public static $relatableSearchResults = 10;

    /** @var string */
    public static $title = 'user_id';

    /** @var string[] */
    public static $search = [
        'user_id',
    ];

    public static $group = 'Nutzerverwaltung';

    public static function label()
    {
        return 'Apotheken Nutzerprofile';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            BelongsTo::make('User')->searchable()->sortable()->hideWhenUpdating(),
            BelongsTo::make('User')->searchable()->sortable()->onlyOnForms()->readonly()->hideWhenCreating(),
            // two times because readonly can't be set as readonly for selected forms

            BelongsTo::make('Association')->sortable()->readonly(),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [];
    }

    public function filters(NovaRequest $request): array
    {
        return [];
    }

    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
