<?php

namespace App\Nova\Actions;

use App\CalendarTopic;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\ActionResponse;
use <PERSON>vel\Nova\Fields\ActionFields;

class ChangePublicStateCalendarTopic extends Action
{
    public $name = 'Wechsle Öffentlich-Status';

    public function handle(ActionFields $fields, Collection $models): ActionResponse|Action
    {
        $failedModels = collect();

        /** @var CalendarTopic $calendarTopic */
        foreach ($models as $calendarTopic) {
            if ($calendarTopic->pharmacies()->count() > 0) {
                $failedModels->push($calendarTopic);

                continue;
            }

            $calendarTopic->isPublic() ? $calendarTopic->deactivate() : $calendarTopic->activate();
        }

        if ($failedModels->isNotEmpty()) {
            return Action::danger(
                'Folgende Vorgänge konnten nicht ausgeführt werden,'
                .' da Apotheken bereits mit diesen verknüpft sind: '
                .$failedModels->pluck('id')->implode(', ')
            );
        }

        return Action::message('Der Vorgang war erfolgreich.');
    }
}
