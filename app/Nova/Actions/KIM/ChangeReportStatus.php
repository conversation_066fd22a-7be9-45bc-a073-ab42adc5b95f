<?php

namespace App\Nova\Actions\KIM;

use App\Enums\KimAddressReportStatus;
use App\KimAddress;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\ActionResponse;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;
use TheSeer\Tokenizer\Exception;

class ChangeReportStatus extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Übertragungsstatus ändern';

    /**
     * @param  Collection<int, KimAddress>  $models
     */
    public function handle(ActionFields $fields, Collection $models): ActionResponse|Action
    {
        $reportStatus = $fields->get('report_status');
        assert(is_string($reportStatus));

        $reportedAt = match ($reportStatus) {
            KimAddressReportStatus::NOT_REPORTED->value => null,
            default => now(),
        };

        if (in_array($reportStatus, [KimAddressReportStatus::NOT_REPORTED->value, KimAddressReportStatus::REPORTED->value], true) === false) {
            return Action::danger(sprintf('Ein ungültiger Übertragungsstatus [%s] wurde ausgewählt.', $reportStatus));
        }

        try {
            foreach ($models as $model) {
                $model->update([
                    'report_status' => $reportStatus,
                    'reported_at' => $reportedAt,
                ]);
            }

            return Action::message('Die Aktion war erfolgreich.')->withRedirect('/nova/resources/kim-addresses');
        } catch (Exception $exception) {
            return Action::danger('Die KIM-Adressen konnten nicht geändert werden.');
        }
    }

    /** @return array<int, Field> */
    public function fields(NovaRequest $request): array
    {
        return [
            Select::make('Übertragungsstatus', 'report_status')
                ->options([
                    KimAddressReportStatus::NOT_REPORTED->value => KimAddressReportStatus::NOT_REPORTED->readable(),
                    KimAddressReportStatus::REPORTED->value => KimAddressReportStatus::REPORTED->readable(),
                ])
                ->required(),
        ];
    }
}
