<?php

namespace App\Nova\Actions;

use App\KimAddress;
use App\Pharmacy;
use App\Support\MoveKimAddress\MoveKimAddressFactory;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class MoveKimAddress extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models): ActionResponse|Action
    {
        $kimAddress = $models->sole();

        assert($kimAddress instanceof KimAddress);

        /** @phpstan-ignore-next-line */
        $targetPharmacy = Pharmacy::query()->findOrFail($fields->targetPharmacyId);

        assert($targetPharmacy instanceof Pharmacy);

        if ($targetPharmacy->id === $kimAddress->pharmacy?->id) {
            return Action::danger('Die Ziel-Apotheke ist bereits die aktuelle Apotheke der KIM-Adresse.');
        }

        $moveKimAddress = MoveKimAddressFactory::create($kimAddress, $targetPharmacy);

        $moveKimAddress->process();

        return Action::message('Die KIM-Adresse wird umgezogen.');
    }

    /**
     * @return array<Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Text::make('ID der Ziel-Apotheke', 'targetPharmacyId')
                ->rules('required'),
        ];
    }
}
