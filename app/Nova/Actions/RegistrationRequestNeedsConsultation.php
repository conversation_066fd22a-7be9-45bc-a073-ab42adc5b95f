<?php

namespace App\Nova\Actions;

use App\Enums\RegistrationRequestStatusEnum;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class RegistrationRequestNeedsConsultation extends Action
{
    public $name = '<PERSON><PERSON><PERSON><PERSON><PERSON>ti<PERSON>';

    /** @phpstan-ignore-next-line */
    public function handle(ActionFields $fields, Collection $models): mixed
    {
        if ($models->isEmpty()) {
            return Action::danger('Es wurden keine Registrierungsanfragen markiert.');
        }

        DB::transaction(function () use ($models) {
            $models->each(function ($model) {
                $model->status = RegistrationRequestStatusEnum::CONSULTATION;
                $model->save();
            });
        });

        return Action::message('Die markierten Registrierungsanfragen wurden erfolgreich auf benötigte Konsultation gesetzt.');
    }
}
