<?php

namespace App\Nova\Dashboards;

use App\Nova\Metrics\NewApomails;
use App\Nova\Metrics\TotalApomailsByStatus;
use App\Nova\Metrics\TotalApomailsForIDPByStatus;
use App\Nova\Metrics\TotalApomailsNotForIDPByStatus;
use <PERSON>vel\Nova\Dashboard;

class ApomailDashboard extends Dashboard
{
    public function label()
    {
        return 'ApoMail';
    }

    /**
     * Get the cards for the dashboard.
     *
     * @return array
     */
    public function cards()
    {
        return [
            (new TotalApomailsByStatus),
            (new TotalApomailsForIDPByStatus),
            (new TotalApomailsNotForIDPByStatus),
            (new NewApomails),
        ];
    }

    /**
     * Get the URI key for the dashboard.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'apomail-dashboard';
    }
}
