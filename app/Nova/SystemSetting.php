<?php

namespace App\Nova;

use App\Enums\StaffRoleEnum;
use App\Nova\Actions\ChangeSystemSetting;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Fields\Field;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class SystemSetting extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\SystemSetting::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array<string>
     */
    public static $search = [
        'name',
        'group',
    ];

    public static function label()
    {
        return 'Systemeinstellungen';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<Field>
     */
    public function fields(Request $request): array
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),

            Text::make('Gruppe', 'group')
                ->readonly()
                ->sortable(),

            Text::make('Variable', 'name')
                ->readonly()
                ->sortable(),

            Text::make('Wert', 'payload')
                ->readonly()
                ->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array<empty>
     */
    public function cards(Request $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array<empty>
     */
    public function filters(Request $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array<empty>
     */
    public function lenses(Request $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array<int, Action>
     */
    public function actions(NovaRequest $request): array
    {
        return [
            (new ChangeSystemSetting)
                ->canSee(fn () => auth('staff')->user()?->hasRole(StaffRoleEnum::ADMIN) ?? false)
                ->canRun(fn () => auth('staff')->user()?->hasRole(StaffRoleEnum::ADMIN) ?? false)
                ->onlyOnDetail(),
        ];
    }

    /**
     * Determine if the current user can create new resources.
     */
    public static function authorizedToCreate(Request $request): bool
    {
        return false;
    }

    public function authorizedToReplicate(Request $request): bool
    {
        return false;
    }

    /**
     * Determine if the current user can update the given resource.
     */
    public function authorizedToUpdate(Request $request): bool
    {
        // must be false to prevent staff from editing the settings without the action
        return false;
    }

    /**
     * Determine if the current user can delete the given resource.
     *
     * @return bool
     */
    public function authorizedToDelete(Request $request)
    {
        return false;
    }
}
