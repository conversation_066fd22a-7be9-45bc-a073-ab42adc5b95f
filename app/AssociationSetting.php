<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class AssociationSetting
 *
 * @mixin IdeHelperAssociationSetting
 */
class AssociationSetting extends Model
{
    protected $primaryKey = 'association_id';

    public $incrementing = false;

    protected $fillable = [
        'association_id',
        'can_vaccinate',
    ];

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }
}
