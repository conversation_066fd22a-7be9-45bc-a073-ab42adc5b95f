<?php

namespace App\Listeners\Pharmacy;

use App\Actions\DocSpaces\RemoveUsersFromAllDocSpaceGroupsAction;
use App\Events\Settings\PharmacyPrivacyPolicyDeclined;
use App\Events\Settings\PharmacyTermsOfUseDeclined;

class RemoveUsersFromAllDocSpaceGroupsListener
{
    public function handle(PharmacyTermsOfUseDeclined|PharmacyPrivacyPolicyDeclined $event): void
    {
        app(RemoveUsersFromAllDocSpaceGroupsAction::class)->setPharmacy($event->setting->settingable)->execute();
    }
}
