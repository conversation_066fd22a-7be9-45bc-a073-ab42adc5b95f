<?php

namespace App\Listeners\Pharmacy;

use App\Events\PharmacyAfterCreatedOrUpdatedEvent;
use App\Helper\IaHelper;
use App\Jobs\PushPharmacyDataToIaJob;
use Exception;

class PushPharmacyDataToIA
{
    protected ?string $token = null;

    /**
     * Create the event listener.
     *
     * @throws Exception
     */
    public function __construct()
    {
        $this->token = IaHelper::getUpdatePharmacyDataApiKey();
    }

    /**
     * Handle the event.
     *
     * @throws Exception
     */
    public function handle(PharmacyAfterCreatedOrUpdatedEvent $event): void
    {
        $pharmacy = $event->pharmacy;

        if (! $pharmacy->hasIaEnabled()) {
            return;
        }

        PushPharmacyDataToIaJob::dispatch($event);
    }
}
