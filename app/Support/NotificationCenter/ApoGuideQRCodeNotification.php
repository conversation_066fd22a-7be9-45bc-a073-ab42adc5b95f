<?php

namespace App\Support\NotificationCenter;

use App\ClosedNotification;
use App\Pharmacy;
use App\User;
use Illuminate\Support\Collection;

class ApoGuideQRCodeNotification extends Notification
{
    protected function __construct(protected User $user, protected Pharmacy $pharmacy)
    {
        parent::__construct();
    }

    public static function close(Pharmacy $pharmacy): void
    {
        $user = user();

        assert($user instanceof User);

        (new self($user, $pharmacy))->markAsClosed();
    }

    public function getKey(): string
    {
        return md5(self::class.'-'.$this->user->id.'-'.$this->pharmacy->id);
    }

    public function toLivewire(): array
    {
        return [
            'userId' => $this->user->id,
            'pharmacyId' => $this->pharmacy->id,
        ];
    }

    public static function fromLivewire(mixed $value): self
    {
        /** @var array<mixed> $value */
        /** @var User $user */
        $user = User::find($value['userId']);
        /** @var Pharmacy $pharmacy */
        $pharmacy = Pharmacy::find($value['pharmacyId']);

        return new self(
            $user,
            $pharmacy
        );
    }

    public function markAsClosed(): void
    {
        $this->closeNotification($this->pharmacy->getMorphClass(), (int) $this->pharmacy->id);
    }

    public function isVisible(): bool
    {
        if ($this->user->cannot('downloadApoguideQrCode', $this->pharmacy)) {
            return false;
        }

        if ($this->closedNotifications->count()) {
            if ($this->closedNotifications->contains(function (ClosedNotification $closedNotification) {
                return $closedNotification->type === self::class
                    && $closedNotification->closeable_type === $this->pharmacy->getMorphClass()
                    && $closedNotification->closeable_id === $this->pharmacy->id;
            })) {
                return false;
            }
        }

        return true;
    }

    public function getTitle(): string
    {
        return sprintf(
            'NEU: JETZT IHRE APOTHEKE ALS FAVORITEN IN DER APOGUIDE APP HINTERLEGEN! (%s)',
            $this->pharmacy->name
        );
    }

    /**
     * @return array<string>
     */
    public function getContent(): array
    {
        return ['Laden Sie sich Ihr kostenfreies ApoGuide Plakat mit individuellem QR-Code für Ihre Apotheke herunter. Nach Scan des QR-Codes wird Ihre Apotheke automatisch in der ApoGuide App der Kunden als Favorit hinterlegt. Einfach Plakat ausdrucken, in der Apotheke aufhängen und loslegen! Sie möchten den individuellen QR-Code auf eigenen Werbemitteln drucken oder auf Ihrer Website nutzen? Kein Problem! Der freigestellte QR-Code ist ebenso als Einzeldatei verfügbar.'];
    }

    public static function getNotifications(): Collection
    {
        $user = user();
        if (! $user instanceof User) {
            return collect();
        }

        $notifications = collect();

        foreach ($user->pharmacies as $pharmacy) {
            $notification = new self($user, $pharmacy);

            if ($notification->isVisible()) {
                $notifications->push($notification);
            }
        }

        return $notifications;
    }

    public function getUrl(): string
    {
        return route('pharmacies.edit', $this->pharmacy).'#apoguide_poster';
    }

    public function getLinkText(): string
    {
        return 'Hier geht’s zu den Werbemitteln!';
    }
}
