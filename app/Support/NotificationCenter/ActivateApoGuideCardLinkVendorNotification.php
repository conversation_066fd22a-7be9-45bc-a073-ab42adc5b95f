<?php

namespace App\Support\NotificationCenter;

use App\ClosedNotification;
use App\Data\CardLink\OrderInformationData;
use App\Enums\FeatureScope;
use App\Features\CardLink;
use App\Pharmacy;
use App\User;
use Illuminate\Support\Collection;
use Laravel\Pennant\Feature;

final class ActivateApoGuideCardLinkVendorNotification extends Notification
{
    protected function __construct(protected User $user, protected Pharmacy $pharmacy)
    {
        parent::__construct();
    }

    public function isVisible(): bool
    {
        if (Feature::for(FeatureScope::App->value)->inactive(CardLink::class)) {
            return false;
        }

        if (Feature::for($this->pharmacy)->inactive(CardLink::class)) {
            return false;
        }

        if ($this->user->isOwner() === false && $this->user->isSubOwner() === false) {
            return false;
        }

        if (! $this->pharmacy->cardLinkOrder) {
            return false;
        }

        $orderInformation = OrderInformationData::fromCardLinkOrder($this->pharmacy->cardLinkOrder);

        if ($orderInformation === null || $orderInformation->activateApoGuideVendor) {
            return false;
        }

        return $this->closedNotifications->doesntContain(function (ClosedNotification $closedNotification) {
            return $closedNotification->type === self::class
                && $closedNotification->closeable_type === $this->user->getMorphClass()
                && $closedNotification->closeable_id === $this->user->id;
        });
    }

    public static function getNotifications(): Collection
    {
        $user = user();

        if (! $user instanceof User) {
            return collect();
        }

        $notifications = collect();

        foreach ($user->pharmacies as $pharmacy) {
            $notification = new self($user, $pharmacy);

            if ($notification->isVisible()) {
                $notifications->push($notification);
            }
        }

        return $notifications;
    }

    public function getTitle(): string
    {
        return sprintf('Empfehlung: ApoGuide als CardLink-Kanal aktivieren (%s)', $this->pharmacy->name);
    }

    /**
     * @return array<string>
     */
    public function getContent(): array
    {
        return [
            'Nutzen Sie ApoGuide als CardLink-Kanal und profitieren Sie von der Reichweite durch ApoGuide.',
            'Um ApoGuide zu aktivieren, klicken Sie auf "Jetzt ApoGuide für CardLink aktivieren" und aktivieren Sie ApoGuide für Ihre Apotheke.',
        ];
    }

    public function getLinkText(): string
    {
        return 'Jetzt ApoGuide für CardLink aktivieren';
    }

    public function getUrl(): string
    {
        return route('card-link', $this->pharmacy);
    }

    /**
     * @return array<string, mixed>
     */
    public function toLivewire(): array
    {
        return [
            'userId' => $this->user->id,
            'pharmacyId' => $this->pharmacy->id,
        ];
    }

    public static function fromLivewire(mixed $value): self
    {
        /** @var array<mixed> $value */
        /** @var User $user */
        $user = User::find($value['userId']);

        /** @var Pharmacy $pharmacy */
        $pharmacy = Pharmacy::find($value['pharmacyId']);

        return new self($user, $pharmacy);
    }

    public function markAsClosed(): void
    {
        $this->closeNotification($this->user->getMorphClass(), $this->user->id);
    }

    public function getKey(): string
    {
        return md5(self::class.'-'.$this->user->id.'-'.$this->pharmacy->id);
    }
}
