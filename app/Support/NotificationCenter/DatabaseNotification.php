<?php

namespace App\Support\NotificationCenter;

use App\DatabaseNotification as DatabaseNotificationModel;
use App\User;
use Illuminate\Support\Collection;

final class DatabaseNotification extends Notification
{
    protected function __construct(
        public DatabaseNotificationModel $databaseNotificationModel
    ) {
        parent::__construct();
    }

    public function getKey(): string
    {
        return md5(self::class.'-'.$this->databaseNotificationModel->id);
    }

    public function isVisible(): bool
    {
        return true;
    }

    public static function getNotifications(): Collection
    {
        $user = user();
        if (! $user instanceof User) {
            return collect();
        }

        $notifications = collect();

        foreach ($user->unreadNotifications as $databaseNotificationModel) {
            $notification = new self($databaseNotificationModel);

            if ($notification->isVisible()) {
                $notifications->push($notification);
            }
        }

        return $notifications;
    }

    public function getTitle(): string
    {
        return $this->databaseNotificationModel->data['title'];
    }

    /**
     * @return array<string>
     */
    public function getContent(): array
    {
        if (! is_array($this->databaseNotificationModel->data['content'])) {
            return [$this->databaseNotificationModel->data['content']];
        }

        return $this->databaseNotificationModel->data['content'];
    }

    public function getLinkText(): string
    {
        return $this->databaseNotificationModel->data['linkText'] ?? '';
    }

    public function getUrl(): string
    {
        return $this->databaseNotificationModel->data['url'] ?? '';
    }

    public function getTime(): int
    {
        return $this->databaseNotificationModel->data['time'] ?? 0;
    }

    public function toLivewire(): array
    {
        return [
            'databaseNotificationModelId' => $this->databaseNotificationModel->id,
        ];
    }

    public static function fromLivewire(mixed $value): self
    {
        /** @var array<mixed> $value */
        /** @var DatabaseNotificationModel $databaseNotificationModel */
        $databaseNotificationModel = DatabaseNotificationModel::find($value['databaseNotificationModelId']);

        return new self(
            $databaseNotificationModel,
        );
    }

    public function markAsClosed(): void
    {
        $this->databaseNotificationModel->markAsRead();
    }

    public function forwardTo(): string
    {
        $this->markAsClosed();

        return $this->getUrl();
    }
}
