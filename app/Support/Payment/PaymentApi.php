<?php

namespace App\Support\Payment;

use App\BillingAddress;
use App\Enums\InvoiceStatusEnum;
use App\Invoice;
use App\SubscriptionOrder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use Throwable;

abstract class PaymentApi
{
    abstract public function billingAddressIdSuffix(): string;

    /**
     * @throws Throwable
     */
    public function createAndSendInvoice(Collection $subscriptionOrders): Invoice
    {
        if ($subscriptionOrders->isEmpty()) {
            throw new InvalidArgumentException('$subscriptionOrders must not be empty.');
        }

        $orderable = $subscriptionOrders->first()->orderable;

        if (! $orderable) {
            $ids = $subscriptionOrders->pluck('id')->implode(', ');

            throw new InvalidArgumentException(
                "subscriptionOrders with IDs [$ids] are linked to non existant orderables."
            );
        }

        $billingAddress = $orderable->billingAddress;

        if (! $subscriptionOrders->every(
            fn (SubscriptionOrder $subscriptionOrder) => $subscriptionOrder->orderable->billingAddress->is($billingAddress)
        )) {
            throw new InvalidArgumentException(
                'Billing addresses must be the same across all orders in one invoice.'
            );
        }

        return $this->createAndSendInvoiceTransaction($subscriptionOrders, $billingAddress);
    }

    /**
     * @throws Throwable
     */
    public function replaceAndSendInvoice(
        Invoice $invoiceToReplace,
        BillingAddress $billingAddress
    ): Invoice {
        $subscriptionOrders = $this->duplicateSubscriptionOrders($invoiceToReplace);

        $invoice = $this->createAndSendInvoiceTransaction($subscriptionOrders, $billingAddress);

        $invoiceToReplace->update([
            'resent_as_invoice_id' => $invoice->id,
        ]);

        return $invoice;
    }

    /**
     * Returns transaction_id and status_code
     *
     * @return array{string, string}
     */
    abstract public function sendInvoice(Invoice $invoice): array;

    abstract public function reverseInvoice(Invoice $invoice): void;

    abstract public function processPostback(IPaymentPostbackRequest $request): void;

    private function createAndSendInvoiceTransaction(
        Collection $subscriptionOrders,
        BillingAddress $billingAddress
    ): Invoice {
        DB::beginTransaction();

        try {
            $invoice = new Invoice;
            $invoice->transaction_id = 'placeholder';
            $invoice->status_code = InvoiceStatusEnum::CREATED->value;
            $invoice->billingAddress()->associate($billingAddress);
            $invoice->save();
            $invoice->subscriptionOrders()->saveMany($subscriptionOrders);
            $subscriptionOrders->each(
                fn (SubscriptionOrder $subscriptionOrder) => $subscriptionOrder->update(['processed_at' => now()])
            );

            [$transactionId, $statusCode] = $this->sendInvoice($invoice);

            $invoice->status_code = $statusCode;
            $invoice->transaction_id = $transactionId;
            $invoice->save();

            DB::commit();

            return $invoice;
        } catch (Throwable $e) {
            DB::rollBack();

            throw $e;
        }
    }

    private function duplicateSubscriptionOrders(Invoice $invoice): Collection
    {
        return $invoice->subscriptionOrders->map(
            function (SubscriptionOrder $subscriptionOrder) {
                $newSubscriptionOrder = $subscriptionOrder->replicate([
                    'processed_at',
                    'invoice_id',
                ]);
                $newSubscriptionOrder->save();

                return $newSubscriptionOrder;
            }
        );
    }
}
