<?php

namespace App\Features;

use App\Contracts\PennantFeature;
use App\Settings\ShiftPlanSettings;
use App\Staff;
use App\User;

class ShiftPlan implements PennantFeature
{
    public function before(Staff|User $user): ?bool
    {
        if (! $user instanceof User) {
            return false;
        }

        if (ShiftPlanSettings::enabled()) {
            return true;
        }

        if (ShiftPlanSettings::betaEnabled() && $user->isShiftPlanBetaUser()) {
            return true;
        }

        if (ShiftPlanSettings::friendlyEnabled() && $user->isBetaTester()) {
            return true;
        }

        return false;
    }

    public function resolve(Staff|User $user): bool
    {
        return true;
    }
}
