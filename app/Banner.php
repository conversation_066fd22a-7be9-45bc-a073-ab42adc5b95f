<?php

namespace App;

use App\Enums\BannerPosition;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Banner extends Model
{
    use HasFactory;

    protected $guarded = [
        'id',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'activated_at' => 'datetime',
        'position' => BannerPosition::class,
    ];

    public function scopeActive(Builder $query): void
    {
        $query->where('active', true);
    }

    public function scopePublished(Builder $query): void
    {
        $now = now('Europe/Berlin');

        $query
            ->where(
                fn (Builder $query) => $query->whereNull('starts_at')->orWhere('starts_at', '<=', $now)
            )
            ->where(
                fn (Builder $query) => $query->whereNull('ends_at')->orWhere('ends_at', '>=', $now)
            );
    }

    public function scopeBottom(Builder $query): void
    {
        $query->where('position', BannerPosition::BOTTOM);
    }

    public function scopeTop(Builder $query): void
    {
        $query->where('position', BannerPosition::TOP);
    }
}
