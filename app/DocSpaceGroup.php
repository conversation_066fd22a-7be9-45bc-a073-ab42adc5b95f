<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class DocSpaceGroup extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function docSpaces(): BelongsToMany
    {
        return $this->belongsToMany(DocSpace::class)->using(DocSpaceDocSpaceGroup::class);
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class)->using(DocSpaceGroupUser::class);
    }

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function getFullNameAttribute(): string
    {
        if (! empty($this->suffix)) {
            return $this->name.' ['.$this->suffix.']';
        }

        return $this->name;
    }
}
