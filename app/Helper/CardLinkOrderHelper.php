<?php

namespace App\Helper;

use App\CardLinkOrder;
use App\Data\CardLink\CardLinkSettingsData;
use App\Data\CardLink\ChannelData;
use App\Data\CardLink\OrderInformationData;
use App\Data\CardLink\TransactionUsageData;
use App\Enums\CardLink\CardLinkOrderStatusEnum;
use App\Enums\CardLink\CardLinkPackageEnum;
use App\Enums\FeatureScope;
use App\Features\CardLink;
use App\Http\Integrations\CardLinkService\CardLinkServiceConnector;
use App\Http\Integrations\CardLinkService\Requests\GetCardLinkSettingsRequest;
use App\Http\Integrations\CardLinkService\Requests\GetChannelsRequest;
use App\Http\Integrations\CardLinkService\Requests\GetTransactionUsageRequest;
use App\Pharmacy;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use JsonException;
use Laravel\Pennant\Feature;
use RuntimeException;
use Saloon\Contracts\Authenticator;
use Saloon\Exceptions\Request\FatalRequestException;
use Saloon\Exceptions\Request\RequestException;
use Throwable;

readonly class CardLinkOrderHelper
{
    public static function package(CardLinkOrder $cardLinkOrder): ?CardLinkPackageEnum
    {
        return self::orderInformation($cardLinkOrder)?->package;
    }

    public static function packageName(CardLinkOrder $cardLinkOrder): ?string
    {
        return self::package($cardLinkOrder)?->label();
    }

    public static function contractEndsAt(CardLinkOrder $cardLinkOrder): ?Carbon
    {
        // TODO: (AP-2512) – Wie wird das Vertragsende in Zukunft ermittelt?
        return null;
    }

    public static function channels(CardLinkOrder $cardLinkOrder): ?Collection
    {
        try {
            if ($cardLinkOrder->pharmacy === null) {
                return null;
            }

            $orderInformationData = self::orderInformation($cardLinkOrder);

            if ($orderInformationData?->transmittedAt === null) {
                return null;
            }

            $connector = new CardLinkServiceConnector;

            $authenticator = $connector->getAccessTokenCached();
            assert($authenticator instanceof Authenticator);

            $connector->authenticate($authenticator);

            $response = $connector->send(new GetChannelsRequest($cardLinkOrder->pharmacy));

            if ($response->ok() === false) {
                report(new RuntimeException('Kanäle können nicht vom CardLink-Service abgerufen werden.', previous: $response->toException()));

                return null;
            }

            return $response->collect('channels')->map(fn (array $channel) => ChannelData::from($channel)); // @phpstan-ignore-line
        } catch (Throwable $e) {
            report($e);

            return null;
        }
    }

    public static function usage(CardLinkOrder $cardLinkOrder, ?Carbon $startsAt = null, ?Carbon $endsAt = null): ?TransactionUsageData
    {
        try {
            $connector = new CardLinkServiceConnector;

            $authenticator = $connector->getAccessTokenCached();
            assert($authenticator instanceof Authenticator);

            $connector->authenticate($authenticator);

            $request = new GetTransactionUsageRequest($cardLinkOrder, $startsAt, $endsAt);
            $response = $connector->send($request);

            if ($response->ok() === false) {
                report(
                    new RuntimeException(
                        sprintf('Transaktionsverbrauch der Apotheke [%s] kann nicht vom CardLink-Service abgerufen werden.', $cardLinkOrder->pharmacy?->name),
                        previous: $response->toException()
                    )
                );

                return null;
            }

            return TransactionUsageData::from($response->json());
        } catch (\Throwable $e) {
            report($e);

            return null;
        }
    }

    /**
     * @throws Throwable
     * @throws FatalRequestException
     * @throws RequestException
     * @throws JsonException
     */
    public static function settings(?Pharmacy $pharmacy, bool $throwOnError = false): ?CardLinkSettingsData
    {
        try {
            if ($pharmacy === null) {
                return null;
            }

            if ($pharmacy->cardLinkOrder === null) {
                return null;
            }

            $orderInformationData = self::orderInformation($pharmacy->cardLinkOrder);

            if ($orderInformationData?->transmittedAt === null) {
                return null;
            }

            $connector = new CardLinkServiceConnector;

            $authenticator = $connector->getAccessTokenCached();
            assert($authenticator instanceof Authenticator);

            $connector->authenticate($authenticator);

            $request = new GetCardLinkSettingsRequest($pharmacy);
            $response = $connector->send($request);

            if ($response->ok() === false) {
                $exception = new RuntimeException(
                    sprintf('Settings der Apotheke [%s] kann nicht vom CardLink-Service abgerufen werden.', $pharmacy),
                    previous: $response->toException()
                );
                report(
                    $exception
                );
                if ($throwOnError) {
                    throw $exception;
                }

                return null;
            }

            return CardLinkSettingsData::from($response->json());
        } catch (\Throwable $e) {
            report($e);

            if ($throwOnError) {
                throw $e;
            }

            return null;
        }
    }

    public static function orderInformation(CardLinkOrder $cardLinkOrder): ?OrderInformationData
    {
        return OrderInformationData::fromCardLinkOrder($cardLinkOrder);
    }

    public static function isPartner(Pharmacy $pharmacy): bool
    {
        return $pharmacy->cardLinkPartnerPharmacy()->exists();
    }

    public static function canReserve(Pharmacy $pharmacy): bool
    {
        if (Feature::for(FeatureScope::App->value)->inactive(CardLink::class)) {
            return false;
        }

        return Feature::for($pharmacy)->inactive(CardLink::class);
    }

    public static function canOrder(Pharmacy $pharmacy): bool
    {
        if (Feature::for($pharmacy)->inactive(\App\Features\CardLink::class)) {
            return false;
        }

        if ($pharmacy->cardLinkOrder && $pharmacy->cardLinkOrder->status !== CardLinkOrderStatusEnum::Reserved) {
            return false;
        }

        return true;
    }
}
