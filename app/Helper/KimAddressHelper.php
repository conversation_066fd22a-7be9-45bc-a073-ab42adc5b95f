<?php

namespace App\Helper;

use App\Enums\KimAddressStatus;
use App\Enums\KimVendorEnum;
use App\Enums\PharmacyRoleEnum;
use App\KimAddress;
use App\Pharmacy;
use App\Settings\KimSettings;
use App\User;

class KimAddressHelper
{
    public const LOCAL_PART_REGEX = '[a-zA-Z0-9\\.\\-\\_]{1,64}';

    public static function bookingAvailable(Pharmacy $pharmacy): bool
    {
        if (! app(KimSettings::class)->kim_booking_active) {
            return false;
        }

        if (app(KimSettings::class)->kim_booking_limit === null) {
            return true;
        }

        // owner has already kim addresses that has been activated
        if ($pharmacy->owner() && $pharmacy->owner()->pharmacies()->whereHas(
            'kimAddresses',
            fn ($q) => $q->where('status', '!=', KimAddressStatus::RESERVED)
        )->count() !== 0) {
            return true;
        }

        $count = User::query()->whereHas('pharmacies', function ($q) {
            $q
                ->where('role_name', PharmacyRoleEnum::OWNER)
                ->whereHas('kimAddresses', function ($q) {
                    $q->where('status', KimAddressStatus::ORDERED);
                });
        })->count();

        return $count < app(KimSettings::class)->kim_booking_limit;
    }

    public static function getVendor(?Pharmacy $pharmacy = null): string
    {
        $vendor = app(KimSettings::class)->kim_vendor;

        if (! $pharmacy) {
            /** @var KimVendorEnum $v */
            $v = KimVendorEnum::tryFrom($vendor);

            return $v->value;
        }

        /** @var User $owner */
        $owner = $pharmacy->owner();
        $firstKimAddressWithVendor = KimAddress::query()
            ->whereIn('pharmacy_id', $owner->pharmacies()->pluck('id'))
            ->whereNotNull('vendor')
            ->first();

        if ($firstKimAddressWithVendor) {
            /** @var string $vendor */
            $vendor = $firstKimAddressWithVendor->vendor;
        }

        /** @var KimVendorEnum $v */
        $v = KimVendorEnum::tryFrom($vendor);

        return $v->value;
    }

    public static function getKimAddressDomain(?Pharmacy $pharmacy): string
    {
        /** @var string */
        return config('kim.vendors.'.self::getVendor($pharmacy).'.domain');
    }

    public static function needsModalConfirmation(?Pharmacy $pharmacy): bool
    {
        if ($pharmacy === null) {
            return false;
        }

        if (! user()?->can('create', [KimAddress::class, $pharmacy])) {
            return false;
        }

        if (! (auth()->user() instanceof User)) {
            throw new \RuntimeException('User not found');
        }

        return auth()->user()->isOwner()
            && $pharmacy->kimAddresses()->whereIn('status', [KimAddressStatus::RESERVED, KimAddressStatus::ORDERED]
            )->count() > 0
            && ! $pharmacy->getGeneralSetting('confirmed_kim_booking_modal')?->value;
    }

    public static function isNew(User $user): bool
    {
        $pharmacies = $user->pharmacies->pluck('id')->toArray();

        return ! KimAddress::whereHas('pharmacy', function ($q) use ($pharmacies) {
            $q->whereIn('id', $pharmacies);
        })->exists();
    }
}
