<?php

namespace App\Observers;

use App\Actions\DocSpaces\AssignRiseSDRGroupToDocSpaceAction;
use App\Actions\DocSpaces\RemoveRiseSDRGroupFromDocSpaceAction;
use App\DocSpace;
use App\DocSpaceDocSpaceGroup;
use App\DocSpaceGroup;
use Illuminate\Http\Client\RequestException;
use Throwable;

class DocSpaceDocSpaceGroupObserver
{
    public function created(DocSpaceDocSpaceGroup $docSpaceDocSpaceGroup): void
    {
        if (! ($docSpaceDocSpaceGroup->docSpace instanceof DocSpace)) {
            throw new \RuntimeException('Invalid docSpace instance');
        }
        if (! ($docSpaceDocSpaceGroup->docSpaceGroup instanceof DocSpaceGroup)) {
            throw new \RuntimeException('Invalid docSpaceGroup instance');
        }

        app(AssignRiseSDRGroupToDocSpaceAction::class)
            ->setSDRDocSpaceId($docSpaceDocSpaceGroup->docSpace->sdr_doc_space_id)
            ->setSDRGroupId($docSpaceDocSpaceGroup->docSpaceGroup->sdr_group_id)
            ->setReadPermission(true)
            ->setWritePermission(true)
            ->setDeletePermission(true)
            ->execute();
    }

    public function updated(DocSpaceDocSpaceGroup $docSpaceDocSpaceGroup): void
    {
        //
    }

    /**
     * @throws Throwable
     */
    public function deleting(DocSpaceDocSpaceGroup $docSpaceDocSpaceGroup): void
    {
        if (! ($docSpaceDocSpaceGroup->docSpace instanceof DocSpace)) {
            throw new \RuntimeException('Invalid docSpace instance');
        }
        if (! ($docSpaceDocSpaceGroup->docSpaceGroup instanceof DocSpaceGroup)) {
            throw new \RuntimeException('Invalid docSpaceGroup instance');
        }

        try {
            app(RemoveRiseSDRGroupFromDocSpaceAction::class)
                ->setSDRDocSpaceId($docSpaceDocSpaceGroup->docSpace->sdr_doc_space_id)
                ->setSDRGroupId($docSpaceDocSpaceGroup->docSpaceGroup->sdr_group_id)
                ->execute();
        } catch (RequestException $e) {
            if (! substr_count($e->getMessage(), 'GROUP__NOT_FOUND')) {
                throw ($e);
            }
        }
    }

    public function deleted(DocSpaceDocSpaceGroup $docSpaceDocSpaceGroup): void
    {
        //
    }

    public function restored(DocSpaceDocSpaceGroup $docSpaceDocSpaceGroup): void
    {
        //
    }

    public function forceDeleted(DocSpaceDocSpaceGroup $docSpaceDocSpaceGroup): void
    {
        //
    }
}
