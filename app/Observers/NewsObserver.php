<?php

namespace App\Observers;

use App\news;
use Illuminate\Support\Facades\Storage;

class NewsObserver
{
    /**
     * Handle the = news "created" event.
     *
     * @return void
     */
    public function created(news $news)
    {
        //
    }

    /**
     * Handle the = news "updated" event.
     *
     * @return void
     */
    public function updated(news $news)
    {
        //
    }

    /**
     * Handle the = news "deleted" event.
     *
     * @return void
     */
    public function deleted(news $news)
    {
        if ($news->image) {
            Storage::delete($news->image);
        }
    }

    /**
     * Handle the = news "restored" event.
     *
     * @return void
     */
    public function restored(news $news)
    {
        //
    }

    /**
     * Handle the = news "force deleted" event.
     *
     * @return void
     */
    public function forceDeleted(news $news)
    {
        //
    }
}
