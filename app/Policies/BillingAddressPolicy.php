<?php

namespace App\Policies;

use App\BillingAddress;
use App\Enums\PermissionEnum;
use App\Enums\StaffRoleEnum;
use App\Traits\AuthorizesStaff;
use App\User;
use Exception;
use Illuminate\Auth\Access\HandlesAuthorization;

class BillingAddressPolicy
{
    use AuthorizesStaff, HandlesAuthorization;

    /**
     * @return array<string, array<PermissionEnum|string>>
     */
    public function staffPermissions(): array
    {
        return [
            StaffRoleEnum::OPERATIONS => PermissionEnum::viewAndViewAny(),
            StaffRoleEnum::SUPPORT => PermissionEnum::viewAndViewAny(),
        ];
    }

    /**
     * Determine whether the user can view any models.
     *
     *
     * @return mixed
     */
    public function viewAny(User $user)
    {
        return $user->isOwner() || $user->isSubOwner();
    }

    /**
     * Determine whether the user can view the model.
     *
     *
     * @return mixed
     */
    public function view(User $user, BillingAddress $billingAddress)
    {
        return $billingAddress->owner->is($user);

    }

    /**
     * Determine whether the user can create models.
     *
     *
     * @return mixed
     */
    public function create(User $user)
    {
        return $user->isOwner() || $user->isSubOwner();

    }

    /**
     * Determine whether the user can update the model.
     *
     *
     * @return mixed
     *
     * @throws Exception
     */
    public function update(User $user, BillingAddress $billingAddress)
    {
        return $billingAddress->editors()->contains(fn (User $editor) => $editor->is($user));
    }

    /**
     * Determine whether the user can delete the model.
     *
     *
     * @return mixed
     *
     * @throws Exception
     */
    public function delete(User $user, BillingAddress $billingAddress)
    {
        return $billingAddress->editors()->contains(fn (User $editor) => $editor->is($user));
    }

    /**
     * Determine whether the user can restore the model.
     *
     *
     * @return mixed
     */
    public function restore(User $user, BillingAddress $billingAddress)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     *
     * @return mixed
     */
    public function forceDelete(User $user, BillingAddress $billingAddress)
    {
        return false;
    }
}
