<?php

namespace App\Policies;

use App\Enums\PermissionEnum;
use App\Enums\StaffRoleEnum;
use App\Traits\AuthorizesStaff;
use Illuminate\Auth\Access\HandlesAuthorization;

class CategoryPolicy
{
    use AuthorizesStaff, HandlesAuthorization, NovaDefaultFunctions;

    /**
     * @return array<string, array<PermissionEnum>>
     */
    public function staffPermissions(): array
    {
        return [
            StaffRoleEnum::EDITOR => PermissionEnum::except(PermissionEnum::restoreAndForceDelete()),
            StaffRoleEnum::OPERATIONS => PermissionEnum::except(PermissionEnum::restoreAndForceDelete()),
        ];
    }
}
