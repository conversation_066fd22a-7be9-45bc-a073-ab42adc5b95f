<?php

namespace App\Policies;

use App\Enums\PermissionEnum;
use App\Traits\AuthorizesStaff;
use Illuminate\Auth\Access\HandlesAuthorization;

class VaccinationImportBatchImportPolicy
{
    use AuthorizesStaff, HandlesAuthorization, NovaDefaultFunctions;

    /**
     * @return array<string, array<PermissionEnum>>
     */
    public function staffPermissions(): array
    {
        return [];
    }
}
