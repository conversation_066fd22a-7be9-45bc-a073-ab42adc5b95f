<?php

namespace App\Policies;

use App\PharmaceuticalServicePatient;
use App\Pharmacy;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class PharmaceuticalServicePatientPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    public function viewAny(User $user, Pharmacy $pharmacy)
    {
        return $user->can('store', [PharmaceuticalServicePatient::class, $pharmacy]);
    }

    public function update(User $user, PharmaceuticalServicePatient $pharmaceuticalServicePatient)
    {
        return true;
    }

    public function store(User $user, Pharmacy $pharmacy)
    {
        return true;
    }
}
