<?php

namespace App\Actions\Users;

use App\Enums\IDPSalutation;
use App\User;
use Exception;
use Illuminate\Support\Facades\DB;

class UpdateUserAction
{
    private array $data = [];

    private ?User $user = null;

    private bool $createIdpUser = false;

    private ?string $loginEmail = null;

    public function __construct(
        private CreateIDPUserAction $createIDPUserAction,
    ) {}

    public function prepare(): static
    {
        return $this;
    }

    public function setData(array $data = []): self
    {
        $this->data = $data;

        return $this;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function setUser(?User $user = null): self
    {
        $this->user = $user;

        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function shouldBeCreatedAtIdp(bool $createIdpUser = true, ?string $loginEmail = null): self
    {
        $this->createIdpUser = $createIdpUser;
        $this->loginEmail = $loginEmail;

        return $this;
    }

    public function execute(): User
    {
        DB::beginTransaction();

        try {
            if (count($this->data) > 0) {
                $this->user->update($this->data);
            }

            if ($this->createIdpUser) {
                $this->createIDPUserAction
                    ->setUuid($this->user->uuid)
                    ->setSalutation(IDPSalutation::fromUserSalutationValue($this->user->salutation))
                    ->setTitle($this->user->title)
                    ->setFirstName($this->user->first_name)
                    ->setLastName($this->user->last_name)
                    ->setLoginEmail($this->loginEmail)
                    ->setEmailVerified(false)
                    ->setPhone($this->user->phone)
                    ->setPasswordHash($this->user->password)
                    ->setPhoneNumberVerified(false)
                    ->createUser();
            }

        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
        DB::commit();

        return $this->user;
    }
}
