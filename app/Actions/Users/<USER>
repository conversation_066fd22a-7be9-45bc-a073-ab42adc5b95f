<?php

namespace App\Actions\Users;

use App\PharmaceuticalService;
use App\Shift;
use App\ShiftPlanGroupUser;
use App\User;
use App\UserPharmacyProfile;
use App\Vaccination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeletePharmacyEmployee
{
    public function execute(User $user): void
    {
        DB::beginTransaction();

        try {
            // 1. Delete shift plan related data
            ShiftPlanGroupUser::where('user_id', $user->id)->delete();

            // 2. Unlink pharmaceutical services
            PharmaceuticalService::query()
                ->where('user_id', $user->id)
                ->update(['user_id' => null]);

            // 3. Unlink vaccinations
            Vaccination::query()
                ->where('user_id', $user->id)
                ->update(['user_id' => null]);

            UserPharmacyProfile::where('company_user_id', $user->id)
                ->update(['company_user_id' => null]);

            // 4. Clean up user profile data
            UserPharmacyProfile::where('user_id', $user->id)->delete();

            // 5. Delete at IDP
            if ($user->is_at_idp) {
                app(DeleteIDPUserAction::class)->deleteUser($user->uuid);
            }

            // 6. Clean up user data (existing functionality)
            $user->forceDelete();

            DB::commit();

            Log::info('Successfully deleted pharmacy employee', [
                'user_id' => $user->id,
                'user_email' => $user->email,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete pharmacy employee', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }
}
