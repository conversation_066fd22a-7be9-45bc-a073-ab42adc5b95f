<?php

namespace App\Actions\Users;

use App\User;

class DeleteLocalIDPUserAction extends DeleteIDPUserAction
{
    public function deleteUser($userUuid): void
    {
        $this->getBaseRequest()->delete(
            config('oidc-auth.provider.adminUrl').'/users/'.$userUuid
        )->throw();

        User::where('uuid', $userUuid)
            ->update([
                'is_at_idp' => false,
            ]);
    }
}
