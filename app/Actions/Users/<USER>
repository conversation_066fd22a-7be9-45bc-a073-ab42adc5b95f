<?php

namespace App\Actions\Users;

use App\User;

class DeleteRiseIDPUserAction extends DeleteIDPUserAction
{
    public function deleteUser($userUuid): void
    {
        $this->getBaseRequest()
            ->delete(config('oidc-auth.provider.adminUrl').'/pharmacists/v1/'.$userUuid)
            ->throw();

        User::withTrashed()->where('uuid', $userUuid)
            ->update([
                'is_at_idp' => false,
            ]);
    }
}
