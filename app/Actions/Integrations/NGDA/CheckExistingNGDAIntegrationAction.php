<?php

namespace App\Actions\Integrations\NGDA;

use App\Exceptions\Integrations\Ngda\LinkingNGDAException;
use App\Integration;
use App\Pharmacy;

class CheckExistingNGDAIntegrationAction
{
    public function execute(Pharmacy $pharmacy, string $integrationId): void
    {
        $integration = Integration::query()->where('settings->id', $integrationId)->first();
        $currentIntegrationPharmacy = $integration?->integratable;

        if (($currentIntegrationPharmacy && ! $currentIntegrationPharmacy->owner()) || ! user() || ! user()->owner()) {
            throw new \RuntimeException('Owner not found.');
        }

        if ($currentIntegrationPharmacy && $currentIntegrationPharmacy->owner()->id !== user()->owner()->id) {
            throw new LinkingNGDAException('Linking NGDA failed. Integration found for different owner.');
        }
    }
}
