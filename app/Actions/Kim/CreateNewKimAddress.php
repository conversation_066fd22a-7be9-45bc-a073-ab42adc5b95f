<?php

namespace App\Actions\Kim;

use App\Enums\KimAddressStatus;
use App\Enums\KimVendorEnum;
use App\Helper\KimAddressHelper;
use App\KimAddress;
use App\Pharmacy;
use App\Rules\NotOnBlacklist;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CreateNewKimAddress
{
    public function execute(array $input, Pharmacy $pharmacy): KimAddress
    {
        Gate::authorize('create', [KimAddress::class, $pharmacy]);

        if (isset($input['email']) && ! Str::contains($input['email'], '@')) {
            $input['email'] = $input['email'].'@'.KimAddressHelper::getKimAddressDomain($pharmacy);
        }

        $validated = Validator::make($input, [
            'email' => ['required', 'unique:kim_addresses', new \App\Rules\KimAddress($pharmacy), new NotOnBlacklist],
            'tosCheckboxAccepted' => KimAddressHelper::bookingAvailable($pharmacy) ? ['accepted'] : ['nullable'],
        ], [
            'tosCheckboxAccepted.accepted' => 'Bitte bestätigen Sie die Nutzungsbedingungen und Datenschutzerklärung.',
        ], [
            'email' => 'KIM-Adresse',
        ])->validate();

        $kimAddress = null;

        DB::transaction(function () use ($validated, $pharmacy, &$kimAddress) {
            $kimAddress = KimAddress::forceCreate([
                'email' => $validated['email'],
                'pharmacy_id' => $pharmacy->id,
                'billing_address_id' => $pharmacy->billing_address_id,
                'status' => KimAddressStatus::RESERVED->value,
                'reserved_at' => KimAddressHelper::bookingAvailable($pharmacy) ? null : now(),
            ]);

            if (! KimAddressHelper::bookingAvailable($pharmacy)) {
                return $kimAddress;
            }

            if (KimAddressHelper::getVendor($pharmacy) === KimVendorEnum::RISE->value) {
                app(CreateNewKimAddressAtRise::class)->execute($validated, $kimAddress);
            } else {
                app(CreateNewKimAddressAtAkquinet::class)->execute($validated, $kimAddress);
            }
        });

        return $kimAddress;
    }
}
