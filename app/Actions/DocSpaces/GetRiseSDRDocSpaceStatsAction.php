<?php

namespace App\Actions\DocSpaces;

use App\DocSpace;
use App\Helper\RiseSDRApi;
use Illuminate\Http\Client\RequestException;

class GetRiseSDRDocSpaceStatsAction extends AbstractRiseSDRAction
{
    public function __construct(protected DocSpace $docSpace) {}

    public function execute(): self
    {
        try {
            $response = app(RiseSDRApi::class)->getDocSpaceStats($this->docSpace->sdr_doc_space_id);

            $this->lastUsage = $response->json('size');
        } catch (RequestException $e) {
            $this->handleErrors($e);
        }

        return $this;
    }

    public function getErrorMessageFieldName(?string $sdrFieldName = null): string
    {
        return match ($sdrFieldName) {
            null => 'name',
            'name' => 'name',
            default => $sdrFieldName,
        };
    }
}
