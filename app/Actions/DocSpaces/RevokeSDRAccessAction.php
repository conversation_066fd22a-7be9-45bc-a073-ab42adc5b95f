<?php

namespace App\Actions\DocSpaces;

use App\Jobs\RevokeSDRAccessJob;
use App\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class RevokeSDRAccessAction extends AbstractRiseSDRAction
{
    protected User $user;

    public function execute(): self
    {
        // todo: AP-1032
        if (! $this->isSeeded(sdrUserId: $this->user->sdr_user_id)) {
            $jobId = Str::uuid()->toString();
            $jobCacheKey = 'execute-RevokeSDRAccessJob-for-user-'.$this->user->id;
            Cache::set($jobCacheKey, $jobId);
            RevokeSDRAccessJob::dispatch(
                user: $this->user->toArray(),
                jobId: $jobId,
                jobCacheKey: $jobCacheKey
            )->delay(300);
        }

        return $this;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getErrorMessageFieldName(?string $sdrFieldName = null): string
    {
        return match ($sdrFieldName) {
            null => 'id',
            'name' => 'id',
            default => $sdrFieldName,
        };
    }
}
