<?php

namespace App\Actions\CardLink;

use App\CardLinkOrder;
use App\Enums\CardLink\CardLinkPackageChangeEnum;
use App\Enums\CardLink\CardLinkPackageEnum;
use App\Helper\CardLinkOrderHelper;
use App\Mail\CardLink\CardLinkPackageChangedMail;
use Illuminate\Support\Facades\Mail;
use RuntimeException;

class ChangePackage
{
    public static function execute(CardLinkOrder $cardLinkOrder, CardLinkPackageEnum $package): void
    {
        $orderInformation = CardLinkOrderHelper::orderInformation($cardLinkOrder);

        if ($orderInformation === null) {
            throw new RuntimeException(
                sprintf('Diese CardLink-Bestellung [%s] hat keine Bestellinformationen.', $cardLinkOrder->id)
            );
        }

        $changeType = CardLinkPackageChangeEnum::determineChange($orderInformation->package, $package);

        if ($changeType === CardLinkPackageChangeEnum::Same) {
            return;
        }

        ChangePackageAtCardLinkService::execute($cardLinkOrder, $package, $orderInformation);

        $orderInformation->package = $package;

        $cardLinkOrder->update([
            'order_information' => $orderInformation->toArray(),
        ]);

        if ($cardLinkOrder->user) {
            Mail::to($cardLinkOrder->user)->queue(new CardLinkPackageChangedMail($cardLinkOrder->user, $cardLinkOrder, $changeType));
        }

        notify('Paketänderung erfolgreich');

        $cardLinkOrder->pharmacy?->searchable();
    }
}
