<?php

namespace App\Actions\Subscription;

use App\Pharmacy;
use App\SubscriptionOrder;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;

/**
 * @deprecated
 */
class CancelAllMembershipToDate
{
    public function handle(Pharmacy $pharmacy, Carbon $cancelDate): void
    {
        DB::transaction(function () use ($pharmacy, $cancelDate) {
            $cancelDate->endOfMonth();

            foreach ($pharmacy->oldSubscriptions as $subscription) {
                if ($subscription->ends_at?->lte($cancelDate)) {
                    continue;
                }

                $subscriptionOrders = $subscription->subscriptionOrders->where('ended_at', '>', $cancelDate->format('Y-m-d H:i:s'));

                foreach ($subscriptionOrders as $subscriptionOrder) {
                    if (! $subscriptionOrder->invoice_id || $subscriptionOrder->invoice->isCanceled()) {
                        continue;
                    }

                    throw new Exception('Die Kündigung ist nicht möglich, da mindestens eine Bestellung bearbeitet werden würde, die im gewählten Zeitraum bereits abgerechnet wurde.');
                }

                if ($subscription->started_at->gt($cancelDate)) {
                    $subscription->subscriptionOrders()->delete();
                    $subscription->delete();

                    continue;
                }

                $subscriptionOrders
                    ->where('started_at', '>', $cancelDate->toDateString())
                    ->each(fn (SubscriptionOrder $order) => $order->delete());

                $subscription->update([
                    'ends_at' => $cancelDate,
                ]);

                $subscriptionOrders
                    ->where('started_at', '<=', $cancelDate->toDateString())
                    ->where('ended_at', '>=', $cancelDate->toDateString())
                    ->each(function (SubscriptionOrder $order) use ($cancelDate, &$subscription) {
                        $order->update([
                            'ended_at' => $cancelDate,
                        ]);

                        $order->update([
                            'total_price' => $order->calcPrice(),
                        ]);

                        $subscription->update([
                            'cycle_started_at' => $order->started_at,
                            'cycle_ends_at' => $order->ended_at,
                        ]);
                    });
            }
        });
    }
}
