<?php

namespace App\Actions\IhreApotheken;

use App\Integrations\IaIntegration;
use App\Integrations\IntegrationTypeEnum;
use App\Pharmacy;

class SetIntegrationVisibilityInApoguideAction
{
    public function execute(Pharmacy $pharmacy, bool $isVisible): void
    {
        $integration = $pharmacy->getIntegration(IntegrationTypeEnum::IhreApotheken);

        if ($integration) {
            /** @var IaIntegration $settings */
            $settings = $integration->settings;

            $settings->isVisibleInPharmacySearch = $isVisible;

            $pharmacy->setIntegration($settings);
        }
    }
}
