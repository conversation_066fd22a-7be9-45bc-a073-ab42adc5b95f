<?php

namespace App\Casts;

use App\Integrations\IntegrationTypeEnum;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class IntegrationSettingCast implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        if ($value === null) {
            return null;
        }

        if (! is_string($value)) {
            throw new \Exception('Invalid JSON in "$key" field.');
        }

        if (! json_validate($value)) {
            throw new \Exception('Invalid JSON in "$key" field.');
        }

        if (! in_array($attributes['integration_type'], IntegrationTypeEnum::values()->toArray())) {
            // @phpstan-ignore-next-line
            throw new \Exception(sprintf('Invalid integration type "%s" in "$integration_type" field.', $attributes['integration_type']));
        }

        $integrationType = $attributes['integration_type'];

        /** @phpstan-ignore-next-line */
        return $integrationType::from(json_decode($value, true));
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        if ($value === null) {
            return null;
        }

        if (is_string($value) && json_validate($value)) {
            return $value;
        }

        if (! in_array($attributes['integration_type'], IntegrationTypeEnum::values()->toArray())) {
            throw new \Exception('Invalid integration settings.');
        }

        if (! $value instanceof $attributes['integration_type']) {
            throw new \Exception('Invalid integration settings.');
        }

        /** @phpstan-ignore-next-line */
        return $value->toJson();
    }
}
