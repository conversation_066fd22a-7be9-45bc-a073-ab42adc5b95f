<?php

namespace App\Exceptions\Integrations\Ngda;

use App\Exceptions\Integrations\IntegrationNotFoundException;

class NGDAIntegrationNotFoundException extends IntegrationNotFoundException
{
    public const NGDA_INTEGRATION_NOT_FOUND_MESSAGE = 'Es konnte keine NGDA-Integration für die Apotheke gefunden werden.';

    public function __construct(string $message = self::NGDA_INTEGRATION_NOT_FOUND_MESSAGE)
    {
        parent::__construct($message);
    }
}
