<?php

namespace App\Providers;

use App\Domains\Payment\Application\Listeners\StripeEventListener;
use App\Domains\Subscription\Application\Listeners\AddKimProductToSubscription;
use App\Domains\Subscription\Application\Listeners\SendSubscriptionConfirmationListener;
use App\Events\CreatingCardLinkOrder;
use App\Events\Kim\KimAddressActivated;
use App\Events\Kim\KimAddressOrdered;
use App\Events\PharmacyAfterCreatedOrUpdatedEvent;
use App\Events\PharmacySaved;
use App\Events\Settings\PharmacyPrivacyPolicyAccepted;
use App\Events\Settings\PharmacyPrivacyPolicyDeclined;
use App\Events\Settings\PharmacyTermsOfUseAccepted;
use App\Events\Settings\PharmacyTermsOfUseDeclined;
use App\Listeners\CardLink\GenerateUuid;
use App\Listeners\CreateGedisaIdListener;
use App\Listeners\DeactivateApoGuideCardLinkVendorListener;
use App\Listeners\InstantRegistrationLinkSentEvent;
use App\Listeners\LogLoginEvent;
use App\Listeners\Pharmacy\AddOwnerToDocSpaceGroupsListener;
use App\Listeners\Pharmacy\EnsureSettingsConsistency;
use App\Listeners\Pharmacy\PushPharmacyDataToIA;
use App\Listeners\Pharmacy\RemoveNGDAIntegrationListener;
use App\Listeners\Pharmacy\RemoveUsersFromAllDocSpaceGroupsListener;
use App\Listeners\PurgePennantListener;
use App\Listeners\SendKimAddressInformation;
use App\Listeners\SetFirstLoginEvent;
use App\Listeners\SetLastLogin;
use App\Listeners\SetVerifiedEmail;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Mail\Events\MessageSent;
use Laravel\Cashier\Events\WebhookReceived;
use Spatie\LaravelSettings\Events\SettingsSaved;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        Login::class => [
            SetVerifiedEmail::class,
            SetFirstLoginEvent::class,
            SetLastLogin::class,
            LogLoginEvent::class,
        ],

        MessageSent::class => [
            InstantRegistrationLinkSentEvent::class,
        ],

        PharmacySaved::class => [
            EnsureSettingsConsistency::class, // This must always be the first Listener since other Listeners could rely on the data.
            CreateGedisaIdListener::class,
        ],

        PharmacyAfterCreatedOrUpdatedEvent::class => [
            PushPharmacyDataToIA::class,
        ],

        PharmacyTermsOfUseAccepted::class => [
            AddOwnerToDocSpaceGroupsListener::class,
        ],

        PharmacyTermsOfUseDeclined::class => [
            RemoveNGDAIntegrationListener::class,
            RemoveUsersFromAllDocSpaceGroupsListener::class,
            DeactivateApoGuideCardLinkVendorListener::class,
        ],

        PharmacyPrivacyPolicyAccepted::class => [
            AddOwnerToDocSpaceGroupsListener::class,
        ],

        PharmacyPrivacyPolicyDeclined::class => [
            RemoveNGDAIntegrationListener::class,
            RemoveUsersFromAllDocSpaceGroupsListener::class,
            DeactivateApoGuideCardLinkVendorListener::class,
        ],

        KimAddressOrdered::class => [
            SendKimAddressInformation::class,
        ],

        KimAddressActivated::class => [
            SendKimAddressInformation::class,
            AddKimProductToSubscription::class,
        ],

        SettingsSaved::class => [
            PurgePennantListener::class,
        ],

        CreatingCardLinkOrder::class => [
            GenerateUuid::class,
        ],

        WebhookReceived::class => [
            StripeEventListener::class,
        ],

        \App\Domains\Subscription\Domain\Events\SubscriptionCreated::class => [
            SendSubscriptionConfirmationListener::class,
        ],

        \App\Domains\Subscription\Domain\Events\SubscriptionUpdated::class => [
            SendSubscriptionConfirmationListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
        //
    }
}
