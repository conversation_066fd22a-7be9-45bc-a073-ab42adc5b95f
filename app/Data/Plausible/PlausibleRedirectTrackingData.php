<?php

namespace App\Data\Plausible;

use App\Settings\PlausibleSettings;
use <PERSON><PERSON>\LaravelData\Data;

class PlausibleRedirectTrackingData extends Data
{
    public function __construct(
        public string $redirectUrl,
        public string $originUrl,
        public ?string $userAgent = '',
        public ?string $ip = '',
        public ?string $domain = null,
        public ?string $referrer = null,
        public ?string $name = 'Outbound Link: Click',
    ) {}

    public function getDomain(): string
    {
        return $this->domain ?? app(PlausibleSettings::class)->domain;
    }
}
