<?php

namespace App\Data\Transformers;

use <PERSON><PERSON>\LaravelData\Support\DataProperty;
use <PERSON><PERSON>\LaravelData\Support\Transformation\TransformationContext;
use <PERSON>tie\LaravelData\Transformers\Transformer;

class LimitString implements Transformer
{
    public function __construct(
        public int $length = 80,
    ) {}

    public function transform(DataProperty $property, mixed $value, TransformationContext $context): mixed
    {
        if (! is_string($value)) {
            return $value;
        }

        return str($value)->substr(0, $this->length)->value();
    }
}
