<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * Class Author
 *
 * @mixin IdeHelperAuthor
 */
class Author extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumbnail')
            ->width(112)
            ->sharpen(10)
            ->performOnCollections('image');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')
            ->singleFile()
            ->withResponsiveImages();
    }

    public function getFullNameAttribute(): string
    {
        return trim($this->title.' '.$this->first_name.' '.$this->last_name);
    }

    public function news()
    {
        return $this->hasMany(News::class);
    }
}
