<?php

namespace App\Http\Integrations\Ia\ConsentServiceAPI;

use Saloon\Http\Auth\TokenAuthenticator;
use Saloon\Http\Connector;

class ConsentServiceApi extends Connector
{
    public function __construct(
        public readonly string $baseUrl,
        public readonly string $token
    ) {}

    protected function defaultAuth(): TokenAuthenticator
    {
        return new TokenAuthenticator($this->token);
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    public function resolveBaseUrl(): string
    {
        return $this->baseUrl;
    }
}
