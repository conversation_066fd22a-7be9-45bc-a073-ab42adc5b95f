<?php

namespace App\Http\Integrations\Ia\PartnerApi\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class ChangeBackendSystemRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(
        private readonly string $authCode,
        private readonly string $apPharmacyId
    ) {}

    public function resolveEndpoint(): string
    {
        return '/partner/authcode/v1.0/change-pharmacybackend';
    }

    protected function defaultHeaders(): array
    {
        return [
            'authCode' => $this->authCode,
        ];
    }

    /** @return array<string, string> */
    protected function defaultBody(): array
    {
        return [
            'externalKey' => $this->apPharmacyId,
        ];
    }
}
