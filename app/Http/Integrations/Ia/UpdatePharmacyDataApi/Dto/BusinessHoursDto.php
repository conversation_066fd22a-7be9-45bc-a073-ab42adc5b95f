<?php

namespace App\Http\Integrations\Ia\UpdatePharmacyDataApi\Dto;

use App\Data\Pipelines\StripEmptyBusinessHours;
use App\Interfaces\Mergeable;
use App\Pharmacy;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Spatie\LaravelData\Concerns\WireableData;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataPipeline;

class BusinessHoursDto extends Data implements Mergeable
{
    use WireableData;

    public ?BusinessHoursDayDto $mon = null;

    public ?BusinessHoursDayDto $tue = null;

    public ?BusinessHoursDayDto $wed = null;

    public ?BusinessHoursDayDto $thu = null;

    public ?BusinessHoursDayDto $fri = null;

    public ?BusinessHoursDayDto $sat = null;

    public ?BusinessHoursDayDto $sun = null;

    public function isEmpty(): bool
    {
        return $this->mon === null
            && $this->tue === null
            && $this->wed === null
            && $this->thu === null
            && $this->fri === null
            && $this->sat === null
            && $this->sun === null;
    }

    public function hasWeekdaysDefined(): bool
    {
        return $this->mon !== null
            && $this->tue !== null
            && $this->wed !== null
            && $this->thu !== null
            && $this->fri !== null;
    }

    public static function fromPharmacyModel(Pharmacy $pharmacy): self
    {
        return self::from([
            'mon' => self::getBusinessHoursDayDto($pharmacy, 0),
            'tue' => self::getBusinessHoursDayDto($pharmacy, 1),
            'wed' => self::getBusinessHoursDayDto($pharmacy, 2),
            'thu' => self::getBusinessHoursDayDto($pharmacy, 3),
            'fri' => self::getBusinessHoursDayDto($pharmacy, 4),
            'sat' => self::getBusinessHoursDayDto($pharmacy, 5),
            'sun' => self::getBusinessHoursDayDto($pharmacy, 6),
        ]);
    }

    /**
     * @throws Exception
     */
    private static function getBusinessHoursDayDto(Pharmacy $pharmacy, int $forDay): ?BusinessHoursDayDto
    {
        $businessHours = $pharmacy->businessHours
            ->where('day_of_week', $forDay)
            ->values();

        if ($businessHours->count() > 2) {
            throw new Exception('Unsupported. More than 2 business hour ranges for the same day');
        }

        return match ($businessHours->count()) {
            1 => BusinessHoursDayDto::from([
                'morningStart' => self::getTimeStringFromBusinessHour($businessHours, '0.opens'),
                'afternoonEnd' => self::getTimeStringFromBusinessHour($businessHours, '0.closes'),
            ]),
            2 => BusinessHoursDayDto::from([
                'morningStart' => self::getTimeStringFromBusinessHour($businessHours, '0.opens'),
                'morningEnd' => self::getTimeStringFromBusinessHour($businessHours, '0.closes'),
                'afternoonStart' => self::getTimeStringFromBusinessHour($businessHours, '1.opens'),
                'afternoonEnd' => self::getTimeStringFromBusinessHour($businessHours, '1.closes'),
            ]),
            default => null
        };
    }

    private static function getTimeStringFromBusinessHour(Collection $businessHours, string $target): ?string
    {
        /** @var string|null $timeString */
        $timeString = data_get($businessHours, $target);

        if (! $timeString) {
            return null;
        }

        return date('H:i', strtotime($timeString) ?: null);
    }

    public function toHtml(): string
    {
        if ($this->isEmpty()) {
            return 'Keine Öffnungszeiten hinterlegt';
        }

        $template = '<span class="inline-block w-[40px]">%s</span> %s';

        return collect()->when($this->mon, fn (\Illuminate\Support\Collection $collection) => $collection->add(sprintf($template, 'Mo: ', $this->mon?->toHtml())))
            ->when($this->tue, fn (\Illuminate\Support\Collection $collection) => $collection->add(sprintf($template, 'Di:', $this->tue?->toHtml())))
            ->when($this->wed, fn (\Illuminate\Support\Collection $collection) => $collection->add(sprintf($template, 'Mi: ', $this->wed?->toHtml())))
            ->when($this->thu, fn (\Illuminate\Support\Collection $collection) => $collection->add(sprintf($template, 'Do: ', $this->thu?->toHtml())))
            ->when($this->fri, fn (\Illuminate\Support\Collection $collection) => $collection->add(sprintf($template, 'Fr: ', $this->fri?->toHtml())))
            ->when($this->sat, fn (\Illuminate\Support\Collection $collection) => $collection->add(sprintf($template, 'Sa: ', $this->sat?->toHtml())))
            ->when($this->sun, fn (\Illuminate\Support\Collection $collection) => $collection->add(sprintf($template, 'So: ', $this->sun?->toHtml())))
            ->join('<br />');
    }

    /**
     * @return array<array<string, int|string>>
     */
    public function toApRecords(): array
    {
        return [
            ...$this->mon?->toApRecords(0) ?? [],
            ...$this->tue?->toApRecords(1) ?? [],
            ...$this->wed?->toApRecords(2) ?? [],
            ...$this->thu?->toApRecords(3) ?? [],
            ...$this->fri?->toApRecords(4) ?? [],
            ...$this->sat?->toApRecords(5) ?? [],
            ...$this->sun?->toApRecords(6) ?? [],
        ];
    }

    /**
     * @param  BusinessHoursDto  $other
     */
    public function isEqualTo(Mergeable $other): bool
    {
        return $this->mon?->toArray() == $other->mon?->toArray()
            && $this->tue?->toArray() == $other->tue?->toArray()
            && $this->wed?->toArray() == $other->wed?->toArray()
            && $this->thu?->toArray() == $other->thu?->toArray()
            && $this->fri?->toArray() == $other->fri?->toArray()
            && $this->sat?->toArray() == $other->sat?->toArray()
            && $this->sun?->toArray() == $other->sun?->toArray();
    }

    public static function pipeline(): DataPipeline
    {
        return parent::pipeline()->firstThrough(StripEmptyBusinessHours::class);
    }
}
