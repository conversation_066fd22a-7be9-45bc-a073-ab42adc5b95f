<?php

namespace App\Http\Integrations\Apomail\Requests;

use App\Settings\ApoMailSettings;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasFormBody;

class ApomailAdminLoginRequest extends Request implements HasBody
{
    use HasFormBody;

    protected Method $method = Method::POST;

    public function resolveEndpoint(): string
    {
        return '/api/login';
    }

    /**
     * @return array<string, mixed>
     */
    public function defaultBody(): array
    {
        return [
            'username' => ApoMailSettings::username(),
            'password' => ApoMailSettings::password(),
        ];
    }
}
