<?php

namespace App\Http\Requests;

use App\Actions\Users\CreateUserAction;
use App\Association;
use App\Enums\AssociationRoleEnum;
use App\Rules\GedisaMail;
use App\User;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AssociationUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $user = $this->route('user') ?? new User;

        $rules = [
            'salutation' => [
                'required',
                'string',
            ],
            'title' => [
                'nullable',
                'string',
                'max:100',
            ],
            'first_name' => [
                'required',
                'string',
                'max:250',
            ],
            'last_name' => [
                'required',
                'string',
                'max:250',
            ],
            'differing_notifications_email_enabled' => [
                'nullable',
                'boolean',
            ],
            'login_email' => [
                Rule::requiredIf(! $user->exists),
                app(GedisaMail::class),
            ],
            'phone' => [
                'required',
                'string',
                'max:100',
            ],
            'permissions' => [
                'nullable',
                'array',
            ],
        ];

        if ($user->exists || $this->input('differing_notifications_email_enabled')) {
            $rules['notifications_email'] = [
                'required',
                app(GedisaMail::class),
            ];
        }

        return $rules;
    }

    public function createAssociationUser(Association $association): User
    {
        $validated = $this->validated();

        return app(CreateUserAction::class)
            ->setSalutation($validated['salutation'])
            ->setTitle($validated['title'])
            ->setFirstName($validated['first_name'])
            ->setLastName($validated['last_name'])
            ->setLoginEmail($validated['login_email'])
            ->setNotificationsEmail($validated['notifications_email'] ?? $validated['login_email'])
            ->setPhone($validated['phone'])
            ->setAssociationUser()
            ->setAssociation(
                $association,
                AssociationRoleEnum::EMPLOYEE,
                $this->getPermissions($validated)
            )
            ->createUser();
    }

    public function updateAssociationUser(Association $association, User $user)
    {
        $validated = $this->validated();
        $validated['email'] = $validated['notifications_email'];

        $user->update($validated);

        $association->reassignUser(
            $user,
            AssociationRoleEnum::EMPLOYEE,
            $this->getPermissions($validated)
        );
    }

    private function getPermissions($validated)
    {
        return $validated['permissions'] ?? [];
    }

    protected function failedValidation(Validator $validator)
    {
        notify(__('notifications.employee.validation_error'), 'error');
        parent::failedValidation($validator);
    }
}
