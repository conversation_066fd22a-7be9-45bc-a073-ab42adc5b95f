<?php

namespace App\Http\Requests;

use App\User;

class UpdatePharmacy extends Pharmacy
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        if (! ($this->user() instanceof User)) {
            throw new \RuntimeException('User not found');
        }

        $pharmacy = $this->route('pharmacy');

        if (! ($this->user() instanceof User)) {
            abort(404, 'User not found');
        }

        return $pharmacy && $this->user()->can('update', $pharmacy);
    }
}
