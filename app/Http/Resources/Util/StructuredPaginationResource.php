<?php

namespace App\Http\Resources\Util;

use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class StructuredPaginationResource extends AnonymousResourceCollection
{
    /**
     * Create a paginate-aware HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    protected function preparePaginatedResponse($request)
    {
        if ($this->preserveAllQueryParameters) {
            $this->resource->appends($request->query());
        } elseif (! is_null($this->queryParameters)) {
            $this->resource->appends($this->queryParameters);
        }

        return (new StructuredPaginatedResourceResponse($this))->toResponse($request);
    }
}
