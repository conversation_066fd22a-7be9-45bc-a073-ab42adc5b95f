<?php

namespace App\Http\Middleware;

use App\Enums\Ia\IaWebComponentEnum;
use App\Exceptions\Integrations\Ia\FetchTokenException;
use App\Features\IhreApothekenPreflight;
use App\Helper\IaHelper;
use App\Helper\NetworkHelper;
use App\Http\Integrations\Ia\PartnerApi\Dto\TokenDto;
use App\Http\Integrations\Ia\PartnerApi\PartnerApi;
use App\Http\Integrations\Ia\PartnerApi\Requests\GetWebComponentAuthTokenRequest;
use App\Integration;
use App\Integrations\IaIntegration;
use App\Integrations\IntegrationTypeEnum;
use App\Pharmacy;
use Cache;
use Closure;
use Illuminate\Http\Request;
use Laravel\Pennant\Feature;
use Saloon\Exceptions\Request\FatalRequestException;
use Saloon\Exceptions\Request\RequestException;

class IaTokenAuthMiddleware
{
    // @phpstan-ignore-next-line
    public function handle(Request $request, Closure $next)
    {
        $user = user();

        if (! $user) {
            return redirect()->route('login');
        }

        $currentPharmacy = currentPharmacy();

        if (! $currentPharmacy) {
            return redirect()->route('dashboard');
        }

        // @phpstan-ignore-next-line
        $cacheKey = IaHelper::tokenCacheKey($user->id, $currentPharmacy->id);

        if (! Cache::has($cacheKey)) {

            if (Feature::active(IhreApothekenPreflight::class)) {
                if (! $user->isPreflightUser()) {
                    abort(403);
                }
            }

            if (! $currentPharmacy->hasIaEnabled()) {
                return redirect(route('ia.activate', ['pharmacy' => $currentPharmacy]));
            }

            try {
                $tokenDto = $this->requestToken($currentPharmacy);

                Cache::put($cacheKey, $tokenDto->token, now()->addHours(2));

                $this->setZpaFlagOnIntegration($currentPharmacy, $tokenDto->zpa_customer);

                $this->setAdditionalComponentsOnIntegration($currentPharmacy, $tokenDto->additional_components);
            } catch (FetchTokenException $exception) {
                notify('Es ist ein Fehler bei der Kommunikation mit IhreApotheken aufgetreten. Wenn das Problem weiterhin besteht, wenden Sie sich bitte an den Support.', 'error', 5);

                return redirect(route('dashboard'));
            }
        }

        return $next($request);
    }

    /**
     * @throws FetchTokenException
     */
    private function requestToken(Pharmacy $pharmacy): TokenDto
    {
        /** @var ?Integration $iaIntegration */
        $iaIntegration = $pharmacy->getIntegration(IntegrationTypeEnum::IhreApotheken);

        /** @var IaIntegration $settings */
        $settings = $iaIntegration?->settings;

        $permissions = user()
            ?->pharmacies()
            ->wherePivot('pharmacy_id', $pharmacy->id)
            ->first()
            ?->pivot
            ->permissions ?? [];

        $partnerApi = new PartnerApi;

        try {
            $response = $partnerApi->send(
                new GetWebComponentAuthTokenRequest(
                    $settings->iaPharmacyId,
                    (string) $pharmacy->id,
                    $permissions
                )
            )->throw();

            if (! $response->ok()) {
                throw new \Exception('Failed to get token');
            }

            /** @var TokenDto $dto */
            $dto = $response->dto();

            return $dto;
        } catch (FatalRequestException|RequestException $exception) {
            NetworkHelper::reportRequest($exception);

            throw new FetchTokenException('Failed to fetch token from IA Partner API');
        }
    }

    private function setZpaFlagOnIntegration(Pharmacy $pharmacy, bool $isZpaCustomer): void
    {
        /** @var IaIntegration|null $integration */
        $integration = $pharmacy->getIntegration(IntegrationTypeEnum::IhreApotheken)?->settings;

        if (! $integration) {
            return;
        }

        $integration->isZpaCustomer = $isZpaCustomer;

        $pharmacy->setIntegration($integration);
    }

    /**
     * @param  array<string>  $additionalComponents
     */
    private function setAdditionalComponentsOnIntegration(Pharmacy $pharmacy, array $additionalComponents): void
    {
        /** @var IaIntegration|null $integration */
        $integration = $pharmacy->getIntegration(IntegrationTypeEnum::IhreApotheken)?->settings;

        if (! $integration) {
            return;
        }

        /** @var array<string> $safeAdditionalComponentList */
        $safeAdditionalComponentList = collect($additionalComponents)
            ->intersect(IaWebComponentEnum::values())
            ->values()
            ->toArray();

        $integration->additionalComponents = $safeAdditionalComponentList;

        $pharmacy->setIntegration($integration);
    }
}
