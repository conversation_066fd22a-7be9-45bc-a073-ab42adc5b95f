<?php

namespace App\Http\Middleware;

use App\Domains\Subscription\Application\FeatureAccess\BaseFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\DataAccessPeriodFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\FeatureAccess;
use App\Domains\Subscription\Application\Helper\FeatureAccessResolver;
use App\Pharmacy;
use App\Settings\AppSettings;
use App\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;

class EnsureSubscribed
{
    /**
     * @param  class-string<FeatureAccess>  $feature
     */
    public function handle(Request $request, Closure $next, string $feature = BaseFeatureAccess::class)
    {
        if (Carbon::now() < app(AppSettings::class)->terms_of_use_deadline) {
            return $next($request);
        }

        /** @var User $owner */
        $owner = user()->pharmacies->first()?->owner();

        if (blank($owner)) {
            abort(Response::HTTP_FORBIDDEN);
        }

        $pharmacy = $request->route('pharmacy') instanceof Pharmacy ? $request->route('pharmacy') : currentPharmacy();
        assert($pharmacy instanceof Pharmacy);

        if (app(FeatureAccessResolver::class)->fromClassString($feature, $pharmacy)->canUse()) {
            return $next($request);
        }

        if (
            DataAccessPeriodFeatureAccess::check($pharmacy)->canUse()
            && in_array($request->route()->getName(), config('subscription.data-access-period.allowed-routes'))
        ) {
            return $next($request);
        }

        if (
            ! DataAccessPeriodFeatureAccess::check($pharmacy)->canUse()
            && in_array($request->route()->getName(), config('subscription.new-users-whitelisted-routes'))
        ) {
            return $next($request);
        }

        if (DataAccessPeriodFeatureAccess::check($pharmacy)->canUse()) {
            return redirect()->route('pharmacies.data.index', [$pharmacy]);
        }

        return redirect()->route('pharmacies.subscription', $pharmacy);
    }
}
