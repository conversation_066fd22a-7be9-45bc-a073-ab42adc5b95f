<?php

namespace App\Http\Controllers;

use App\Enums\AssociationRoleEnum;
use App\Http\Requests\AssociationUserRequest;
use App\User;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class AssociationUserController extends Controller
{
    public function index()
    {
        $association = user()->associations->first();

        $this->authorize('administrateUsers', $association);

        SEOMeta::setTitle('Nutzerübersicht');

        return view('association.users.index', [
            'association' => $association,
        ]);
    }

    public function create(): View
    {
        $association = user()->associations->first();

        $this->authorize('administrateUsers', $association);

        SEOMeta::setTitle('Nutzer erstellen');

        return view('association.users.create', [
            'association' => $association,
        ]);
    }

    /**
     * @param  Request  $request
     *
     * @throws AuthorizationException
     */
    public function store(AssociationUserRequest $request): RedirectResponse
    {
        $association = user()->associations->first();

        $this->authorize('administrateUsers', $association);

        $request->createAssociationUser($association);

        notify(__('notifications.employee.created'));

        return redirect()->route('associations.users.index');
    }

    public function edit(User $user): View
    {
        $association = user()->associations->first();

        $this->authorize('administrateUsers', $association);
        $this->authorize('update', $user);

        SEOMeta::setTitle('Nutzer bearbeiten');

        return view('association.users.edit', [
            'association' => $association,
            'user' => $user,
        ]);
    }

    /**
     * @param  Request  $request
     *
     * @throws AuthorizationException
     */
    public function update(User $user, AssociationUserRequest $request): RedirectResponse
    {
        $association = user()->associations->first();

        $this->authorize('administrateUsers', $association);
        $this->authorize('update', $user);
        abort_if($user->getAssociationRole($association) === AssociationRoleEnum::ADMIN, 403);

        $request->updateAssociationUser($association, $user);

        notify(__('notifications.employee.updated'));

        return redirect()->route('associations.users.index');
    }
}
