<?php

namespace App\Http\Controllers\PharmacyCovidVaccination;

use App\Enums\Vaccinate\AgeGroupEnum;
use App\Enums\Vaccinate\CovidVaccinationStepEnum;
use App\Enums\Vaccinate\GenderEnum;
use App\Enums\Vaccinate\VaccinationStatus;
use App\Http\Controllers\Controller;
use App\Pharmacy;
use App\Vaccination;
use Artesaos\SEOTools\Facades\SEOMeta;
use Carbon\Carbon;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PersonalDataController extends Controller
{
    public function edit(Pharmacy $pharmacy, Vaccination $vaccination)
    {
        $this->authorize('update', $vaccination->covidVaccination);

        SEOMeta::setTitle(trans('messages.vaccinate'));

        if ($vaccination->status == VaccinationStatus::FINISHED) {
            return redirect()->route('pharmacies.vaccinate-covid.finished', [$pharmacy, $vaccination]);
        }

        if ($vaccination->step < CovidVaccinationStepEnum::PERSONAL_DATA) {
            return \redirect()->route(CovidVaccinationStepEnum::getRedirectRoute($vaccination), [$pharmacy, $vaccination]);
        }

        return view('pharmacy.vaccinateCovid.personal_data', [
            'pharmacy' => $vaccination->pharmacy,
            'vaccination' => $vaccination,
        ]);
    }

    /**
     * @return RedirectResponse
     *
     * @throws AuthorizationException
     */
    public function update(Pharmacy $pharmacy, Vaccination $vaccination, Request $request)
    {
        $this->authorize('update', $vaccination->covidVaccination);

        $validated = $request->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'gender' => ['required', 'in:'.GenderEnum::getAll()->implode(',')],
            'birthdate' => ['required', 'date', 'before_or_equal:'.now()->subYears(12), 'after:'.now()->subYears(149)->subDay()->format('d.m.Y')],
            'optional_address_line' => ['nullable', 'string', 'max:255'],
            'street' => ['required', 'string', 'max:255'],
            'house_number' => ['required', 'string', 'max:255'],
            'postcode' => ['required', 'digits:5'],
            'city' => ['required', 'string', 'max:255'],
        ]);

        // calculate patient age [DAVMAP-170]
        $validated['age'] = Carbon::parse($validated['birthdate'])->age;

        DB::beginTransaction();

        try {
            $vaccination->vaccinationPatient->update($validated);

            $vaccination->update([
                'gender' => $validated['gender'],
                'age_group' => AgeGroupEnum::rangeForAge(Carbon::parse($validated['birthdate'])->age),
            ]);

            if ($vaccination->step < CovidVaccinationStepEnum::VACCINATION_DATA) {
                $vaccination->update(['step' => CovidVaccinationStepEnum::VACCINATION_DATA]);
            }

            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }

        return response()->redirectToRoute('pharmacies.vaccinate-covid.vaccination-data', [$pharmacy, $vaccination]);
    }

    private function resetVaccinationData(Vaccination $vaccination)
    {
        $vaccination->covidVaccination->update([
            'vaccination_type' => null,
        ]);

        $vaccination->update([
            'duration' => null,
            'pharmaceutical_id' => null,
            'batch_number' => null,
            'status' => VaccinationStatus::DRAFT,
            'reasons_to_abort' => null,
            'notes' => null,
        ]);
    }
}
