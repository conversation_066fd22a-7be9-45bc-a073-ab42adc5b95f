<?php

namespace App\Http\Controllers\PharmacyCovidVaccination;

use App\Enums\Vaccinate\CovidVaccinationStepEnum;
use App\Enums\Vaccinate\VaccinationStatus;
use App\Http\Controllers\Controller;
use App\Pharmacy;
use App\Vaccination;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class StartController extends Controller
{
    public function edit(Pharmacy $pharmacy, Vaccination $vaccination)
    {
        $this->authorize('update', $vaccination->covidVaccination);

        SEOMeta::setTitle(trans('messages.vaccinate'));

        if ($vaccination->status == VaccinationStatus::FINISHED) {
            return redirect()->route('pharmacies.vaccinate-covid.finished', [$pharmacy, $vaccination]);
        }

        if ($vaccination->step < CovidVaccinationStepEnum::START) {
            return \redirect()->route(CovidVaccinationStepEnum::getRedirectRoute($vaccination), [$pharmacy, $vaccination]);
        }

        return view('pharmacy.vaccinateCovid.start', [
            'pharmacy' => $vaccination->pharmacy,
            'vaccination' => $vaccination,
            'accepted' => $vaccination->step != CovidVaccinationStepEnum::START,
        ]);
    }

    /**
     * @return RedirectResponse
     *
     * @throws AuthorizationException
     */
    public function update(Pharmacy $pharmacy, Vaccination $vaccination, Request $request)
    {
        $this->authorize('update', $vaccination->covidVaccination);

        $request->validate([
            'information_lefleat' => ['accepted'],
            'medical_history' => ['accepted'],
            'consent_lefleat' => ['accepted'],
        ]);

        if ($vaccination->step < CovidVaccinationStepEnum::PERSONAL_DATA) {
            $vaccination->update(['step' => CovidVaccinationStepEnum::PERSONAL_DATA]);
        }

        return response()->redirectToRoute('pharmacies.vaccinate-covid.personal-data', [$pharmacy, $vaccination]);
    }
}
