<?php

namespace App\Http\Controllers;

use App\BillingAddress;
use Illuminate\Auth\Access\AuthorizationException;

class UserBillingAddressesController extends Controller
{
    public function index()
    {
        $this->authorize('viewAny', BillingAddress::class);

        return view('user.billing-addresses');
    }

    /**
     * @throws AuthorizationException
     */
    public function upsert(?BillingAddress $billingAddress = null)
    {
        $this->authorize('create', BillingAddress::class);
        if ($billingAddress) {
            $this->authorize('update', $billingAddress);
        }

        return view('user.billing-addresses-upsert', compact('billingAddress'));
    }
}
