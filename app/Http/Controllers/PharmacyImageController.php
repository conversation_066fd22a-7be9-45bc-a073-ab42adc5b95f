<?php

namespace App\Http\Controllers;

use App\ApprovableChange;
use App\Pharmacy;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Facades\Storage;

class PharmacyImageController extends Controller
{
    /**
     * @throws AuthorizationException
     */
    public function logo(Pharmacy $pharmacy): string
    {
        $this->authorize('view', $pharmacy);

        abort_unless(isset($pharmacy->logo->path), 404);

        return Storage::get($pharmacy->logo->path);
    }

    /**
     * @throws AuthorizationException
     */
    public function image(Pharmacy $pharmacy): string
    {
        $this->authorize('view', $pharmacy);

        return Storage::get($pharmacy->images()->first()->path);
    }

    public function approval(ApprovableChange $approvableChange)
    {
        abort_unless($approvableChange->type == 'image', 404);
        $this->authorize('update', $approvableChange->pharmacy);

        return response()->file(Storage::disk('approval')->path($approvableChange->change['path']));
    }
}
