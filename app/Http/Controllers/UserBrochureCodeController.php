<?php

namespace App\Http\Controllers;

use App\BrochureCode;
use App\Rules\BrochureCodeExists;
use App\Rules\BrochureCodeFormat;
use App\Rules\BrochureCodeNotTaken;
use Illuminate\Http\Request;

class UserBrochureCodeController extends Controller
{
    public function edit()
    {
        $user = user();

        abort_if($user->brochureCode !== null, 403);

        return view('user.editBrochureCode');
    }

    public function update(Request $request)
    {
        $user = user();

        $validated = $request->validate([
            'code' => ['required', 'string', new BrochureCodeFormat, new BrochureCodeExists, new BrochureCodeNotTaken],
        ]);

        $brochureCode = BrochureCode::findForCode($validated['code']);
        $brochureCode->update([
            'user_id' => $user->id,
        ]);

        return redirect()->route('home');
    }
}
