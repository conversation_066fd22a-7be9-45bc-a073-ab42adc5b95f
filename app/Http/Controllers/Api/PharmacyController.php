<?php

namespace App\Http\Controllers\Api;

use App\Domains\Subscription\Application\FeatureAccess\DigitalRepresentationFeatureAccess;
use App\Enums\PharmacyRoleEnum;
use App\Pharmacy;
use App\QueryBuilder\DistanceFilter;
use App\Settings\AppSettings;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Laravel\Passport\Exceptions\MissingScopeException;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class PharmacyController extends ClientCredentialsController
{
    public $token_type;

    public function __construct()
    {
        $this->token_type = collect($this->getTokenModel()->scopes)
            ->filter(fn ($scope) => array_key_exists($scope, config('auth.api.resources.pharmacies')))
            ->first();
    }

    public function index(Request $request, AppSettings $appSettings)
    {
        $pharmacies = QueryBuilder::for(Pharmacy::class) // @phpstan-ignore-line
            ->allowedFilters([
                AllowedFilter::exact('corona_rapid_test'),
                AllowedFilter::exact('ibm_id'),
                AllowedFilter::custom('near', new DistanceFilter),
                AllowedFilter::callback(
                    'updated_before',
                    fn (Builder $query, $end) => $query->where('updated_at', '<', Carbon::parse($end))
                ),
                AllowedFilter::callback(
                    'updated_after',
                    fn (Builder $query, $start) => $query->where('updated_at', '>', Carbon::parse($start))
                ),
            ])
            ->when($this->token_type == 'pharmacies-ngda', function ($q) {
                return $q
                    ->whereHas('telematicsId')
                    ->where('export_added_value_to_gematik', true);
            })
            ->when(
                $this->token_type != 'pharmacies-ignore-tos' && Carbon::now() > $appSettings->terms_of_use_deadline,
                fn ($q) => $q->withFeatureAccess(DigitalRepresentationFeatureAccess::class) // @phpstan-ignore-line
            )
            ->allowedSorts(['distance', 'name'])
            ->with($this->getLazyLoadedAttributes())
            ->paginate($this->getPerPage());

        return $this->getResource()::collection($pharmacies);
    }

    public function show(Pharmacy $pharmacy, AppSettings $appSettings)
    {
        abort_if(! $pharmacy->isActiveForApi() && Carbon::now() > $appSettings->terms_of_use_deadline, 403);

        if ($this->token_type == 'pharmacies-ngda') {
            abort_if(! $pharmacy->telematicsId, 403);
            abort_if(! $pharmacy->export_added_value_to_gematik, 403);
        }

        $pharmacy->load($this->getLazyLoadedAttributes());
        $class = $this->getResource();

        return new $class($pharmacy);
    }

    private function getResource()
    {
        throw_unless($this->token_type, MissingScopeException::class, 'pharmacy-index-type');

        return config('auth.api.resources.pharmacies.'.$this->token_type);
    }

    private function getLazyLoadedAttributes(): array
    {
        return [
            'businessHours',
            'languages',
            'pharmacyType',
            'publicTransportStations',
            'users' => function ($q) {
                return $q->wherePivot('role_name', '=', PharmacyRoleEnum::OWNER)->with('pharmacies');
            },
            'pharmacyImages.pharmacy',
        ];
    }
}
