<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\PharmacyImageResource;
use App\PharmacyImage;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class PharmacyImageController extends ClientCredentialsController
{
    public function index(Request $request)
    {
        $images =
            QueryBuilder::for(PharmacyImage::class)
                ->allowedFilters([
                    AllowedFilter::callback('type', function (Builder $query, $type) {
                        if ($type == 'logo') {
                            return $query->where('is_logo', true);
                        }

                        if ($type == 'header') {
                            return $query->where('is_logo', false);
                        }

                        throw ValidationException::withMessages(['type' => 'Type needs to be header or logo']);
                    }),
                ])
                ->with('pharmacy')
                ->paginate($this->getPerPage());

        return PharmacyImageResource::collection($images);
    }

    public function show(PharmacyImage $pharmacyImage)
    {
        return new PharmacyImageResource($pharmacyImage);
    }

    public function image(PharmacyImage $pharmacyImage)
    {
        return response()->file(Storage::path($pharmacyImage->path));
    }
}
