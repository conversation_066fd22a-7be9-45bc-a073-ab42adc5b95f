<?php

namespace App\Http\Controllers;

use App\Enums\WebchatFolder;
use App\Helper\OIDCHelper;
use App\Helper\PharmacySessionHelper;
use App\Pharmacy;
use App\Support\FamedlyApi;
use App\User;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use Laravel\Pennant\Feature;
use OpenIDConnectClient\AccessToken;

class ChatController extends Controller
{
    public function chat()
    {
        abort_unless(user()->can('chat', [user(), currentPharmacy()]), 403);

        if (! user()->can('accessChat', currentPharmacy())
            && user()->can('activateChat', currentPharmacy())) {
            return redirect(route('pharmacies.edit', ['pharmacy' => currentPharmacy()]).'?#activate_chat');
        }

        return view('chat');
    }

    public function matrixBootstrap(Request $request, ?Pharmacy $pharmacy = null): RedirectResponse|Redirector|View
    {
        /** @var User $user */
        $user = user();

        if ($pharmacy && $pharmacy->id !== currentPharmacy()?->id) {
            PharmacySessionHelper::set($pharmacy);
        }

        /** @var Pharmacy $pharmacy */
        $pharmacy = currentPharmacy();

        if ($user->matrix_bootstrap_done) {
            return redirect(route('pharmacies.chat'));
        }

        if ($user->cannot('chat', [$user, $pharmacy]) || ! $pharmacy->hasPatientChatEnabled()) {
            return redirect(route('pharmacies.edit', ['pharmacy' => $pharmacy]).'?#activate_chat');
        }

        return view('matrix-bootstrap');
    }

    public function auth(): string
    {
        $this->authorize('chat', [user(), currentPharmacy()]);

        return app(FamedlyApi::class)->generateJWTForUser(user(), false);
    }

    public function oauth(): ?AccessToken
    {
        $this->authorize('chat', [user(), currentPharmacy()]);

        return app(OIDCHelper::class)->token();
    }

    public function app(string $path = 'index.html'): ResponseFactory|Response
    {

        $disk = 'webchat';

        $folder = Feature::active(\App\Features\WebChatLatestVersion::class) ? WebchatFolder::LATEST->value : WebchatFolder::STABLE->value;
        $fullPath = $folder.'/'.$path;

        /** @var ?string $contents */
        $contents = Cache::tags(['webchat-files-'.$folder])->remember($fullPath, 60 * 60 * 24, function () use ($disk, $fullPath) {
            return Storage::disk($disk)->get($fullPath);
        });

        abort_if($contents === null, 404);

        if ($path === 'index.html') {
            $contents = str_replace(
                '<base href="/">',
                '<base href="/messenger/app/">',
                $contents
            );
        }

        return response($contents, 200, [
            'Content-Type' => Storage::disk('webchat')->mimeType($fullPath),
        ]);
    }
}
