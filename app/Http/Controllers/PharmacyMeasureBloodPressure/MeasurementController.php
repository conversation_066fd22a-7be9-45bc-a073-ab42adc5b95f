<?php

namespace App\Http\Controllers\PharmacyMeasureBloodPressure;

use App\Enums\MeasureBloodPressure\MeasureBloodPressureStepEnum;
use App\Enums\PharmaceuticalService\PharmaceuticalServiceStatus;
use App\Http\Controllers\Controller;
use App\PharmaceuticalService;
use App\Pharmacy;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MeasurementController extends Controller
{
    public function edit(Pharmacy $pharmacy, PharmaceuticalService $pharmaceuticalService)
    {
        $this->authorize('update', $pharmaceuticalService->measureBloodPressurePatient);

        SEOMeta::setTitle(trans('messages.measureBloodPressure'));

        if ($pharmaceuticalService->status == PharmaceuticalServiceStatus::FINISHED) {
            return redirect()->route('pharmacies.pharmaceutical-services.measure-blood-pressures.finished', [$pharmacy, $pharmaceuticalService]);
        }

        if ($pharmaceuticalService->step < MeasureBloodPressureStepEnum::MEASUREMENT) {
            return \redirect()->route(MeasureBloodPressureStepEnum::getRedirectRoute($pharmaceuticalService), [$pharmacy, $pharmaceuticalService]);
        }

        return view('pharmacy.measureBloodPressure.measurement', [
            'pharmacy' => $pharmaceuticalService->pharmacy,
            'pharmaceuticalService' => $pharmaceuticalService,
            'accepted' => $pharmaceuticalService->step != MeasureBloodPressureStepEnum::MEASUREMENT,
        ]);
    }

    /**
     * @return RedirectResponse
     *
     * @throws AuthorizationException
     */
    public function update(Pharmacy $pharmacy, PharmaceuticalService $pharmaceuticalService, Request $request)
    {
        $this->authorize('update', $pharmaceuticalService->measureBloodPressurePatient);

        $validated = $request->validate([
            'heart_rate_left_or_right_arm' => ['required'],
            'heart_rate_upper_arm_or_wrist_while_sitting' => ['required'],
            'first_measure_systolic' => ['required'],
            'first_measure_diastolic' => ['required'],
            'first_measure_heart_rate' => ['required'],
            'second_measure_systolic' => ['required'],
            'second_measure_diastolic' => ['required'],
            'second_measure_heart_rate' => ['required'],
            'third_measure_systolic' => ['required'],
            'third_measure_diastolic' => ['required'],
            'third_measure_heart_rate' => ['required'],
            'arrhythmias' => ['required'],
        ]);

        $validated['average_systolic'] = 0;
        $validated['average_diastolic'] = 0;
        $validated['average_heart_rate'] = 0;
        $validated['recommended_solutions'] = null;

        if (! empty($validated['second_measure_systolic']) && ! empty($validated['third_measure_systolic'])) {
            $validated['average_systolic'] = ($validated['second_measure_systolic'] + $validated['third_measure_systolic']) / 2;
            $validated['average_diastolic'] = ($validated['second_measure_diastolic'] + $validated['third_measure_diastolic']) / 2;
            $validated['average_heart_rate'] = ($validated['second_measure_heart_rate'] + $validated['third_measure_heart_rate']) / 2;
        }

        if (
            (
                $validated['average_systolic'] > 130
                || $validated['average_diastolic'] > 80
            )
            && $pharmaceuticalService->pharmaceuticalServicePatient->age <= 64
        ) {
            $validated['recommended_solutions'][] = \App\Enums\MeasureBloodPressure\MeasureBloodPressureRecommendSolutionsEnum::MAKE_APPOINTMENT_WITHIN_4_WEEKS;
        }

        if (
            (
                $validated['average_systolic'] > 140
                || $validated['average_diastolic'] > 80
            )
            && $pharmaceuticalService->pharmaceuticalServicePatient->age >= 65
        ) {
            $validated['recommended_solutions'][] = \App\Enums\MeasureBloodPressure\MeasureBloodPressureRecommendSolutionsEnum::MAKE_APPOINTMENT_WITHIN_4_WEEKS;
        }

        if (
            (
                $validated['average_systolic'] < 120
                || $validated['average_diastolic'] < 70
            )
            && $pharmaceuticalService->pharmaceuticalServicePatient->age <= 64
        ) {
            $validated['recommended_solutions'][] = \App\Enums\MeasureBloodPressure\MeasureBloodPressureRecommendSolutionsEnum::INFORM_YOUR_DOCTOR;
        }

        if (
            (
                $validated['average_systolic'] < 120
                || $validated['average_diastolic'] < 70
            )
            && $pharmaceuticalService->pharmaceuticalServicePatient->age >= 65
        ) {
            $validated['recommended_solutions'][] = \App\Enums\MeasureBloodPressure\MeasureBloodPressureRecommendSolutionsEnum::INFORM_YOUR_DOCTOR;
        }

        if (
            (
                ($validated['average_systolic'] >= 120 && $validated['average_systolic'] <= 130)
                || ($validated['average_diastolic'] >= 70 && $validated['average_diastolic'] <= 80)
            )
            && $pharmaceuticalService->pharmaceuticalServicePatient->age <= 64
        ) {
            $validated['recommended_solutions'][] = \App\Enums\MeasureBloodPressure\MeasureBloodPressureRecommendSolutionsEnum::CONTINUE_TO_CHECK;
        }

        if (
            (
                ($validated['average_systolic'] >= 120 && $validated['average_systolic'] <= 140)
                || ($validated['average_diastolic'] >= 70 && $validated['average_diastolic'] <= 80)
            )
            && $pharmaceuticalService->pharmaceuticalServicePatient->age >= 65
        ) {
            $validated['recommended_solutions'][] = \App\Enums\MeasureBloodPressure\MeasureBloodPressureRecommendSolutionsEnum::CONTINUE_TO_CHECK;
        }

        // checking recommended solutions, take the lowest solution and make it to an array
        $validated['recommended_solutions'] = is_array($validated['recommended_solutions']) ? (array) min(array_unique($validated['recommended_solutions'])) : null;

        DB::beginTransaction();

        try {
            $pharmaceuticalService->measureBloodPressurePatient->update($validated);

            if ($pharmaceuticalService->step < MeasureBloodPressureStepEnum::OVERVIEW) {
                $pharmaceuticalService->update(['step' => MeasureBloodPressureStepEnum::OVERVIEW]);
            }

            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }

        return response()->redirectToRoute('pharmacies.pharmaceutical-services.measure-blood-pressures.overview', [$pharmacy, $pharmaceuticalService]);
    }
}
