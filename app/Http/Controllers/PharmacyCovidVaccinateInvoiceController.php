<?php

namespace App\Http\Controllers;

use App\CovidVaccinationInvoice;
use App\Pharmacy;

class PharmacyCovidVaccinateInvoiceController extends Controller
{
    public function index(Pharmacy $pharmacy)
    {
        $this->authorize('viewAny', [CovidVaccinationInvoice::class, $pharmacy]);

        return view('pharmacy.vaccinateCovid.invoice.index', [
            'pharmacy' => $pharmacy,
        ]);
    }

    public function download(Pharmacy $pharmacy, CovidVaccinationInvoice $covidVaccinationInvoice)
    {
        $this->authorize('view', [CovidVaccinationInvoice::class, $pharmacy]);

        abort_unless($covidVaccinationInvoice->pharmacy->is($pharmacy), 403);

        $media = $covidVaccinationInvoice->getFirstMedia('invoice');

        $downloadHeaders = [
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Content-Type' => $media->mime_type,
            'Content-Length' => $media->size,
            'Content-Disposition' => 'attachment; filename="abrechnung_'.$covidVaccinationInvoice->start_date->format('d-m-Y').'_'.$covidVaccinationInvoice->end_date->format('d-m-Y').'.pdf"',
            'Pragma' => 'public',
        ];

        return response()->stream(function () use ($media) {
            $stream = $media->stream();

            fpassthru($stream);

            if (is_resource($stream)) {
                fclose($stream);
            }
        }, 200, $downloadHeaders);
    }
}
