<?php

namespace App\Http\Controllers;

use App\Pharmacy;
use Illuminate\Support\Facades\Storage;

class PharmacyImageNovaController extends Controller
{
    public function logo(Pharmacy $pharmacy)
    {
        if (! $pharmacy->logo) {
            return null;
        }

        return Storage::response($pharmacy->logo->path);
    }

    public function image(Pharmacy $pharmacy)
    {
        $imagePath = $pharmacy->images()->first()->path ?? null;

        return $imagePath ? Storage::response($imagePath) : null;
    }
}
