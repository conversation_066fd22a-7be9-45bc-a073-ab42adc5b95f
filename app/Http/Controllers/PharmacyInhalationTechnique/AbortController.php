<?php

namespace App\Http\Controllers\PharmacyInhalationTechnique;

use App\Actions\AnonymizePharmaceuticalServiceAction;
use App\Enums\InhalationTechnique\InhalationTechniqueStepEnum;
use App\Enums\InhalationTechnique\ReasonToAbortEnum;
use App\Enums\PharmaceuticalService\PharmaceuticalServiceStatus;
use App\Http\Controllers\Controller;
use App\PharmaceuticalService;
use App\Pharmacy;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;

class AbortController extends Controller
{
    public function edit(Pharmacy $pharmacy, PharmaceuticalService $pharmaceuticalService, $reasons, Request $request)
    {
        $this->authorize('update', $pharmaceuticalService->inhalationTechniquePatient);

        SEOMeta::setTitle(trans('messages.inhalationTechnique'));

        $reasons = array_map('intval', explode(',', $reasons));

        if (in_array(ReasonToAbortEnum::ABORT, $reasons)) {
            $pharmaceuticalService->delete();

            return view('pharmacy.inhalationTechnique.abort', [
                'pharmacy' => $pharmacy,
            ]);
        }

        $pharmaceuticalService->reasons_to_abort = $reasons;
        $pharmaceuticalService->step = InhalationTechniqueStepEnum::FINISHED;
        $pharmaceuticalService->status = PharmaceuticalServiceStatus::FINISHED;
        $pharmaceuticalService->save();

        app(AnonymizePharmaceuticalServiceAction::class)->execute($pharmaceuticalService);

        return redirect()->route('pharmacies.pharmaceutical-services.inhalation-techniques.finished', [
            'pharmacy' => $pharmacy,
            'pharmaceuticalService' => $pharmaceuticalService,
        ]);
    }
}
