<?php

namespace App\Domains\Association\Domain\Enums;

use App\Domains\Subscription\Application\AssociationFrameworkContracts\AssociationFrameworkContract;
use App\Domains\Subscription\Application\AssociationFrameworkContracts\BaseAssociationFrameworkContract;
use App\Domains\Subscription\Application\AssociationFrameworkContracts\NoAssociationFrameworkContract;
use App\Domains\Subscription\Application\AssociationFrameworkContracts\PlusAssociationFrameworkContract;
use Illuminate\Support\Collection;

enum AssociationFrameworkContractEnum: string
{
    case NoAssociationFrameworkContract = 'NoAssociationFrameworkContract';
    case BaseAssociationFrameworkContract = 'BaseAssociationFrameworkContract';
    case PlusAssociationFrameworkContract = 'PlusAssociationFrameworkContract';

    public function instance(): AssociationFrameworkContract
    {
        return match ($this) {
            self::NoAssociationFrameworkContract => app(NoAssociationFrameworkContract::class),
            self::BaseAssociationFrameworkContract => app(BaseAssociationFrameworkContract::class),
            self::PlusAssociationFrameworkContract => app(PlusAssociationFrameworkContract::class),
        };
    }

    public function label(): string
    {
        return match ($this) {
            self::NoAssociationFrameworkContract => 'kein Vertrag',
            self::BaseAssociationFrameworkContract => 'Basis',
            self::PlusAssociationFrameworkContract => 'Plus',
        };
    }

    /**
     * @return Collection<string, string>
     */
    public static function options(): Collection
    {
        return collect(self::cases())->mapWithKeys(fn (AssociationFrameworkContractEnum $item) => [
            $item->value => $item->label(),
        ]);
    }
}
