<?php

namespace App\Domains\ShiftPlan\Domain\Data;

use Livewire\Wireable;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Concerns\WireableData;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

#[MapName(SnakeCaseMapper::class)]
class ShiftPlanGroupData extends Data implements Wireable
{
    use WireableData;

    public function __construct(
        public string $name,
        public string $color,
        public int $shiftPlanId,
        public ?string $uuid = null,
        public ?int $sortOrder = null,
    ) {}
}
