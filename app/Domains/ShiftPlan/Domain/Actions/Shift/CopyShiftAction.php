<?php

namespace App\Domains\ShiftPlan\Domain\Actions\Shift;

use App\Domains\ShiftPlan\Domain\Data\ShiftData;
use App\Shift;
use Carbon\Carbon;

class CopyShiftAction
{
    public static function execute(ShiftData $data, int $groupUserId, Carbon $targetDay): Shift
    {
        $data->shiftPlanGroupUserId = $groupUserId;
        $data->startsAt = $targetDay->setTimeFrom($data->startsAt);
        $data->endsAt = $targetDay->copy()->setTimeFrom($data->endsAt);

        return CreateShiftAction::execute($data);
    }
}
