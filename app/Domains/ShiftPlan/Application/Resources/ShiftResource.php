<?php

namespace App\Domains\ShiftPlan\Application\Resources;

use App\Shift;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Shift */
class ShiftResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'plan_id' => $this->plan?->id,
            'plan_uuid' => $this->plan?->uuid,
            'starts_at' => $this->starts_at,
            'ends_at' => $this->ends_at,
            'duration_in_minutes' => $this->duration,
            'break_minutes' => $this->break_duration,
            'shift_plan_user_group_id' => $this->shift_plan_group_user_id,
            'assigned_user_id' => $this->planGroupUser?->user_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
