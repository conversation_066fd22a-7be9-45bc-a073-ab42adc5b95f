<?php

namespace App\Domains\ShiftPlan\Application\Policies;

use App\ShiftPlan;
use App\User;

class ShiftPlanPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ShiftPlan $shiftPlan): bool
    {
        return $user->owner()?->id === $shiftPlan->owner_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ShiftPlan $shiftPlan): bool
    {
        return true;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ShiftPlan $shiftPlan): bool
    {
        return $user->owner()?->id === $shiftPlan->owner_id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ShiftPlan $shiftPlan): bool
    {
        return $user->owner()?->id === $shiftPlan->owner_id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ShiftPlan $shiftPlan): bool
    {
        return $user->owner()?->id === $shiftPlan->owner_id;
    }
}
