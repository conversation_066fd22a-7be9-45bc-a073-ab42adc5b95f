<?php

namespace App\Domains\ShiftPlan\Application\Policies;

use App\ShiftPlanGroup;
use App\User;

class ShiftPlanGroupPolicy
{
    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, ShiftPlanGroup $shiftPlanGroup): bool
    {
        return $user->owner()?->id === $shiftPlanGroup->plan?->owner_id;
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, ShiftPlanGroup $shiftPlanGroup): bool
    {
        return true;
    }

    public function delete(User $user, ShiftPlanGroup $shiftPlanGroup): bool
    {
        return true;
    }

    public function restore(User $user, ShiftPlanGroup $shiftPlanGroup): bool
    {
        return true;
    }

    public function forceDelete(User $user, ShiftPlanGroup $shiftPlanGroup): bool
    {
        return true;
    }

    public function attachUser(User $user, ShiftPlanGroup $shiftPlanGroup, User $userToAttach): bool
    {
        return $shiftPlanGroup->plan?->owner?->getAllOwnerRelatedUsers()->contains('id', $userToAttach->id) ?? false;
    }
}
