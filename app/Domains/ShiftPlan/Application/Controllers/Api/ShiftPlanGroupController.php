<?php

namespace App\Domains\ShiftPlan\Application\Controllers\Api;

use App\Domains\ShiftPlan\Application\Queries\IndexShiftPlanGroupQuery;
use App\Domains\ShiftPlan\Application\Resources\ShiftPlanGroupResource;
use App\Http\Controllers\Controller;
use App\Http\Requests\IndexShiftPlanGroupRequest;
use Illuminate\Http\Resources\Json\JsonResource;

class ShiftPlanGroupController extends Controller
{
    public function index(IndexShiftPlanGroupRequest $request, IndexShiftPlanGroupQuery $query): JsonResource
    {
        return ShiftPlanGroupResource::collection($query->get());
    }
}
