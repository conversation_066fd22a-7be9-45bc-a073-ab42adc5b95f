<?php

namespace App\Domains\ShiftPlan\Application\Controllers\Api;

use App\Domains\ShiftPlan\Application\Queries\IndexShiftQuery;
use App\Domains\ShiftPlan\Application\Resources\ShiftResource;
use App\Http\Controllers\Controller;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;

class ShiftController extends Controller
{
    public function index(IndexShiftQuery $query): Response|JsonResource
    {
        return ShiftResource::collection($query->get());
    }
}
