<?php

namespace App\Domains\Subscription\Application\Discounts;

use App\Domains\Subscription\Application\Settings\Discounts\CardLinkPartnerPharmacyDiscountSetting;
use App\Domains\Subscription\Application\Settings\Discounts\StripeDiscountSetting;
use App\Domains\Subscription\Application\Settings\Products\CardLinkProductSetting;
use App\Domains\Subscription\Application\StripeProducts\StripeProduct;
use App\Pharmacy;

class CardLinkPartnerPharmacyStripeDiscount extends StripeDiscount
{
    /** @param StripeProduct[] $stripeProducts */
    public function shouldBeApplied(Pharmacy $pharmacy, array $stripeProducts): bool
    {
        if (! collect($stripeProducts)->contains(fn (StripeProduct $product) => $product->getStripeProductId() === app(CardLinkProductSetting::class)->product_id)) {
            return false;
        }

        return ! is_null($pharmacy->cardLinkPartnerPharmacy);
    }

    public function getSettings(): StripeDiscountSetting
    {
        return app(CardLinkPartnerPharmacyDiscountSetting::class);
    }
}
