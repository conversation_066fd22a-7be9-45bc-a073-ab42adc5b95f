<?php

namespace App\Domains\Subscription\Application\AssociationFrameworkContracts;

use App\Domains\Payment\Domain\Data\StripePublicRepresentationData;
use App\Domains\Subscription\Application\StripeProducts\StripeProduct;

abstract class AssociationFrameworkContract
{
    /** @return array<class-string<StripeProduct>> **/
    abstract public function getAvailableAddons(): array;

    /** @return array<class-string<StripeProduct>> **/
    abstract public function getIncludedProducts(): array;

    abstract public function getRequiredBaseProduct(): ?StripeProduct;

    abstract public function getPublicRepresentationData(): StripePublicRepresentationData;
}
