<?php

namespace App\Domains\Subscription\Application\StripeProducts\AddOns;

use App\Domains\Payment\Domain\Data\StripeProductFrontendTextData;
use App\Domains\Payment\Domain\Data\StripePublicRepresentationData;
use App\Domains\Subscription\Application\Settings\Products\ShiftPlanProductSetting;
use App\Domains\Subscription\Application\Settings\Products\StripeProductSetting;
use App\Domains\Subscription\Application\StripeProducts\StripeProduct;
use App\Domains\Subscription\Domain\Enums\StripeProductTypeEnum;
use App\Pharmacy;

class ShiftPlanStripeProduct extends StripeProduct
{
    public static StripeProductTypeEnum $type = StripeProductTypeEnum::ADD_ON;

    public static bool $isProrated = true;

    public function getSettings(): StripeProductSetting
    {
        return app(ShiftPlanProductSetting::class);
    }

    public function getPublicRepresentationData(Pharmacy $pharmacy): StripePublicRepresentationData
    {
        return new StripePublicRepresentationData(
            class: self::class,
            price: $this->getOneTimeStripePrice($pharmacy)->price,
            text: new StripeProductFrontendTextData(
                name: 'Dienstplan',
                description: 'Verwalten Sie u. a. Arbeitszeiten, Schichten und Urlaube für alle Mitarbeitenden Ihrer Apotheke(n).',
                features: [],
            )
        );
    }
}
