<?php

namespace App\Domains\Subscription\Application\Jobs;

use App\Domains\Payment\Application\WebhookHandlers\HasConsequencesText;
use App\Integrations\IaIntegration;
use App\Integrations\IntegrationTypeEnum;
use App\Pharmacy;
use App\Processes\OffboardIA;
use App\Processes\Payloads\OffboardIAPayload;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\Attributes\WithoutRelations;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use RuntimeException;

class OffboardIaJob implements HasConsequencesText, ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $uniqueFor = 60;

    public int $tries = 3;

    public function uniqueId(): string
    {
        return $this->pharmacy->uuid ?? throw new RuntimeException('Pharmacy UUID not found');
    }

    public function __construct(
        #[WithoutRelations]
        public Pharmacy $pharmacy,
    ) {
        $this->afterCommit();
    }

    public function handle(): void
    {
        try {
            $iaIntegration = $this->pharmacy->getIntegration(IntegrationTypeEnum::IhreApotheken)?->settings;
            if (! $iaIntegration) {
                return;
            }
            assert($iaIntegration instanceof IaIntegration);
            $payload = new OffboardIAPayload($this->pharmacy, $iaIntegration);

            $process = app(OffboardIA::class);
            $process->run($payload);

        } catch (Exception $exception) {
            $exception = new RuntimeException('Couldnt offboard IA for pharmacy: '.$this->pharmacy->id, previous: $exception);
            report($exception);
            throw $exception;
        }
    }

    public static function getConsequencesText(): string
    {
        return 'Ihre-Apotheken Integration wird gelöscht.';
    }
}
