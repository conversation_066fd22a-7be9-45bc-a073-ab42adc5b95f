<?php

namespace App\Domains\Subscription\Application\Settings\Products;

use App\Domains\Subscription\Application\StripeProducts\OneTimePurchases\CardLinkPacketMStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\StripeProduct;

class CardLinkPacketMProductSetting extends StripeProductSetting
{
    public static function getStripeProduct(): StripeProduct
    {
        return app(CardLinkPacketMStripeProduct::class);
    }
}
