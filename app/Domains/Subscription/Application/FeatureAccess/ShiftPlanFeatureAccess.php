<?php

namespace App\Domains\Subscription\Application\FeatureAccess;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Features\ShiftPlan;
use Illuminate\Database\Eloquent\Builder;
use Laravel\Pennant\Feature;
use RuntimeException;

class ShiftPlanFeatureAccess extends FeatureAccess
{
    protected function canUseNew(): bool
    {
        return Feature::for($this->pharmacy->ownerOrFail())->active(ShiftPlan::class) && $this->pharmacy->canUseProduct(BaseStripeProduct::class);
    }

    protected function scopeCanUseNew(Builder $builder): Builder
    {
        throw new RuntimeException('Not implemented');
    }
}
