<?php

namespace App\Domains\Payment\Application\Helper;

use App\Enums\PaymentMethod;
use App\Pharmacy;

class PharmacyPaymentMethodHelper
{
    public function getPreferedPaymentMethodForPharmacy(Pharmacy $pharmacy): ?string
    {
        return $pharmacy->defaultPaymentMethod()?->id;   // @phpstan-ignore-line paymentMethod has ID
    }

    /** @return array<string, mixed> */
    public function getSubscriptionPaymentDetails(Pharmacy $pharmacy): array
    {
        $paymentMethod = $this->getPreferedPaymentMethodForPharmacy($pharmacy);

        if ($pharmacy->selected_pm_type === PaymentMethod::Invoice) {
            return [
                'collection_method' => 'send_invoice',
                'days_until_due' => 14,
                'payment_settings' => [
                    'payment_method_types' => ['customer_balance', 'sepa_debit'],
                ],
            ];
        }

        if ($paymentMethod) {
            return [
                'collection_method' => 'charge_automatically',
                'payment_settings' => [
                    'payment_method_types' => ['sepa_debit'],
                ],
            ];
        }

        return [
            'collection_method' => 'send_invoice',
            'days_until_due' => 14,
            'payment_settings' => [
                'payment_method_types' => ['sepa_debit'],
            ],
        ];
    }
}
