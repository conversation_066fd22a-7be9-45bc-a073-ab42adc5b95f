<?php

namespace App\Domains\Payment\Application\WebhookHandlers;

use App\Pharmacy;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Laravel\Cashier\Events\WebhookReceived;
use RuntimeException;
use Stripe\Event;

abstract class StripeWebhookHandler implements ShouldQueue
{
    /** @var string[] */
    public array $jobs = [];

    public function handle(WebhookReceived $event): void
    {
        /** @phpstan-ignore-next-line */
        $stripeObject = Event::constructFrom($event->payload)->data?->object;

        if (! $stripeObject) {
            throw new RuntimeException('Stripe-Event konnte nicht dekonstruiert werden.');
        }

        $pharmacy = Pharmacy::query()->where('stripe_id', $stripeObject->customer)->firstOrFail();

        assert($pharmacy instanceof Pharmacy);

        foreach ($this->jobs as $pharmacyJob) {
            if (! is_subclass_of($pharmacyJob, ShouldQueue::class) || ! in_array(Dispatchable::class, class_uses($pharmacyJob))) {
                throw new RuntimeException("{$pharmacyJob} must implement ShouldQueue interface");
            }

            if (! method_exists($pharmacyJob, 'dispatch')) {
                throw new RuntimeException("{$pharmacyJob} must implement dispatch method");
            }

            $pharmacyJob::dispatch($pharmacy);
        }
    }
}
