<?php

namespace App\Domains\Payment\Application\WebhookHandlers;

use App\Pharmacy;
use Lara<PERSON>\Cashier\Events\WebhookReceived;
use RuntimeException;
use Stripe\Event;

class ChargeFailedHandler extends StripeWebhookHandler
{
    public function handle(WebhookReceived $event): void
    {
        /** @phpstan-ignore-next-line */
        $stripeObject = Event::constructFrom($event->payload)->data?->object;

        if (! $stripeObject) {
            throw new RuntimeException('Stripe-Event konnte nicht dekonstruiert werden.');
        }

        /** @var Pharmacy $pharmacy */
        $pharmacy = Pharmacy::query()->where('stripe_id', $stripeObject->customer)->firstOrFail();
        $pharmacy->deletePaymentMethods('sepa_debit');

        $pharmacy->selected_pm_type = null;
        $pharmacy->save();
    }
}
