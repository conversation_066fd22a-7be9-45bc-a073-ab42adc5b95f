<?php

namespace App\Domains\Payment\Domain\Actions\CheckoutSession;

use App\Domains\Payment\Domain\Data\CheckoutSessionData;
use App\Settings\SubscriptionSettings;
use Laravel\Cashier\Cashier;
use Stripe\Checkout\Session;

class CreateCheckoutSession
{
    public function execute(CheckoutSessionData $data): Session
    {
        return Cashier::stripe()->checkout->sessions->create([
            'mode' => $data->mode ?? 'setup',
            'currency' => SubscriptionSettings::currency(),
            'customer' => $data->pharmacy->stripeId(),
            'success_url' => $data->successUrl,
            'cancel_url' => $data->cancelUrl,
        ]);
    }
}
