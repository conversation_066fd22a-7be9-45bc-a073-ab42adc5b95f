<?php

namespace App\Processes\Tasks\ProcessAssociationMembershipChange;

use App\Actions\Subscription\CancelAllMembershipToDate;
use App\Pharmacy;
use App\Processes\Payloads\ProcessAssociationMembershipChangePayload;

class CancelMembership
{
    public function __construct(
        protected CancelAllMembershipToDate $cancelAllMembershipToDate,
    ) {}

    public function __invoke(ProcessAssociationMembershipChangePayload $payload, \Closure $next): ProcessAssociationMembershipChangePayload|bool
    {
        if (! $payload->associationMembershipChange->user()->withTrashed()->first()?->pharmacyProfile) {
            return $next($payload);
        }

        if (! $payload->associationMembershipChange->change_at) {
            return $next($payload);
        }

        $payload->associationMembershipChange->user()->withTrashed()->first()->pharmacies
            ->filter(fn (Pharmacy $pharmacy) => $pharmacy->hasSubscribedIfNeeded())
            ->each(fn (Pharmacy $pharmacy) => $this->cancelAllMembershipToDate->handle($pharmacy, $payload->associationMembershipChange->change_at));

        return $next($payload);
    }
}
