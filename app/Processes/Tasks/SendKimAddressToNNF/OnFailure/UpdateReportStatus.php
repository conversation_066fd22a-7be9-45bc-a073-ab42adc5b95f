<?php

namespace App\Processes\Tasks\SendKimAddressToNNF\OnFailure;

use App\Enums\KimAddressReportStatus;
use App\Processes\Payloads\SendKimAddressToNNFPayload;

class UpdateReportStatus
{
    public function __invoke(SendKimAddressToNNFPayload $payload, \Closure $next): SendKimAddressToNNFPayload
    {
        $payload->kimAddress->update(['report_status' => KimAddressReportStatus::REPORT_FAILED]);

        return $next($payload);
    }
}
