<?php

namespace App\Processes\Tasks\SendKimAddressToNNF;

use App\Enums\KimAddressReportStatus;
use App\Processes\Payloads\SendKimAddressToNNFPayload;
use Closure;

class MarkKimAddressAsReported
{
    public function __invoke(SendKimAddressToNNFPayload $payload, Closure $next): SendKimAddressToNNFPayload
    {
        $payload->kimAddress->update([
            'report_status' => KimAddressReportStatus::REPORTED,
            'reported_at' => now(),
        ]);

        return $next($payload);
    }
}
