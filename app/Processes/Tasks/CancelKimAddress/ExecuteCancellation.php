<?php

namespace App\Processes\Tasks\CancelKimAddress;

use App\Enums\KimAddressStatus;
use App\Processes\Payloads\CancelKimAddressPayload;
use Closure;

class ExecuteCancellation
{
    public function __invoke(CancelKimAddressPayload $payload, Closure $next): CancelKimAddressPayload
    {
        if ($payload->status() === KimAddressStatus::RESERVED->value) {
            $payload->kimAddress->forceDelete();

            return $next($payload);
        }

        $payload->kimAddress->fill([
            'status' => KimAddressStatus::CANCELED->value,
            'cancellation_reason' => $payload->reason,
            'cancellation_id' => $payload->cancellationId,
            'deactivated_at' => now(),
            'canceled_at' => now(),
        ]);

        $payload->kimAddress->save();

        return $next($payload);
    }
}
