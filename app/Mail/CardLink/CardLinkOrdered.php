<?php

namespace App\Mail\CardLink;

use App\CardLinkOrder;
use App\Data\CardLink\OrderInformationData;
use App\GedisaId;
use App\Pharmacy;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class CardLinkOrdered extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public User $user,
        public CardLinkOrder $cardLinkOrder,
    ) {
        $this->afterCommit();
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Ihre Bestellbestätigung zu CardLink',
        );
    }

    public function content(): Content
    {
        assert($this->cardLinkOrder->pharmacy instanceof Pharmacy);
        assert($this->cardLinkOrder->pharmacy->gedisaId instanceof GedisaId);

        $orderInformationData = OrderInformationData::fromCardLinkOrder($this->cardLinkOrder);
        assert($orderInformationData?->package !== null);

        return new Content(
            markdown: 'mail.card-link.ordered',
            with: [
                'customerName' => $this->user->greeting,
                'cardLinkOrder' => $this->cardLinkOrder,
                'package' => $orderInformationData->package,
                'gedisaId' => $this->cardLinkOrder->pharmacy->gedisaId->id,
            ],
        );
    }
}
