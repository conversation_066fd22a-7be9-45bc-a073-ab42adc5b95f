<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewSupportRequest extends Mailable
{
    use Queueable, SerializesModels;

    public string $name;

    public string $text;

    public string $email;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(string $name, string $subject, string $text, string $email)
    {
        $this->name = $name;
        $this->subject = $subject;
        $this->text = $text;
        $this->email = $email;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this
            ->replyTo($this->email)
            ->view('mail.plainText.newSupportRequest');
    }
}
