<?php

namespace App\Mail\IA;

use App\User;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class OnlineShopOrderedToIA extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public User $user,
        public Collection $pharmacies,
        public Carbon $orderedAt
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Neubestellung Online-Shop',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.ia.ordered-to-ia',
        );
    }
}
