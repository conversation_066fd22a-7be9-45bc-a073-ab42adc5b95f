<?php

namespace App\Mail;

use App\User;
use App\VaccinationImportInvoice;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class VaccinationImportAccountingFinishedMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;

    public $invoice;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(User $user, VaccinationImportInvoice $invoice)
    {
        $this->invoice = $invoice;
        $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this
            ->subject('Abrechnung erstellt')
            ->markdown('mail.vaccinationImportAccountingFinishedMail', [
                'actionUrl' => url(route('pharmacies.importVaccination.accounting', ['pharmacy' => $this->invoice->pharmacy])),
            ]);
    }
}
