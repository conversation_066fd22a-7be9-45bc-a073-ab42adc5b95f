<?php

namespace App;

use App\Enums\PharmaceuticalsTypeEnum;
use App\Enums\Vaccinate\AgeGroupEnum;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Pharmaceutical
 *
 * @mixin IdeHelperPharmaceutical
 */
class Pharmaceutical extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function vaccinations(): HasMany
    {
        return $this->hasMany(Vaccination::class);
    }

    public function getFullDisplayName(bool $withPzn = true): string
    {
        $name = $this->display_name ?? $this->name;
        if ($withPzn && $this->pzn) {
            $name .= ' ('.$this->pzn.')';
        }

        return $name;
    }

    public static function getPharmaceuticalsForVaccination(Vaccination $vaccination): Collection
    {
        if ($vaccination->influenzaVaccination) {
            return static::getPharmaceuticalsForInfluenzaVaccination($vaccination->influenzaVaccination);
        }

        return new Collection;
    }

    public static function getPharmaceuticalsForInfluenzaVaccination(InfluenzaVaccination $influenzaVaccination): Collection
    {
        $pharmaceuticals = static::query()->where('active', true);

        $type = match (true) {
            (bool) $influenzaVaccination->high_dose_vaccine_bottleneck => [PharmaceuticalsTypeEnum::VACCINE_INFLUENZA, PharmaceuticalsTypeEnum::VACCINE_INFLUENZA_HIGH_DOSE],
            (bool) $influenzaVaccination->use_high_dose_vaccine => [PharmaceuticalsTypeEnum::VACCINE_INFLUENZA_HIGH_DOSE],
            default => [PharmaceuticalsTypeEnum::VACCINE_INFLUENZA]
        };

        $pharmaceuticals->whereIn('type', $type);

        if (! $influenzaVaccination->high_dose_vaccine_bottleneck && $influenzaVaccination->vaccination?->age_group >= AgeGroupEnum::AGE_60_69) {
            $pharmaceuticals->where('min_agegroup', '>=', AgeGroupEnum::AGE_60_69);
        }

        return $pharmaceuticals->get();
    }
}
