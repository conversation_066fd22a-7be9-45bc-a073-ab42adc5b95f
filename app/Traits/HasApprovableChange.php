<?php

namespace App\Traits;

use App\Actions\GetGeolocationForPharmacyAction;
use App\ApprovableChange;
use App\Enums\PharmacyStatusEnum;
use App\Pharmacy;
use Illuminate\Support\Facades\Storage;

trait HasApprovableChange
{
    protected static $forceUpdate = false;

    protected static $addressUpdated = false;

    public function approvableChanges()
    {
        return $this->hasMany(ApprovableChange::class);
    }

    public static function bootHasApprovableChange()
    {
        static::updating(function (Pharmacy $item) {
            if (self::shouldStartApproval($item)) {
                $dirty = $item->getDirty();

                foreach ($dirty as $key => $dirt) {
                    if (array_key_exists($key, $item->needsApproval)) {
                        self::startApproval($item, $key, $dirt);
                    }
                }
            }
        });
    }

    private static function shouldStartApproval(Pharmacy $item): bool
    {
        if (self::$forceUpdate == true) {
            return false;
        }

        if (auth('staff')->check()) {
            return false;
        }

        return $item->verification_status == PharmacyStatusEnum::VERIFIED;
    }

    private static function startApproval(Pharmacy $item, $attribute, $value)
    {
        if (in_array($attribute, ['optional_address_line', 'street', 'house_number', 'postcode', 'city'])) {
            if (! self::$addressUpdated) {
                self::startAddressApproval($item);

                self::$addressUpdated = true;
            }
        } else {
            self::startPrimitiveApproval($item, $attribute, $value);
        }

        $item->{$attribute} = $item->getOriginal($attribute);
    }

    private static function startAddressApproval(Pharmacy $item)
    {
        $previousChange = $item->approvableChanges()->where('type', 'address')->first();

        $item->approvableChanges()->create([
            'attribute' => 'address',
            'type' => 'address',
            'change' => [
                'optional_address_line' => self::getAddressValue($previousChange, $item, 'optional_address_line'),
                'street' => self::getAddressValue($previousChange, $item, 'street'),
                'house_number' => self::getAddressValue($previousChange, $item, 'house_number'),
                'postcode' => self::getAddressValue($previousChange, $item, 'postcode'),
                'city' => self::getAddressValue($previousChange, $item, 'city'),
            ],
        ]);
    }

    private static function startPrimitiveApproval(Pharmacy $item, $attribute, $value)
    {
        $item->approvableChanges()->create([
            'attribute' => $attribute,
            'type' => $item->needsApproval[$attribute],
            'change' => [
                'newValue' => $value,
            ],
        ]);
    }

    public function startImageApproval($filePath, $isLogo)
    {
        $path = Storage::disk('approval')->putFile($this->getFilesDirectory(), $filePath);

        $this->approvableChanges()->create([
            'attribute' => $isLogo ? 'logo' : 'image',
            'type' => 'image',
            'change' => [
                'path' => $path,
            ],
        ]);
    }

    public function getPendingAttribute($attribute)
    {
        return $this->approvableChanges->where('attribute', $attribute)->first();
    }

    public function hasApprobableChanges()
    {
        return $this->approvableChanges->count() > 0;
    }

    /**
     * @param  string|array|ApprovableChange  $attribute
     */
    public function approveAttributeChange($attribute)
    {
        self::$forceUpdate = true;

        if (is_array($attribute)) {
            foreach ($attribute as $item) {
                $this->approveAttributeChange($item);
            }
        }

        $change = $this->getChangeModel($attribute);

        if (! $change) {
            return;
        }

        if ($change->type == 'image') {
            $this->approveImage($change);
        } elseif ($change->type == 'address') {
            $this->approveAddress($change);
        } else {
            $this->approveModelChangeApprovableChange($change);
        }

        self::$forceUpdate = false;
    }

    private function approveImage(ApprovableChange $change)
    {
        $this->storeImage(Storage::disk('approval')->path($change->change['path']), $change->attribute == 'logo');
        $change->delete();
    }

    private function approveModelChangeApprovableChange(ApprovableChange $change)
    {
        $change->pharmacy->update([
            $change->attribute => $change->change['newValue'],
        ]);

        $change->delete();
    }

    private function approveAddress(ApprovableChange $change)
    {
        $pharmacy = $change->pharmacy->fill([
            'optional_address_line' => $change->change['optional_address_line'],
            'street' => $change->change['street'],
            'house_number' => $change->change['house_number'],
            'postcode' => $change->change['postcode'],
            'city' => $change->change['city'],
        ]);

        $pharmacy = (new GetGeolocationForPharmacyAction)->execute($pharmacy);

        $pharmacy->save();

        $change->delete();
    }

    private function getChangeModel($attribute): ApprovableChange
    {
        if ($attribute instanceof ApprovableChange && $attribute->pharmacy->is($this)) {
            return $attribute;
        }

        return $this->approvableChanges->where('attribute', $attribute)->first();
    }

    private static function getAddressValue($previousChange, $item, $attribute)
    {
        if (! $previousChange) {
            return $item->{$attribute};
        }

        if ($previousChange->change[$attribute] != $item->{$attribute} && $item->{$attribute} != $item->getOriginal($attribute)) {
            return $item->{$attribute};
        }

        return $previousChange->change[$attribute];
    }
}
