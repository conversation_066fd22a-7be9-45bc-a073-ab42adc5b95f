<?php

namespace App\Traits;

use Illuminate\Support\Collection;

trait BackedEnumToArray
{
    public static function names(): Collection
    {
        return collect(array_column(self::cases(), 'name'));
    }

    public static function values(): Collection
    {
        return collect(array_column(self::cases(), 'value'));
    }

    public static function array(): Collection
    {
        return self::names()->combine(self::values());
    }

    public static function value(string $name): string|int
    {
        foreach (self::cases() as $status) {
            if ($name === $status->name) {
                return $status->value;
            }
        }

        throw new \ValueError("$name is not a valid backing value for enum ".self::class);
    }

    /**
     * @param  array<mixed, mixed>  $filter
     */
    public static function arrayOnly(array $filter = []): Collection
    {
        if ($filter === []) {
            return collect();
        }

        return self::names()->combine(self::values())->filter(function ($value, $key) use ($filter) {
            return in_array($key, $filter);
        });
    }
}
