<?php

namespace App\Traits;

use App\Association;
use App\User;

/**
 * Trait AssociationRules
 *
 * @mixin User
 */
trait AssociationRules
{
    public function hasAssociationRole($association, ?string $role = null): bool
    {
        $association = $this->getAssociation($association);

        if ($association === null) {
            return false;
        }

        if ($role === null) {
            return true;
        }

        return $role === $association->pivot->role_name;
    }

    public function hasAssociationPermission($association, $permission): bool
    {
        $association = $this->getAssociation($association);

        if ($association === null) {
            return false;
        }

        return in_array($permission, $association->pivot->permissions ?? []);
    }

    public function getAssociationRole($association)
    {
        $association = $this->getAssociation($association);

        if ($association === null) {
            return null;
        }

        return $association->pivot->role_name;
    }

    public function getAssociationPermissions($association)
    {
        $association = $this->getAssociation($association);

        if ($association === null) {
            return null;
        }

        return $association->pivot->permissions;
    }

    private function getAssociation($association)
    {
        if (is_a($association, Association::class)) {
            $association = $association->id;
        }

        return $this->associations()->find($association);
    }
}
