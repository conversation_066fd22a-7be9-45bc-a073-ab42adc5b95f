<?php

namespace App\Enums\InhalationTechnique;

use Illuminate\Support\Collection;

class InhalationTechniqueTypeEnum
{
    const UNKNOWN_HYPERTENSION = 0;

    const EXISTING_HYPERTENSION = 1;

    public static function getAll(): Collection
    {
        return collect([
            self::UNKNOWN_HYPERTENSION,
            self::EXISTING_HYPERTENSION,
        ]);
    }

    public static function getLabel($value): string
    {
        if ($value === null) {
            return '';
        }
        $value = intval($value);

        switch ($value) {
            case self::UNKNOWN_HYPERTENSION:
                return 'Ohne bekannten Bluthochdruck';
            case self::EXISTING_HYPERTENSION:
                return 'Mit bestehendem Bluthochdruck';
        }

        return '';
    }
}
