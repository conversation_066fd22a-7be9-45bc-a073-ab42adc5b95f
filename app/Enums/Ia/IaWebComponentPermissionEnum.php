<?php

namespace App\Enums\Ia;

enum IaWebComponentPermissionEnum: string
{
    case NotificationSetupShow = 'notification-setup-show';
    case NotificationSetupEdit = 'notification-setup-edit';
    case OwnCouponsShow = 'own-coupons-show';
    case OwnCouponsEdit = 'own-coupons-edit';
    case OwnPznShow = 'own-pzn-show';
    case OwnPznEdit = 'own-pzn-edit';
    case MyLifeIndividualisationShow = 'my-life-individualisation-show';
    case MyLifeIndividualisationEdit = 'my-life-individualisation-edit';
    case ExtendedProfileShow = 'extended-profile-show';
    case ExtendedProfileEdit = 'extended-profile-edit';
    case ExpressDeliveryShow = 'express-delivery-show';
    case ExpressDeliveryEdit = 'express-delivery-edit';
    case AdvertisedProductsShow = 'advertised-products-show';
    case AdvertisedProductsEdit = 'advertised-products-edit';
    case PharmacyWebSdkActivationShow = 'pharmacy-web-sdk-activation-show';
    case PharmacyWebSdkActivationEdit = 'pharmacy-web-sdk-activation-edit';
    case PharmacyWebsiteWebSdkStylingShow = 'pharmacy-website-web-sdk-styling-show';
    case PharmacyWebsiteWebSdkStylingEdit = 'pharmacy-website-web-sdk-styling-edit';
    case OnlinePayedOrdersShow = 'online-payed-orders-show';
    case OnlinePayedOrdersEdit = 'online-payed-orders-edit';
    case OnlinePaymentSetupShow = 'online-payment-setup-show';
    case OnlinePaymentSetupEdit = 'online-payment-setup-edit';
    case ProductAvailabilityShow = 'product-availability-show';
    case ProductAvailabilityEdit = 'product-availability-edit';
    case ReservationCockpitShow = 'reservation-cockpit-show';
    case ReservationCockpitEdit = 'reservation-cockpit-edit';
    case SpecialOfferProductsShow = 'special-offer-products-show';
    case SpecialOfferProductsEdit = 'special-offer-products-edit';
    case StandardDeliveryShow = 'standard-delivery-show';
    case StandardDeliveryEdit = 'standard-delivery-edit';
    case PharmacyWebsiteActivationShow = 'pharmacy-website-activation-show';
    case PharmacyWebsiteActivationEdit = 'pharmacy-website-activation-edit';
    case TelemedicineShow = 'telemedicine-show';
    case TelemedicineEdit = 'telemedicine-edit';
    case PharmacyServicesShow = 'pharmacy-services-show';
    case PharmacyServicesEdit = 'pharmacy-services-edit';
    case AssortmentShow = 'assortment-show';
    case AssortmentEdit = 'assortment-edit';
    case TermsShow = 'terms-show';
    case TermsEdit = 'terms-edit';
    case WawiShow = 'wawi-show';
    case WawiEdit = 'wawi-edit';
}
