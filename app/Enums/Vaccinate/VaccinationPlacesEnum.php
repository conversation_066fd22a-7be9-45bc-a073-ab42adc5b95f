<?php

namespace App\Enums\Vaccinate;

use Illuminate\Support\Collection;

class VaccinationPlacesEnum
{
    const OTHER = 1;

    const FAMILY_DOCTOR = 2;

    const COMPANY_DOCTOR = 3;

    const HEALTH_DEPARTMENT = 4;

    const MEDICAL_SPECIALIST = 5;

    const NONE = 6;

    public static function getAll(): Collection
    {
        return collect([
            self::OTHER,
            self::FAMILY_DOCTOR,
            self::COMPANY_DOCTOR,
            self::HEALTH_DEPARTMENT,
            self::MEDICAL_SPECIALIST,
            self::NONE,
        ]);
    }

    public static function getForDropdown()
    {
        return [
            self::FAMILY_DOCTOR => self::FAMILY_DOCTOR,
            self::COMPANY_DOCTOR => self::COMPANY_DOCTOR,
            self::HEALTH_DEPARTMENT => self::HEALTH_DEPARTMENT,
            self::MEDICAL_SPECIALIST => self::MEDICAL_SPECIALIST,
            self::OTHER => self::OTHER,
            self::NONE => self::NONE,
        ];
    }

    public static function getShortLabel($value): string
    {
        if ($value === null) {
            return '';
        }
        $value = intval($value);

        switch ($value) {
            case self::OTHER:
                return 'Sonstiges';
            case self::FAMILY_DOCTOR:
                return '(Haus-)Arzt';
            case self::COMPANY_DOCTOR:
                return 'Betriebsarzt';
            case self::HEALTH_DEPARTMENT:
                return 'Gesundheitsamt';
            case self::MEDICAL_SPECIALIST:
                return 'Facharzt';
            case self::NONE:
                return 'Nein';
        }

        return '';
    }

    public static function getShortLabels($values): array
    {
        if (! $values) {
            return [];
        }

        $labels = [];
        foreach ($values as $value) {
            array_push($labels, self::getShortLabel($value));
        }

        return $labels;
    }
}
