<?php

namespace App\Enums\Vaccinate;

use App\Vaccination;

class CovidVaccinationStepEnum
{
    const START = 1;

    const PERSONAL_DATA = 2;

    const VACCINATION_DATA = 3;

    const OVERVIEW = 4;

    const FINISHED = 5;

    public static function getRedirectRoute(Vaccination $vaccination)
    {
        return self::getRedirectRouteArray()[$vaccination->step - 1];
    }

    public static function getRedirectRouteArray()
    {
        return [
            'pharmacies.vaccinate-covid.start-information',
            'pharmacies.vaccinate-covid.personal-data',
            'pharmacies.vaccinate-covid.vaccination-data',
            'pharmacies.vaccinate-covid.overview',
            'pharmacies.vaccinate-covid.finished',
        ];
    }
}
