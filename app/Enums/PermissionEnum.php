<?php

namespace App\Enums;

enum PermissionEnum: string
{
    case VIEW = 'view';

    case VIEW_ANY = 'viewAny';

    case CREATE = 'create';

    case UPDATE = 'update';

    case DELETE = 'delete';

    case RESTORE = 'restore';

    case FORCE_DELETE = 'forceDelete';

    /**
     * @return array<string>
     */
    public static function wildcard(): array
    {
        return ['*'];
    }

    /**
     * @return PermissionEnum[]
     */
    public static function all(): array
    {
        return [self::VIEW, self::VIEW_ANY, self::CREATE, self::UPDATE, self::DELETE, self::RESTORE, self::FORCE_DELETE];
    }

    /**
     * @return PermissionEnum[]
     */
    public static function viewAndViewAny(): array
    {
        return [self::VIEW, self::VIEW_ANY];
    }

    /**
     * @return PermissionEnum[]
     */
    public static function createAndUpdate(): array
    {
        return [self::CREATE, self::UPDATE];
    }

    /**
     * @return PermissionEnum[]
     */
    public static function delete(): array
    {
        return [self::DELETE];
    }

    /**
     * @return PermissionEnum[]
     */
    public static function restoreAndForceDelete(): array
    {
        return [self::RESTORE, self::FORCE_DELETE];
    }

    /**
     * @param  PermissionEnum[]|PermissionEnum  $except
     * @return array<PermissionEnum>
     */
    public static function except(array|PermissionEnum $except = []): array
    {
        if ($except instanceof PermissionEnum) {
            return self::except([$except]);
        }

        $permissionCollection = collect(self::cases());

        /** @var array<PermissionEnum> $permissions */
        $permissions = $permissionCollection
            ->filter(fn (PermissionEnum $permission) => in_array($permission, $except) === false)
            ->toArray();

        return $permissions;
    }
}
