<?php

namespace App\Enums;

class NewsStatusEnum
{
    const DRAFT = 0;

    const PENDING = 1;

    const PUBLISHED = 2;

    const ARCHIVE = 3;

    public static function getForNova()
    {
        return [
            self::DRAFT => 'Entwurf',
            self::PENDING => 'Wartet auf Veröffentlichung',
            self::PUBLISHED => 'Veröffentlicht',
            self::ARCHIVE => 'Archiviert',
        ];
    }
}
