<?php

namespace Database\Factories;

use App\VaccinationPatient;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

class VaccinationPatientFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = VaccinationPatient::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'vaccination_id' => null, // needs to be overwritten
            'birthdate' => $this->faker->date('Y-m-d'),
            'first_name' => $this->faker->firstName,
            'last_name' => $this->faker->lastName,
            'insurance_number' => uniqid(),
            'street' => $this->faker->streetName,
            'house_number' => rand(1, 1000),
            'postcode' => rand(1000, 99999),
            'city' => $this->faker->city,
            'email' => $this->faker->email,
            'phone' => $this->faker->phoneNumber,
            'age' => (int) abs(Carbon::parse(fake()->date())->diffInYears()),
        ];
    }
}
