<?php

namespace Database\Factories;

use App\ShiftPlanGroup;
use App\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\=ShiftPlanGroupUser>
 */
class ShiftPlanGroupUserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid()->toString(),
            'sort_order' => $this->faker->randomNumber(),
            'shift_plan_group_id' => ShiftPlanGroup::factory(),
            'user_id' => User::factory(),
        ];
    }
}
