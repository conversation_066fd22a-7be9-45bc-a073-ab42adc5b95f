<?php

namespace Database\Factories;

use App\Pharmaceutical;
use Illuminate\Database\Eloquent\Factories\Factory;

class PharmaceuticalFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Pharmaceutical::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => uniqid(),
            'display_name' => uniqid(),
            'active' => true,
            'pzn' => rand(10000000, 99999999),
        ];
    }
}
