<?php

namespace Database\Factories;

use App\Integration;
use App\Integrations\IntegrationTypeEnum;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class IntegrationFactory extends Factory
{
    protected $model = Integration::class;

    public function definition(): array
    {
        return [
            'integration_type' => IntegrationTypeEnum::IhreApotheken->value,
            'settings' => $this->faker->words(),
            'integratable_id' => $this->faker->randomNumber(),
            'integratable_type' => $this->faker->word(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
