<?php

namespace Database\Factories;

use App\Banner;
use App\Enums\BannerPosition;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Banner>
 */
class BannerFactory extends Factory
{
    protected $model = Banner::class;

    public function definition(): array
    {
        return [
            'title' => fake()->word(),
            'content' => fake()->text(),
            'position' => fake()->randomElement(BannerPosition::cases()),
            'starts_at' => now(),
            'ends_at' => fake()->optional()->dateTimeBetween(now(), now()->addYear()),
            'active' => fake()->boolean(),
        ];
    }
}
