<?php

namespace Database\Seeders;

use App\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $categories = ['Digitale Zukunft', 'Digitales Gesundheitswesen', 'Digitale Apotheke', 'Digitale Gesundheitsanwendungen', 'Digitale Verantwortung', 'Digitale Dates'];

        foreach ($categories as $category) {
            Category::create([
                'title' => $category,
                'slug' => Str::slug($category, '-'),
            ]);
        }
    }
}
