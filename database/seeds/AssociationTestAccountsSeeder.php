<?php

namespace Database\Seeders;

use App\Association;
use App\BrochureCode;
use App\Enums\PharmacyPermissionsEnum;
use App\Enums\PharmacyRoleEnum;
use App\Pharmacy;
use App\PharmacyAddress;
use App\TelematicsId;
use App\User;
use App\UserPharmacyProfile;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AssociationTestAccountsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $associations = Association::all();

        foreach ($associations as $association) {
            $owner = User::factory()->create([
                'email_verified_at' => now(),
                'password' => Hash::make('TheSuperLogTestPassword123!'),
            ]);

            UserPharmacyProfile::create([
                'user_id' => $owner->id,
                'association_id' => $association->id,
            ]);

            BrochureCode::factory()->create([
                'user_id' => $owner->id,
                'association_id' => $association->id,
            ]);

            for ($i = 0; $i < 3; $i++) {
                $pharmacy = Pharmacy::factory()->create([
                    'is_main' => ($i === 0),
                ]);

                TelematicsId::factory()->create([
                    'card_type' => '2',
                    'telematics_idable_id' => $pharmacy->id,
                    'telematics_idable_type' => Pharmacy::class,
                ]);

                PharmacyAddress::factory()->create([
                    'pharmacy_id' => $pharmacy->id,
                ]);

                $employee = User::factory()->create([
                    'email_verified_at' => now(),
                    'password' => Hash::make('TheSuperLogTestPassword123!'),
                ]);

                UserPharmacyProfile::create([
                    'user_id' => $employee->id,
                ]);

                $branchManager = User::factory()->create([
                    'email_verified_at' => now(),
                    'password' => Hash::make('TheSuperLogTestPassword123!'),
                ]);

                UserPharmacyProfile::create([
                    'user_id' => $branchManager->id,
                ]);

                $pharmacy->assignUser($owner, PharmacyRoleEnum::OWNER);
                $pharmacy->assignUser($employee, PharmacyRoleEnum::EMPLOYEE, [
                    PharmacyPermissionsEnum::IMPORT_VACCINATIONS,
                ]);
                $pharmacy->assignUser($branchManager, PharmacyRoleEnum::BRANCH_MANAGER, [
                    PharmacyPermissionsEnum::IMPORT_VACCINATIONS,
                    PharmacyPermissionsEnum::ADMINISTRATE_USERS,
                    PharmacyPermissionsEnum::EDIT_PHARMACY,
                ]);
            }
        }
    }
}
