<?php

namespace Database\Seeders;

use App\Tag;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class TagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $tags = ['Covid-19', 'Digitale Trends', 'Künstliche Intelligenz', 'Telemedizin / Telepharmazie', 'Telepharmazeutische Dienstleistungen', 'Digital Learning', 'Apothekerliche Ethik', 'Datenschutz und Datensicherheit', 'Veranstaltungshinweise'];

        foreach ($tags as $tag) {
            Tag::create([
                'title' => $tag,
                'slug' => Str::slug($tag, '-'),
            ]);
        }
    }
}
