{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-json": "*", "ext-openssl": "*", "artesaos/seotools": "^1.2", "babenkoivan/elastic-migrations": "^3.1", "babenkoivan/elastic-scout-driver": "^3.1", "babenkoivan/elastic-scout-driver-plus": "^4.5", "barryvdh/laravel-dompdf": "^3.0", "chillerlan/php-qrcode": "^5.0", "cknow/laravel-money": "^8.4", "doctrine/dbal": "^3.6", "ebess/advanced-nova-media-library": "^4.0", "fakerphp/faker": "^1.21", "flowframe/laravel-trend": "^0.2.0", "gedisa/gedisa-id-php": "dev-main", "gedisa/text-card": "@dev", "gehrisandro/tailwind-merge-laravel": "^1.2", "geocoder-php/algolia-places-provider": "^0.4.0", "geocoder-php/nominatim-provider": "^5.5", "guzzlehttp/guzzle": "^7.0.1", "laminas/laminas-feed": "^2.13", "laraning/nova-time-field": "^0.3.0", "laravel/cashier": "^15.4", "laravel/framework": "^11.0", "laravel/horizon": "^5.7", "laravel/nova": "^4.0", "laravel/passport": "^12.0", "laravel/pennant": "^1.7", "laravel/scout": "^10.0", "laravel/tinker": "^2.0", "laravel/ui": "^4.2", "league/flysystem-aws-s3-v3": "^3.0", "livewire/flux-pro": "^1.0", "livewire/livewire": "^3.4", "maatwebsite/excel": "^3.1", "maatwebsite/laravel-nova-excel": "^1.2", "markwalet/nova-modal-response": "^0.3.1", "mews/purifier": "^3.3", "miladrahimi/php-jwt": "^2.1", "mpdf/mpdf": "^8.2", "opis/closure": "^4.3", "paragonie/halite": "^4.8", "php-webdriver/webdriver": "^1.15", "phpseclib/phpseclib": "^3.0", "predis/predis": "^2.1", "saloonphp/laravel-plugin": "^3.0", "saloonphp/saloon": "^3.0", "sentry/sentry-laravel": "^4.1", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/eloquent-sortable": "^4.3", "spatie/laravel-data": "^4.6", "spatie/laravel-livewire-wizard": "^2.3", "spatie/laravel-medialibrary": "^11.12", "spatie/laravel-query-builder": "^6.0", "spatie/laravel-rate-limited-job-middleware": "^2.2", "spatie/laravel-ray": "^1.36", "spatie/laravel-settings": "*", "symfony/http-client": "^6.2", "symfony/mailgun-mailer": "^6.2", "titasgailius/search-relations": "^2.0", "toin0u/geocoder-laravel": "^4.5", "web-token/jwt-framework": "^3.4", "xdavidwu/laravel-oidc-auth": "^0.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.4", "brianium/paratest": "^7.0", "barryvdh/laravel-debugbar": "^3.7", "barryvdh/laravel-ide-helper": "^3.5", "larastan/larastan": "^3.4", "laravel/pint": "^1.13", "laravel/sail": "1.*", "laravel/telescope": "^5.0", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": ["laravel/telescope", "barryvdh/laravel-ide-helper"]}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"], "files": ["app/Helper/helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-update-cmd": ["@php artisan nova:publish"], "analyse": "vendor/bin/pint --config=pint.json && vendor/bin/phpstan analyse -c phpstan.neon --memory-limit=2G", "phpstan": "vendor/bin/phpstan analyse -c phpstan.neon --memory-limit=2G", "fix": "./node_modules/.bin/prettier \"resources/views/livewire/ia/**/*.blade.php\" \"resources/views/ia/**/*.blade.php\" \"resources/views/components/headless/**/*.blade.php\" \"resources/views/components/modal/**/*.blade.php\" --write"}, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}, "nova": {"type": "composer", "url": "https://nova.laravel.com"}, "spatie": {"type": "composer", "url": "https://satis.spatie.be"}, "1": {"type": "path", "url": "./nova-components/TextCard"}, "gedisa-id-php": {"type": "github", "url": "https://github.com/gedisa/gedisa-id-php"}}}