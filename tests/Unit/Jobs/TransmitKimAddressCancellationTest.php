<?php

namespace Tests\Unit\Jobs;

use App\Enums\KimVendorEnum;
use App\Helper\Kim\KimAkquinetAddressApi;
use App\Http\Integrations\Akquinet\Requests\CancelKimAddressRequest;
use App\Jobs\KimAddress\TransmitKimAddressCancellation;
use App\KimAddress;
use Illuminate\Support\Facades\Queue;
use Saloon\Http\Faking\MockResponse;
use Saloon\Laravel\Saloon;
use Tests\TestCase;

class TransmitKimAddressCancellationTest extends TestCase
{
    public function test_job_is_unique(): void
    {
        Queue::fake([
            TransmitKimAddressCancellation::class,
        ]);

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $kimAddress = KimAddress::factory()->for($pharmacy)->create();

        TransmitKimAddressCancellation::dispatch($kimAddress);
        TransmitKimAddressCancellation::dispatch($kimAddress);

        Queue::assertCount(1);
    }

    public function test_queue_wont_fail_when_registration_code_not_found_with_status_code_400(): void
    {
        Queue::fake([
            TransmitKimAddressCancellation::class,
        ]);

        cache()->put(KimAkquinetAddressApi::KIM_AKQUINET_ADMIN_CACHE_KEY, 'abc', 295);

        Saloon::fake([
            config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.api_url').'*' => MockResponse::make(['body' => CancelKimAddressRequest::BAD_REQUEST_EXCEPTION_BODY], 400),
        ]);

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $kimAddress = KimAddress::factory()->ordered()->for($pharmacy)->create();

        $this->assertNull((new TransmitKimAddressCancellation($kimAddress))->handle());
    }

    public function test_queue_fails_when_status_code_is_400(): void
    {
        cache()->put(KimAkquinetAddressApi::KIM_AKQUINET_ADMIN_CACHE_KEY, 'abc', 295);

        Saloon::fake([
            config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.api_url').'*' => MockResponse::make(['body' => 'Some message here'], 400),
        ]);

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $kimAddress = KimAddress::factory()->ordered()->for($pharmacy)->create();

        $this->expectException(\Saloon\Exceptions\Request\ClientException::class);

        (new TransmitKimAddressCancellation($kimAddress))->handle();
    }
}
