<?php

namespace Tests\Unit\Jobs;

use App\Jobs\CreateAndSendInvoice;
use App\SubscriptionOrder;
use App\Support\Payment\PaymentApi;
use Illuminate\Foundation\Testing\RefreshDatabase;
use InvalidArgumentException;
use Tests\TestCase;

class CreateAndSendInvoiceTest extends TestCase
{
    use RefreshDatabase;

    public function test_no_orders_no_invoice(): void
    {
        $subscriptionOrders = collect();
        $paymentApi = $this->createMock(PaymentApi::class);
        $paymentApi->expects($this->never())->method('createAndSendInvoice');

        (new CreateAndSendInvoice($subscriptionOrders))->handle($paymentApi);
    }

    public function test_order_already_processed(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('At least one of the orders has already been processed.');

        $invoice = $this->createInvoice();
        $subscriptionOrders = collect([
            $this->createSubscriptionOrder(['invoice_id' => $invoice->id]),
            $this->createSubscriptionOrder(),
        ]);
        $paymentApi = $this->createMock(PaymentApi::class);
        $paymentApi->expects($this->never())->method('createAndSendInvoice');

        (new CreateAndSendInvoice($subscriptionOrders))->handle($paymentApi);
    }

    public function test_handle_valid_orders(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subscriptionOrders = collect([
            $this->createSubscriptionOrder(),
            $this->createSubscriptionOrder(),
        ]);
        $subscriptionOrders->each(fn (SubscriptionOrder $subscriptionOrder) => $subscriptionOrder->orderable()->associate($pharmacy)->save());
        $paymentApi = $this->createMock(PaymentApi::class);
        $paymentApi->expects($this->once())->method('createAndSendInvoice');

        (new CreateAndSendInvoice($subscriptionOrders))->handle($paymentApi);
    }
}
