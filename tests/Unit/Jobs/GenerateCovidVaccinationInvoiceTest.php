<?php

namespace Tests\Unit\Jobs;

use App\CovidVaccinationInvoice;
use App\Enums\Vaccinate\CovidVaccinationInvoiceableActions;
use App\Enums\Vaccinate\CovidVaccinationInvoiceableActions as InvoiceableActions;
use App\Jobs\GenerateCovidVaccinationInvoice;
use App\Misc\CovidVaccinationInvoiceableActionPricing;
use App\Pharmacy;
use App\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\helper\VaccinationCreationHelper;
use Tests\TestCase;

class GenerateCovidVaccinationInvoiceTest extends TestCase
{
    use RefreshDatabase, VaccinationCreationHelper;

    public function test_price_summary_is_calculated_correctly_for_varying_prices()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $vaccinationInvoice = new CovidVaccinationInvoice;
        $vaccinationInvoice->user_id = $user->id;
        $vaccinationInvoice->pharmacy_id = $pharmacy->id;
        $vaccinationInvoice->start_date = '2023-09-15';
        $vaccinationInvoice->end_date = '2023-10-15';

        $this->createTestVaccination($pharmacy, $user, '2023-10-05');
        $this->createTestVaccination($pharmacy, $user, '2023-10-09');
        $this->createTestVaccination($pharmacy, $user, '2023-09-16');

        $invoiceGenerator = new GenerateCovidVaccinationInvoice($vaccinationInvoice, $this->getActionPricing(), false);

        $this->assertEquals([
            'total' => [
                'count' => 3,
                'price' => 5800,
            ],
            'actions' => [
                17716553 => [
                    1500 => [
                        'total' => [
                            'count' => 2,
                            'price' => 3000,
                        ],
                    ],
                    2800 => [
                        'total' => [
                            'count' => 1,
                            'price' => 2800,
                        ],
                    ],
                ],
                17716576 => [],
                17716582 => [],
                17716599 => [],
                17716607 => [],
                '17716607 (oV)' => [],
            ],
        ], $invoiceGenerator->calcPriceSummary(
            $invoiceGenerator->getQuery()
        ));
    }

    private function createTestVaccination(Pharmacy $pharmacy, User $user, string $date): void
    {
        $vaccination = $this->createVaccination($pharmacy, $user);
        $this->createCovidVaccination($vaccination);

        $vaccination->date = new Carbon($date);
        $vaccination->save();
    }

    private function getActionPricing(): CovidVaccinationInvoiceableActionPricing
    {
        $actionPricing = new CovidVaccinationInvoiceableActionPricing;
        $actionPricing->defaultPrices[CovidVaccinationInvoiceableActions::VACCINATION] = 1500;
        $actionPricing->datedPriceDeviations = [
            InvoiceableActions::VACCINATION => [
                [
                    'price' => 2800,
                    'from' => null,
                    'to' => '2023-09-30',
                ],
            ],
        ];

        return $actionPricing;
    }
}
