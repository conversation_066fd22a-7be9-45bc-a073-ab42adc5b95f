<?php

namespace Tests\Unit\Domains\ShiftPlan\Domain\Actions\Shift;

use App\Domains\ShiftPlan\Domain\Actions\Shift\CalculateAssignedHours;
use stdClass;
use Tests\TestCase;

/** @group shiftplan */
class CalculateAssignedHoursTest extends TestCase
{
    protected function createShift($userId, $duration): stdClass
    {
        $shift = new stdClass;
        $shift->planGroupUser = new stdClass;
        $shift->planGroupUser->user_id = $userId;
        $shift->duration = $duration;

        return $shift;
    }

    public function test_no_shifts(): void
    {
        $result = CalculateAssignedHours::execute([]);

        $this->assertEmpty($result);
    }

    public function test_shifts_for_single_user(): void
    {
        $days = [
            [
                '2024-06-10' => [
                    $this->createShift(1, 120),
                    $this->createShift(1, 180),
                ],
            ],
        ];

        $result = CalculateAssignedHours::execute($days);

        $this->assertCount(1, $result);
        $this->assertEquals(300, $result[1]);
    }

    public function test_shifts_for_multiple_users(): void
    {
        $days = [
            [
                '2024-06-10' => [
                    $this->createShift(1, 120),
                    $this->createShift(2, 180),
                ],
                '2024-06-11' => [
                    $this->createShift(1, 60),
                    $this->createShift(2, 90),
                ],
            ],
        ];

        $result = CalculateAssignedHours::execute($days);

        $this->assertCount(2, $result);
        $this->assertEquals(180, $result[1]);
        $this->assertEquals(270, $result[2]);
    }
}
