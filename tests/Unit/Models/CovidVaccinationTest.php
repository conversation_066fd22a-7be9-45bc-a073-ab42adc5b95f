<?php

namespace Tests\Unit\Models;

use App\Enums\Vaccinate\CovidVaccinationInvoiceableActions;
use App\Enums\Vaccinate\HomeVisitTypeEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\helper\VaccinationCreationHelper;
use Tests\TestCase;

class CovidVaccinationTest extends TestCase
{
    use RefreshDatabase, VaccinationCreationHelper;

    public function test_it_has_vaccination(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $vaccination = $this->createVaccination($pharmacy, $user);
        $covidVaccination = $this->createCovidVaccination($vaccination);
        $vaccination = $vaccination->refresh();

        $this->assertTrue($vaccination->is($covidVaccination->vaccination));
    }

    public function test_it_gets_billable_actions_attribute_for_vaccination(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $vaccination = $this->createVaccination($pharmacy, $user);
        $covidVaccination = $this->createCovidVaccination($vaccination);

        $actions = collect([
            CovidVaccinationInvoiceableActions::VACCINATION,
        ]);

        $this->assertEmpty($covidVaccination->invoiceable_actions->diff($actions));
    }

    public function test_it_gets_billable_actions_attribute_for_vaccination_and_certificate(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $vaccination = $this->createVaccination($pharmacy, $user);
        $covidVaccination = $this->createCovidVaccination($vaccination);

        $actions = collect([
            CovidVaccinationInvoiceableActions::VACCINATION,
            CovidVaccinationInvoiceableActions::CERTIFICATE_WITHOUT_REMUNERATION,
        ]);

        $this->assertEmpty($covidVaccination->invoiceable_actions->diff($actions));
    }

    public function test_it_gets_billable_actions_attribute_for_vaccination_and_home_visit_first(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $vaccination = $this->createVaccination($pharmacy, $user);
        $covidVaccination = $this->createCovidVaccination($vaccination);

        ($vaccination->date = '2023-09-30') && $vaccination->save();

        $covidVaccination->home_visit = 1;
        $covidVaccination->home_visit_type = HomeVisitTypeEnum::FIRST;

        $actions = collect([
            CovidVaccinationInvoiceableActions::VACCINATION,
            CovidVaccinationInvoiceableActions::HOME_VISIT_FIRST,
        ]);

        $this->assertEmpty($covidVaccination->invoiceable_actions->diff($actions));
    }

    public function test_it_gets_billable_actions_attribute_for_vaccination_and_home_visit_first_since_oct_2023(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $vaccination = $this->createVaccination($pharmacy, $user);
        $covidVaccination = $this->createCovidVaccination($vaccination);

        ($vaccination->date = '2023-10-01') && $vaccination->save();

        $covidVaccination->home_visit = 1;
        $covidVaccination->home_visit_type = HomeVisitTypeEnum::FIRST;

        $actions = collect([
            CovidVaccinationInvoiceableActions::VACCINATION,
            CovidVaccinationInvoiceableActions::HOME_VISIT_FIRST_WITHOUT_REMUNERATION,
        ]);

        $this->assertEmpty($covidVaccination->invoiceable_actions->diff($actions));
    }

    public function test_it_gets_billable_actions_attribute_for_vaccination_and_home_visit_follow(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $vaccination = $this->createVaccination($pharmacy, $user);
        $covidVaccination = $this->createCovidVaccination($vaccination);

        ($vaccination->date = '2023-09-29') && $vaccination->save();

        $covidVaccination->home_visit = 1;
        $covidVaccination->home_visit_type = HomeVisitTypeEnum::FOLLOW;

        $actions = collect([
            CovidVaccinationInvoiceableActions::VACCINATION,
            CovidVaccinationInvoiceableActions::HOME_VISIT_FOLLOWING,
        ]);

        $this->assertEmpty($covidVaccination->invoiceable_actions->diff($actions));
    }

    public function test_it_gets_billable_actions_attribute_for_vaccination_and_home_visit_follow_since_oct_2023(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $vaccination = $this->createVaccination($pharmacy, $user);
        $covidVaccination = $this->createCovidVaccination($vaccination);

        ($vaccination->date = '2023-10-01') && $vaccination->save();

        $covidVaccination->home_visit = 1;
        $covidVaccination->home_visit_type = HomeVisitTypeEnum::FOLLOW;

        $actions = collect([
            CovidVaccinationInvoiceableActions::VACCINATION,
            CovidVaccinationInvoiceableActions::HOME_VISIT_FOLLOWING_WITHOUT_REMUNERATION,
        ]);

        $this->assertEmpty($covidVaccination->invoiceable_actions->diff($actions));
    }
}
