<?php

namespace Tests\Unit\Models;

use App\AssociationMembershipHistory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AssociationMembershipHistoryTest extends TestCase
{
    use RefreshDatabase;

    public function test_association_membership_history_can_not_be_deleted(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        $this->assertCount(1, AssociationMembershipHistory::all());

        $user->currentAssociationMembershipHistory->delete();

        $this->assertCount(1, AssociationMembershipHistory::all());
    }
}
