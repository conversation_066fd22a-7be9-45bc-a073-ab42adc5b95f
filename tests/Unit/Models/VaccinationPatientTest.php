<?php

namespace Tests\Unit\Models;

use App\Enums\Vaccinate\VaccinationTypeEnum;
use App\Vaccination;
use App\VaccinationPatient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class VaccinationPatientTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_encrypts_all_personal_data()
    {
        /** @var Vaccination $vaccination */
        $vaccination = Vaccination::factory()->create([
            'type' => VaccinationTypeEnum::INFLUENZA,
        ]);

        /** @var VaccinationPatient $vaccinationPatient */
        $vaccinationPatient = VaccinationPatient::factory()->create([
            'vaccination_id' => $vaccination->id,
            'optional_address_line' => 'optional address line',
        ]);

        $data = get_object_vars(DB::select('select * from vaccination_patients where vaccination_id = :id', ['id' => $vaccination->id])[0]);

        // Make sure that all values are tested
        $this->assertCount(16, $data);

        $this->assertEquals($data['vaccination_id'], $vaccinationPatient->vaccination_id);
        $this->assertEquals($data['created_at'], $vaccinationPatient->created_at);
        $this->assertEquals($data['updated_at'], $vaccinationPatient->updated_at);

        $this->assertNotEquals($data['birthdate'], $vaccinationPatient->birthdate);
        $this->assertNotEquals($data['first_name'], $vaccinationPatient->first_name);
        $this->assertNotEquals($data['last_name'], $vaccinationPatient->last_name);
        $this->assertNotEquals($data['insurance_number'], $vaccinationPatient->insurance_number);
        $this->assertNotEquals($data['optional_address_line'], $vaccinationPatient->optional_address_line);
        $this->assertNotEquals($data['street'], $vaccinationPatient->street);
        $this->assertNotEquals($data['house_number'], $vaccinationPatient->house_number);
        $this->assertNotEquals($data['postcode'], $vaccinationPatient->postcode);
        $this->assertNotEquals($data['city'], $vaccinationPatient->city);
        $this->assertNotEquals($data['email'], $vaccinationPatient->email);
        $this->assertNotEquals($data['phone'], $vaccinationPatient->phone);
        $this->assertNotEquals($data['age'], $vaccinationPatient->phone);
    }
}
