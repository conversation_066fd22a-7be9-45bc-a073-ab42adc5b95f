<?php

namespace Tests\Unit\Models\Relations;

use App\Association;
use App\Enums\Vaccinate\VaccinationTypeEnum;
use App\HealthInsuranceCompany;
use App\Pharmaceutical;
use App\Pharmacy;
use App\User;
use App\Vaccination;
use App\VaccinationPatient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VaccinationRelationsTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function association(): void
    {
        $association = Association::factory()->create();
        $vaccination = Vaccination::factory()->create([
            'association_id' => $association->id,
        ]);

        $this->assertTrue($vaccination->association->is($association));
        $this->assertContains($vaccination->id, $association->vaccinations->pluck('id'));
    }

    /** @test */
    public function pharmacy(): void
    {
        $pharmacy = Pharmacy::factory()->create();
        $vaccination = Vaccination::factory()->create([
            'pharmacy_id' => $pharmacy->id,
        ]);

        $this->assertTrue($vaccination->pharmacy->is($pharmacy));
        $this->assertContains($vaccination->id, $pharmacy->vaccinations->pluck('id'));
    }

    /** @test */
    public function health_insurance_company(): void
    {
        $healthInsuranceCompany = HealthInsuranceCompany::factory()->create();
        $vaccination = Vaccination::factory()->create([
            'health_insurance_company_id' => $healthInsuranceCompany->id,
        ]);

        $this->assertTrue($vaccination->healthInsuranceCompany->is($healthInsuranceCompany));
        $this->assertContains($vaccination->id, $healthInsuranceCompany->vaccinations->pluck('id'));
    }

    /** @test */
    public function user(): void
    {
        $user = User::factory()->create();
        $vaccination = Vaccination::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->assertTrue($vaccination->user->is($user));
        $this->assertContains($vaccination->id, $user->vaccinations->pluck('id'));
    }

    /** @test */
    public function pharmaceutical(): void
    {
        $pharmaceutical = Pharmaceutical::factory()->create();
        $vaccination = Vaccination::factory()->create([
            'pharmaceutical_id' => $pharmaceutical->id,
        ]);

        $this->assertTrue($vaccination->pharmaceutical->is($pharmaceutical));
        $this->assertContains($vaccination->id, $pharmaceutical->vaccinations->pluck('id'));
    }

    /** @test */
    public function vaccination_patient()
    {
        $vaccination = Vaccination::factory()->create([
            'type' => VaccinationTypeEnum::INFLUENZA,
        ]);
        $vaccinationPatient = VaccinationPatient::factory()->create([
            'vaccination_id' => $vaccination->id,
        ]);

        $this->assertTrue($vaccination->vaccinationPatient->is($vaccinationPatient->fresh()));
    }
}
