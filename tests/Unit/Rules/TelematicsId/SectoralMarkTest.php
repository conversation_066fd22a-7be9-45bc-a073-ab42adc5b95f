<?php

namespace Tests\Unit\Rules\TelematicsId;

use App\Rules\TelematicsId\SectoralMark;
use Tests\TestCase;

class SectoralMarkTest extends TestCase
{
    /**
     * @test
     */
    public function passes()
    {
        $rule = new SectoralMark;

        $this->assertTrue($rule->passes('telematics_id', '3-xx.x.xxxxxxxxxx.xx.xxx'));

        $this->assertFalse($rule->passes('telematics_id', 'x-xx.x.xxxxxxxxxx.xx.xxx'));
        $this->assertFalse($rule->passes('telematics_id', '1-xx.x.xxxxxxxxxx.xx.xxx'));
        $this->assertFalse($rule->passes('telematics_id', '4-xx.x.xxxxxxxxxx.xx.xxx'));
        $this->assertFalse($rule->passes('telematics_id', 'a-xx.x.xxxxxxxxxx.xx.xxx'));

        $this->assertTrue($rule->passes('sectoral_mark', '3'));

        $this->assertFalse($rule->passes('sectoral_mark', '1'));
        $this->assertFalse($rule->passes('sectoral_mark', '4'));
        $this->assertFalse($rule->passes('sectoral_mark', 'a'));
    }
}
