<?php

namespace Tests\Unit\Rules\TelematicsId;

use App\Pharmacy;
use App\Rules\TelematicsId\CardType;
use App\User;
use Tests\TestCase;

class CardTypeTest extends TestCase
{
    /**
     * @test
     */
    public function passes()
    {
        $rulePharmacy = new CardType(Pharmacy::class);

        $this->assertTrue($rulePharmacy->passes('telematics_id', 'x-xx.2.xxxxxxxxxx.xx.xxx'));

        $this->assertFalse($rulePharmacy->passes('telematics_id', 'x-xx.1.xxxxxxxxxx.xx.xxx'));
        $this->assertFalse($rulePharmacy->passes('telematics_id', 'x-xx.3.xxxxxxxxxx.xx.xxx'));

        $this->assertFalse($rulePharmacy->passes('telematics_id', 'x-xx.x.xxxxxxxxxx.xx.xxx'));
        $this->assertFalse($rulePharmacy->passes('telematics_id', 'x-xx.0.xxxxxxxxxx.xx.xxx'));
        $this->assertFalse($rulePharmacy->passes('telematics_id', 'x-xx.5.xxxxxxxxxx.xx.xxx'));
        $this->assertFalse($rulePharmacy->passes('telematics_id', 'x-xx.a.xxxxxxxxxx.xx.xxx'));

        $this->assertTrue($rulePharmacy->passes('card_type', '2'));

        $this->assertFalse($rulePharmacy->passes('card_type', '1'));
        $this->assertFalse($rulePharmacy->passes('card_type', '3'));
        $this->assertFalse($rulePharmacy->passes('card_type', '9'));
        $this->assertFalse($rulePharmacy->passes('card_type', 'a'));

        //        $ruleUser = new CardType(User::class);
        //
        //        $this->assertTrue($ruleUser->passes('', 'x-xx.1.xxxxxxxxxx.xx.xxx'));
        //        $this->assertTrue($ruleUser->passes('', 'x-xx.3.xxxxxxxxxx.xx.xxx'));
        //
        //        $this->assertFalse($ruleUser->passes('', 'x-xx.2.xxxxxxxxxx.xx.xxx'));
        //        $this->assertFalse($ruleUser->passes('', 'x-xx.4.xxxxxxxxxx.xx.xxx'));
        //
        //        $this->assertFalse($ruleUser->passes('', 'x-xx.x.xxxxxxxxxx.xx.xxx'));
        //        $this->assertFalse($ruleUser->passes('', 'x-xx.0.xxxxxxxxxx.xx.xxx'));
        //        $this->assertFalse($ruleUser->passes('', 'x-xx.5.xxxxxxxxxx.xx.xxx'));
        //        $this->assertFalse($ruleUser->passes('', 'x-xx.a.xxxxxxxxxx.xx.xxx'));
    }
}
