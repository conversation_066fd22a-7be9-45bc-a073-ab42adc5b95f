<?php

namespace Tests\Unit\Actions\DocSpaces;

use App\Actions\DocSpaces\CreateRiseSDRUserAction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AbstractRiseSDRActionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    // todo: AP-1032
    public function test_if_users_doc_spaces_or_doc_space_groups_were_seeded()
    {
        $sdrUserId = collect(config('sdr.seeder.users'))->first();
        $sdrGroupId = collect(config('sdr.seeder.docSpaceGroups'))->first();
        $sdrDocSpaceId = collect(config('sdr.seeder.docSpaces'))->first();
        $createGroupAction = new CreateRiseSDRUserAction;

        $this->assertFalse($createGroupAction->isSeeded());

        $this->assertTrue(
            $createGroupAction->isSeeded(
                sdrUserId: $sdrUserId
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: '123'
            )
        );

        $this->assertTrue(
            $createGroupAction->isSeeded(
                sdrGroupId: $sdrGroupId
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrGroupId: '123'
            )
        );

        $this->assertTrue(
            $createGroupAction->isSeeded(
                sdrDocSpaceId: $sdrDocSpaceId
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrDocSpaceId: '123'
            )
        );

        $this->assertTrue(
            $createGroupAction->isSeeded(
                sdrUserId: $sdrUserId,
                sdrGroupId: $sdrGroupId
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: $sdrUserId,
                sdrGroupId: '123'
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: '123',
                sdrGroupId: $sdrGroupId
            )
        );

        $this->assertTrue(
            $createGroupAction->isSeeded(
                sdrUserId: $sdrUserId,
                sdrDocSpaceId: $sdrDocSpaceId
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: $sdrUserId,
                sdrDocSpaceId: '123'
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: '123',
                sdrDocSpaceId: $sdrDocSpaceId
            )
        );

        $this->assertTrue(
            $createGroupAction->isSeeded(
                sdrGroupId: $sdrGroupId,
                sdrDocSpaceId: $sdrDocSpaceId
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrGroupId: $sdrGroupId,
                sdrDocSpaceId: '123'
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrGroupId: '123',
                sdrDocSpaceId: $sdrDocSpaceId
            )
        );

        $this->assertTrue(
            $createGroupAction->isSeeded(
                sdrUserId: $sdrUserId,
                sdrGroupId: $sdrGroupId,
                sdrDocSpaceId: $sdrDocSpaceId
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: $sdrUserId,
                sdrGroupId: $sdrGroupId,
                sdrDocSpaceId: '123'
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: $sdrUserId,
                sdrGroupId: '123',
                sdrDocSpaceId: $sdrDocSpaceId
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: '123',
                sdrGroupId: $sdrGroupId,
                sdrDocSpaceId: $sdrDocSpaceId
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: $sdrUserId,
                sdrGroupId: '123',
                sdrDocSpaceId: '123'
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: '123',
                sdrGroupId: '123',
                sdrDocSpaceId: $sdrDocSpaceId
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: '123',
                sdrGroupId: $sdrGroupId,
                sdrDocSpaceId: '123'
            )
        );

        $this->assertFalse(
            $createGroupAction->isSeeded(
                sdrUserId: '123',
                sdrGroupId: '123',
                sdrDocSpaceId: '123'
            )
        );
    }
}
