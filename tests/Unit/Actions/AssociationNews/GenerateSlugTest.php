<?php

namespace Tests\Unit\Actions\AssociationNews;

use App\Actions\AssociationNews\GenerateSlug;
use App\AssociationNews;
use Tests\TestCase;

class GenerateSlugTest extends TestCase
{
    public function test_it_generates_slug(): void
    {
        $slug = GenerateSlug::execute('Das ist ein Test');

        $this->assertSame('das-ist-ein-test', $slug);
    }

    public function test_it_appends_number_if_slug_is_used(): void
    {
        [$user, $association] = $this->createAssociationUser();

        AssociationNews::factory()->for($association)->create([
            'slug' => 'das-ist-ein-test',
        ]);

        $slug = GenerateSlug::execute('Das ist ein Test');

        $this->assertSame('das-ist-ein-test-1', $slug);
    }
}
