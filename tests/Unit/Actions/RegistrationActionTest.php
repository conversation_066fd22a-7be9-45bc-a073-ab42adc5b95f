<?php

namespace Tests\Unit\Actions;

use App\Actions\Auth\RegistrationAction;
use App\Actions\Users\CreateUserAction;
use App\BrochureCode;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use <PERSON><PERSON>y\MockInterface;
use Tests\TestCase;

class RegistrationActionTest extends TestCase
{
    use RefreshDatabase;

    public function test_an_instant_registration_can_be_created()
    {
        Http::fake();
        $user = $this->createPharmacyUser();

        $data = array_merge(BrochureCode::factory()->definition(), [
            'email' => $user->idp_email,
            'is_company' => true,
            'house_number' => '1',
            'telematics_id' => '123',
        ]);

        $this->mock(CreateUserAction::class, function (MockInterface $mock) use ($user) {
            $mock->expects(
                'setSalutation->setTitle->setFirstName->setLastName->setLoginEmail->setNotificationsEmail->setPhone->setPharmacyUser->setCompany->createUser'
            )->andReturn($user);
        });

        (new RegistrationAction)->createInstantRegistration($data);

        $this->assertDatabaseHas('brochure_codes', [
            'user_id' => $user->id,
        ]);
    }

    public function test_creating_the_user_fails()
    {
        $this->expectException(Exception::class);
        Http::fake();

        (new RegistrationAction)->createInstantRegistration(BrochureCode::factory()->definition());

        $this->assertDatabaseEmpty('brochure_codes');
        $this->assertDatabaseEmpty('users');
        Http::assertNothingSent();
    }
}
