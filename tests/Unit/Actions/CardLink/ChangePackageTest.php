<?php

namespace Tests\Unit\Actions\CardLink;

use App\Actions\CardLink\ChangePackage;
use App\Enums\CardLink\CardLinkOrderStatusEnum;
use App\Enums\CardLink\CardLinkPackageChangeEnum;
use App\Enums\CardLink\CardLinkPackageEnum;
use App\Mail\CardLink\CardLinkPackageChangedMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class ChangePackageTest extends TestCase
{
    use RefreshDatabase;

    public function test_upgrade(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy, $owner, [
            'status' => CardLinkOrderStatusEnum::Ordered->value,
            'ordered_at' => now(),
            'order_information' => [
                'order_id' => 1,
                'package' => CardLinkPackageEnum::Small->value,
                'show_in_apoguide' => (bool) random_int(0, 1),
                'activate_apo_guide_vendor' => (bool) random_int(0, 1),
            ],
        ]);

        ChangePackage::execute($cardLinkOrder, CardLinkPackageEnum::Unlimited);

        $cardLinkOrder->refresh();
        $this->assertSame($cardLinkOrder->order_information['package'], CardLinkPackageEnum::Unlimited->value);
        Mail::assertQueued(CardLinkPackageChangedMail::class, function (CardLinkPackageChangedMail $mail) use ($owner, $cardLinkOrder) {
            return $mail->user->is($owner) && $mail->cardLinkOrder->is($cardLinkOrder) && $mail->changeType === CardLinkPackageChangeEnum::Upgrade;
        });
    }

    public function test_downgrade(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy, $owner, [
            'status' => CardLinkOrderStatusEnum::Ordered->value,
            'ordered_at' => now(),
            'order_information' => [
                'order_id' => 1,
                'package' => CardLinkPackageEnum::Unlimited->value,
                'show_in_apoguide' => (bool) random_int(0, 1),
                'activate_apo_guide_vendor' => (bool) random_int(0, 1),
            ],
        ]);

        ChangePackage::execute($cardLinkOrder, CardLinkPackageEnum::Small);

        $cardLinkOrder->refresh();
        $this->assertSame($cardLinkOrder->order_information['package'], CardLinkPackageEnum::Small->value);
        Mail::assertQueued(CardLinkPackageChangedMail::class, function (CardLinkPackageChangedMail $mail) use ($owner, $cardLinkOrder) {
            return $mail->user->is($owner) && $mail->cardLinkOrder->is($cardLinkOrder) && $mail->changeType === CardLinkPackageChangeEnum::Downgrade;
        });
    }

    public function test_nothing_to_change(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy, $owner, [
            'status' => CardLinkOrderStatusEnum::Ordered->value,
            'ordered_at' => now(),
            'order_information' => [
                'order_id' => 1,
                'package' => CardLinkPackageEnum::Medium->value,
                'show_in_apoguide' => (bool) random_int(0, 1),
                'activate_apo_guide_vendor' => (bool) random_int(0, 1),
            ],
        ]);

        ChangePackage::execute($cardLinkOrder, CardLinkPackageEnum::Medium);

        $cardLinkOrder->refresh();
        $this->assertSame($cardLinkOrder->order_information['package'], CardLinkPackageEnum::Medium->value);
        Mail::assertNothingQueued();
    }
}
