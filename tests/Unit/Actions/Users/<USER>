<?php

namespace Tests\Unit\Actions\Users;

use App\Actions\Users\DeletePharmacyEmployee;
use App\CompanyUser;
use App\Enums\PharmaceuticalService\PharmaceuticalServiceStatus;
use App\Enums\PharmaceuticalService\PharmaceuticalServiceTypeEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\Vaccinate\VaccinationStatus;
use App\PharmaceuticalService;
use App\PharmaceuticalServicePatient;
use App\Shift;
use App\ShiftPlan;
use App\ShiftPlanGroup;
use App\ShiftPlanGroupUser;
use App\Vaccination;
use App\VaccinationPatient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DeletePharmacyEmployeeActionTest extends TestCase
{
    use RefreshDatabase;

    private DeletePharmacyEmployee $action;

    protected function setUp(): void
    {
        parent::setUp();
        $this->action = app(DeletePharmacyEmployee::class);
    }

    public function test_it_deletes_shift_plan_data_correctly()
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyUser(isOwner: false);
        $pharmacy->assignUser($employee, PharmacyRoleEnum::EMPLOYEE);

        $shiftPlan = ShiftPlan::factory()->create(['owner_id' => $owner->id]);
        $shiftPlanGroup = ShiftPlanGroup::factory()->create(['shift_plan_id' => $shiftPlan->id]);
        $shiftPlanGroupUser = ShiftPlanGroupUser::factory()->create([
            'shift_plan_group_id' => $shiftPlanGroup->id,
            'user_id' => $employee->id,
        ]);

        $shift = Shift::factory()->create([
            'shift_plan_id' => $shiftPlan->id,
            'shift_plan_group_user_id' => $shiftPlanGroupUser->id,
        ]);

        $this->assertDatabaseHas('shift_plan_group_user', ['id' => $shiftPlanGroupUser->id]);
        $this->assertDatabaseHas('shifts', ['id' => $shift->id]);

        $this->action->execute($employee);

        $this->assertDatabaseMissing('shifts', ['id' => $shift->id]);
        $this->assertDatabaseMissing('shift_plan_group_user', ['id' => $shiftPlanGroupUser->id]);
        $this->assertDatabaseMissing('shift_plan_beta_users', ['user_id' => $employee->id]);
    }

    public function test_it_anonymizes_pharmaceutical_services_correctly()
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyUser(isOwner: false);
        $pharmacy->assignUser($employee, PharmacyRoleEnum::EMPLOYEE);

        $pharmaceuticalService = PharmaceuticalService::factory()->create([
            'user_id' => $employee->id,
            'pharmacy_id' => $pharmacy->id,
            'type' => PharmaceuticalServiceTypeEnum::MEASURE_BLOOD_PRESSURE,
            'status' => PharmaceuticalServiceStatus::FINISHED,
        ]);

        $patient = PharmaceuticalServicePatient::create([
            'ps_id' => $pharmaceuticalService->id,
            'first_name' => 'Test',
            'last_name' => 'Patient',
            'birthdate' => '1990-01-01',
        ]);

        $this->action->execute($employee);

        $pharmaceuticalService->refresh();
        $this->assertNull($pharmaceuticalService->user_id);
    }

    public function test_it_anonymizes_vaccinations_correctly()
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyUser(isOwner: false);
        $pharmacy->assignUser($employee, PharmacyRoleEnum::EMPLOYEE);

        $vaccination = Vaccination::factory()->create([
            'user_id' => $employee->id,
            'pharmacy_id' => $pharmacy->id,
            'status' => VaccinationStatus::FINISHED,
        ]);

        $vaccinationPatient = VaccinationPatient::create([
            'vaccination_id' => $vaccination->id,
            'first_name' => 'Test',
            'last_name' => 'Patient',
            'birthdate' => '1990-01-01',
        ]);

        $this->action->execute($employee);

        $vaccination->refresh();
        $this->assertNull($vaccination->user_id);
    }

    public function test_it_cleans_up_user_data_correctly()
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyUser(isOwner: false);
        $pharmacy->assignUser($employee, PharmacyRoleEnum::EMPLOYEE);

        $this->action->execute($employee);

        $this->assertDatabaseMissing('users', [
            'id' => $employee->id,
        ]);
    }

    public function test_it_cleans_up_company_user_data_correctly()
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyUser(isOwner: false);
        $pharmacy->assignUser($employee, PharmacyRoleEnum::EMPLOYEE);

        $companyUser = CompanyUser::create([
            'user_id' => $employee->id,
            'name' => 'Test Company',
        ]);

        $otherEmployee = $this->createPharmacyUser(isOwner: false);
        $pharmacy->assignUser($otherEmployee, PharmacyRoleEnum::EMPLOYEE);

        $userProfile = $otherEmployee->pharmacyProfile;
        $userProfile->update(['company_user_id' => $employee->id]);

        $this->action->execute($employee);

        $this->assertDatabaseMissing('company_users', ['user_id' => $employee->id]);

        $userProfile->refresh();
        $this->assertNull($userProfile->company_user_id);
    }

    public function test_it_cleans_up_user_profile_data_correctly()
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyUser(isOwner: false);
        $pharmacy->assignUser($employee, PharmacyRoleEnum::EMPLOYEE);

        $this->action->execute($employee);

        $this->assertDatabaseMissing('user_pharmacy_profiles', ['user_id' => $employee->id]);
    }
}
