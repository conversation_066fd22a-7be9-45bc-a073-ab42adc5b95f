<?php

namespace Tests\Unit\Actions\Users;

use App\Actions\Users\CreateRiseIDPUserAction;
use App\Enums\IDPField;
use App\Enums\IDPGroup;
use App\Enums\IDPSalutation;
use App\Helper\OIDCHelper;
use App\User;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Validation\ValidationException;
use Mockery\MockInterface;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class CreateRiseIDPUserActionTest extends TestCase
{
    use RefreshDatabase;

    public function test_error_if_nothing_set(): void
    {
        Http::fake();
        $this->expectException(Exception::class);

        $createRiseIDPUserAction = app(CreateRiseIDPUserAction::class);

        $createRiseIDPUserAction->createUser();
    }

    public function test_post_pharmacist_succeeds(): void
    {
        Http::fake();
        $this->mock(OIDCHelper::class, function ($mock) {
            $mock->shouldReceive('getUserManagementAccessToken')->andReturn('access-token');
        });

        User::factory()->create([
            'uuid' => 'your-uuid',
            'title' => 'Mr',
            'salutation' => IDPSalutation::MR->value,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '123456789',
        ]);

        $this->assertDatabaseHas('users', ['uuid' => 'your-uuid']);

        $action = app(CreateRiseIDPUserAction::class);

        $action->setUuid('your-uuid')
            ->setTitle('Mr')
            ->setSalutation(IDPSalutation::MR)
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->setEmailVerified(true)
            ->setPhone('123456789')
            ->setPhoneNumberVerified(false);

        $expectedPayload = [
            IDPField::UUID => 'your-uuid',
            IDPField::TITLE => 'Mr',
            IDPField::SALUTATION => IDPSalutation::MR->value,
            IDPField::FIRST_NAME => 'John',
            IDPField::LAST_NAME => 'Doe',
            IDPField::LOGIN_EMAIL => '<EMAIL>',
            IDPField::EMAIL_VERIFIED => true,
            IDPField::PHONE => '123456789',
            IDPField::PHONE_NUMBER_VERIFIED => false,
            IDPField::GROUPS => [IDPGroup::PHARMACIST->value],
            IDPField::PASSWORD => null,
        ];

        $expectedUrl = config('oidc-auth.provider.adminUrl').'/pharmacists/v1';

        $action->createUser();

        Http::assertSent(static function ($request) use ($expectedUrl, $expectedPayload) {
            return $request->url() === $expectedUrl &&
                $request->data() === $expectedPayload;
        });
    }

    public function test_post_pharmacist_reports_error_on_redirect(): void
    {
        Http::fake([
            config('oidc-auth.provider.adminUrl').'/pharmacists/v1' => Http::response('', 301),
        ]);
        $this->mock(OIDCHelper::class, function (MockInterface $mock) {
            $mock->shouldReceive('getUserManagementAccessToken')->andReturn('access-token');
        });
        $this->expectException(ValidationException::class);

        User::factory()->create([
            'uuid' => 'your-uuid',
            'title' => 'Mr',
            'salutation' => IDPSalutation::MR->value,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '123456789',
        ]);

        $this->assertDatabaseHas('users', ['uuid' => 'your-uuid']);

        $action = app(CreateRiseIDPUserAction::class);

        $action->setUuid('your-uuid')
            ->setTitle('Mr')
            ->setSalutation(IDPSalutation::MR)
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->setEmailVerified(true)
            ->setPhone('123456789')
            ->setPhoneNumberVerified(false);

        $action->createUser();
    }

    public function test_post_pharmacist_reports_error_on_any_status_gte_401(): void
    {
        Http::fake([
            config('oidc-auth.provider.adminUrl').'/pharmacists/v1' => Http::response('', 401),
        ]);
        $this->mock(OIDCHelper::class, function (MockInterface $mock) {
            $mock->shouldReceive('getUserManagementAccessToken')->andReturn('access-token');
        });
        $this->expectException(ValidationException::class);

        User::factory()->create([
            'uuid' => 'your-uuid',
            'title' => 'Mr',
            'salutation' => IDPSalutation::MR->value,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '123456789',
        ]);

        $this->assertDatabaseHas('users', ['uuid' => 'your-uuid']);

        $action = app(CreateRiseIDPUserAction::class);

        $action->setUuid('your-uuid')
            ->setTitle('Mr')
            ->setSalutation(IDPSalutation::MR)
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->setEmailVerified(true)
            ->setPhone('123456789')
            ->setPhoneNumberVerified(false);

        $action->createUser();
    }

    public function test_validation_fails(): void
    {
        Http::fake([
            '*' => Http::response([
                'error_details' => [
                    [
                        'field_path' => 'uuid',
                        'message' => 'Not a valid UUID',
                        'code' => '107',
                        'invalid_value' => 'keineUUID',
                    ],
                    [
                        'field_path' => 'phoneNumber',
                        'message' => 'ERROR [101]: Value consisting only of whitespaces is not allowed',
                        'code' => '101',
                        'invalid_value' => ' ',
                    ],
                    [
                        'field_path' => 'passwordHash',
                        'message' => "ERROR [104]: 'sdfsdf' is not a bcrypt hash",
                        'regexp' => '^\$2[ayb]?\$\\d{2}\$[.\\/A-Za-z0-9]{53,55}|$',
                        'code' => '104',
                        'invalid_value' => 'sdfsdf',
                    ],
                    [
                        'field_path' => 'email',
                        'message' => "ERROR [103]: 'meine email' is not a valid email address",
                        'regexp' => '^(?!\\.)(?=.{5,256}$)[^\\r\\n<>]+@((?!\\.)[^\\r\\n<>.]+\\.)+(?!\\.)[^.\\r\\n<>]+|$',
                        'code' => '103',
                        'invalid_value' => 'meine email',
                    ],
                    [
                        'field_path' => 'familyName',
                        'message' => 'ERROR [101]: Value must not be longer than 250 characters',
                        'min' => 1,
                        'max' => 250,
                        'code' => '101',
                        'invalid_value' => '',
                    ],
                    [
                        'field_path' => 'familyName',
                        'message' => 'ERROR [100]: Required field is not defined',
                        'code' => '100',
                        'invalid_value' => '',
                    ],
                    [
                        'field_path' => 'salutation',
                        'message' => 'ERROR [102]: Valid salutations are (Herr|Frau|Divers)',
                        'regexp' => '(?:Herr|Frau|Divers)?',
                        'code' => '102',
                        'invalid_value' => 'Fraulein',
                    ],
                    [
                        'field_path' => 'title',
                        'message' => 'ERROR [101]: Value must not be longer than 100 characters',
                        'min' => 0,
                        'max' => 100,
                        'code' => '101',
                        'invalid_value' => 'Profffffesssor',
                    ],
                    [
                        'field_path' => 'unknown_field_name',
                        'message' => 'Not a valid UUID',
                        'code' => '99',
                        'invalid_value' => 'keineUUID',
                    ],
                ],
            ], Response::HTTP_BAD_REQUEST),
        ]);

        $this->mock(OIDCHelper::class, function (MockInterface $mock) {
            $mock->shouldReceive('getUserManagementAccessToken')->andReturn('access-token');
        });

        $action = app(CreateRiseIDPUserAction::class);

        $action->setUuid('your-uuid')
            ->setTitle('Mr')
            ->setSalutation(IDPSalutation::MR)
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->setEmailVerified(true)
            ->setPhone('123456789')
            ->setPhoneNumberVerified(false);

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('UUID ist keine gültige UUID. (and 8 more errors)');

        $action->createUser();
    }
}
