<?php

namespace Tests\Unit\Seeder;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class AuthorSeederTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_runs()
    {
        Storage::fake('local');

        Artisan::call('db:seed --class=AuthorSeeder');

        $this->assertDatabaseHas('authors', [
            'email' => '<EMAIL>',
        ]);
    }
}
