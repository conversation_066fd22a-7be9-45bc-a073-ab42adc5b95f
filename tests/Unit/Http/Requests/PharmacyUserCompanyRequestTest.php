<?php

namespace Tests\Unit\Http\Requests;

use App\Enums\AssociationPermissionsEnum;
use App\Enums\SalutationEnum;
use App\Http\Requests\PharmacyUserCompanyRequest;
use Faker\Factory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class PharmacyUserCompanyRequestTest extends TestCase
{
    use RefreshDatabase;

    public function test_authorize(): void
    {
        $request = new PharmacyUserCompanyRequest;

        $this->assertTrue($request->authorize());
    }

    public function test_rules(): void
    {
        $data = $this->provideValidData();

        $request = new PharmacyUserCompanyRequest;

        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->passes());
    }

    public function test_allow_duplicate_emails(): void
    {
        $user = $this->createPharmacyUser();
        $data = $this->provideValidData();
        $data['login_email'] = $user->idp_email;
        $data['notifications_email'] = $user->email;

        $request = new PharmacyUserCompanyRequest;

        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->passes());
    }

    public function test_rules_failed(): void
    {
        $data = $this->provideValidData();

        $request = new PharmacyUserCompanyRequest;
        $data['salutation'] = 1;

        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
    }

    private function provideValidData(): array
    {
        $faker = Factory::create();

        return [
            'salutation' => SalutationEnum::DI,
            'title' => 'Dr.',
            'first_name' => $faker->firstName,
            'last_name' => $faker->lastName,
            'differing_notifications_email_enabled' => $faker->boolean,
            'notifications_email' => $faker->email,
            'login_email' => $faker->email,
            'phone' => $faker->phoneNumber,
            'permissions' => array_rand(array_keys(AssociationPermissionsEnum::getLabels()), random_int(2, 3)),
        ];
    }
}
