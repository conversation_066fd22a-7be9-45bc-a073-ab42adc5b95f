<?php

namespace Tests\Unit\Http\Middleware;

use App\Http\Middleware\UrlParameterPersistenceMiddleware;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Route;
use Tests\TestCase;

class UrlParameterPersistenceMiddlewareTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        config(['url-param-persistence.url-parameters' => [
            'sample-param',
            'sample-param2',
            'sample-param-to-be-renamed' => 'new-name',
            'sample-param-to-be-renamed2' => 'new-name2',
        ]]);

        Route::get('/dummy-test-route', static fn () => 'nice')
            ->middleware([UrlParameterPersistenceMiddleware::class]);
    }

    public function test_middleware_persists_url_param_to_session(): void
    {
        $this->get('/dummy-test-route?sample-param=abc&test=123')
            ->assertStatus(200);

        $this->assertEquals('abc', session('sample-param'));
        $this->assertNull(session('test'));
    }

    public function test_middleware_persists_url_param_with_different_name_to_session(): void
    {
        $this->get('/dummy-test-route?sample-param-to-be-renamed=abc')
            ->assertStatus(200);

        $this->assertEquals('abc', session('new-name'));
    }

    public function test_middleware_persists_multiple_url_params_to_session(): void
    {
        $this->get('/dummy-test-route?sample-param=abc&sample-param2=def&test=123')
            ->assertStatus(200);

        $this->assertEquals('abc', session('sample-param'));
        $this->assertEquals('def', session('sample-param2'));
    }

    public function test_middleware_persists_multiple_url_params_with_different_name_to_session(): void
    {
        $this->get('/dummy-test-route?sample-param-to-be-renamed=abc&sample-param-to-be-renamed2=def')
            ->assertStatus(200);

        $this->assertEquals('abc', session('new-name'));
        $this->assertEquals('def', session('new-name2'));
    }
}
