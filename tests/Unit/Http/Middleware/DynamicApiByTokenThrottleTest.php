<?php

namespace Tests\Unit\Http\Middleware;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Client;
use Laravel\Passport\Passport;
use Tests\helper\ApiHelper;
use Tests\TestCase;

class DynamicApiByTokenThrottleTest extends TestCase
{
    use ApiHelper, RefreshDatabase;

    /** @test */
    public function it_throttles_based_on_database_value()
    {
        Passport::actingAsClient(
            Client::factory()->create([
                'requests_per_minute' => 2,
            ]),
            ['pharmacies', 'pharmacies-all']
        );

        for ($x = 0; $x < 2; $x++) {
            $this
                ->json('GET', route('api.pharmacies.index'))
                ->assertStatus(200);
        }

        $this
            ->json('GET', route('api.pharmacies.index'))
            ->assertStatus(429);

        $this->travel(2)->minutes();

        $this
            ->json('GET', route('api.pharmacies.index'))
            ->assertStatus(200);

        Passport::actingAsClient(
            Client::factory()->create([
                'requests_per_minute' => 5,
            ]),
            ['pharmacies', 'pharmacies-all']
        );

        for ($x = 0; $x < 5; $x++) {
            $this
                ->json('GET', route('api.pharmacies.index'))
                ->assertStatus(200);
        }

        $this
            ->json('GET', route('api.pharmacies.index'))
            ->assertStatus(429);

        $this->travel(2)->minutes();

        $this
            ->json('GET', route('api.pharmacies.index'))
            ->assertStatus(200);
    }
}
