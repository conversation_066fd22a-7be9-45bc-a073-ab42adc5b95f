<?php

namespace Tests\Unit\Http\Integrations\Ia\UpdatePharmacyDataAPI\Requests;

use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Dto\PharmacyDto;
use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Requests\UpdateLogoRequest;
use PHPUnit\Framework\TestCase;

/**
 * @group PushPharmacyDataToIA
 */
class UpdateLogoRequestTest extends TestCase
{
    public function test_it_sets_the_correct_endpoint(): void
    {
        $pharmacyDTO = new PharmacyDto;

        $request = new UpdateLogoRequest($pharmacyDTO);

        $this->assertEquals('/update-logo', $request->resolveEndpoint());
    }

    public function test_it_sets_the_correct_body(): void
    {
        $pharmacyDTO = new PharmacyDto;
        $pharmacyDTO->partnerId = 'PartnerId';
        $pharmacyDTO->logo = 'logo_path';

        $request = new UpdateLogoRequest($pharmacyDTO);

        $expectedBody = [
            'pharmacyId' => 'PartnerId',
            'logos' => [
                [
                    'url' => 'logo_path',
                ],
            ],
        ];

        $this->assertEquals($expectedBody, $request->defaultBody());
    }
}
