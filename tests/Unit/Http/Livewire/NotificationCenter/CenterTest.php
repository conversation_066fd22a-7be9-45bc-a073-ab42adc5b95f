<?php

namespace Tests\Unit\Http\Livewire\NotificationCenter;

use App\ClosedNotification;
use App\Livewire\NotificationCenter\Center;
use App\Pharmacy;
use App\Support\NotificationCenter\NotificationList;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;
use Livewire\Livewire;
use ReflectionClass;
use Tests\helper\AccessHelper;
use Tests\TestCase;

/** @group ap-2182 */
class CenterTest extends TestCase
{
    use AccessHelper, RefreshDatabase;

    public function test_it_shows_a_notification_and_close_it(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->actingAs($owner);

        $reflection = new ReflectionClass(NotificationList::class);
        $staticPropertyValue = $reflection->getStaticPropertyValue('notifications');
        $reflection->setStaticPropertyValue('notifications', [TestNotification::class]);

        Livewire::actingAs($owner)
            ->test(Center::class)
            ->assertSet('count', 1)
            ->assertSet('color', 'red')
            ->assertDispatched('notification-center-update-color')
            ->assertDispatched('notification-center-update-count');

        $this->assertCount(0, $owner->closedNotifications);

        self::instantiateProtectedClass(TestNotification::class, $owner, $pharmacy)->markAsClosed();

        $this->assertCount(1, $owner->refresh()->closedNotifications);

        Livewire::actingAs($owner)
            ->test(Center::class)
            ->assertSet('count', 0)
            ->assertSet('color', 'gray')
            ->assertDispatched('notification-center-update-color')
            ->assertDispatched('notification-center-update-count');

        TestNotification::resetClosedNotification(
            closeableType: $pharmacy->getMorphClass(),
            closeableId: $pharmacy->id
        );

        $this->assertCount(0, $owner->refresh()->closedNotifications);

        Livewire::actingAs($owner)
            ->test(Center::class)
            ->assertSet('count', 1)
            ->assertSet('color', 'red')
            ->assertDispatched('notification-center-update-color')
            ->assertDispatched('notification-center-update-count');

        $reflection->setStaticPropertyValue('notifications', $staticPropertyValue);
    }
}

class TestNotification extends \App\Support\NotificationCenter\Notification
{
    protected function __construct(protected User $user, protected Pharmacy $pharmacy)
    {
        parent::__construct();
    }

    public function getKey(): string
    {
        return md5(self::class.'-'.$this->user->id.'-'.$this->pharmacy->id);
    }

    public function toLivewire(): array
    {
        return [
            'notificationClass' => self::class,
            'userId' => $this->user->id,
            'pharmacyId' => $this->pharmacy->id,
        ];
    }

    public static function fromLivewire(mixed $value): self
    {
        /** @var User $user */
        $user = User::find($value['userId']);
        /** @var Pharmacy $pharmacy */
        $pharmacy = Pharmacy::find($value['pharmacyId']);

        return new self(
            $user,
            $pharmacy
        );
    }

    public function isVisible(): bool
    {
        return $this->closedNotifications->doesntContain(function (ClosedNotification $closedNotification) {
            return $closedNotification->type === self::class
                && $closedNotification->user_id === $this->user->id
                && $closedNotification->closeable_type === $this->pharmacy::class
                && $closedNotification->closeable_id === $this->pharmacy->id;
        });
    }

    public function getTitle(): string
    {
        return 'User And Pharmacy TestNotification';
    }

    /**
     * @return array<string>
     */
    public function getContent(): array
    {
        return [$this->pharmacy->name];
    }

    public static function getNotifications(): Collection
    {
        $user = user();
        if (! $user instanceof User) {
            return collect();
        }

        $notifications = collect();

        foreach ($user->pharmacies as $pharmacy) {
            $notification = new self($user, $pharmacy);

            if ($notification->isVisible()) {
                $notifications->push($notification);
            }
        }

        return $notifications;
    }

    public function markAsClosed(): void
    {
        $this->closeNotification($this->pharmacy->getMorphClass(), $this->pharmacy->id);
    }
}
