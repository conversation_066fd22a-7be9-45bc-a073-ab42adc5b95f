<?php

namespace Tests\Unit\Http\Controller;

use App\BillingAddress;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserBillingAddressesControllerTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_shows_the_billing_addresses_page_for_pharmacy_users()
    {
        $user = $this->createPharmacyUser();
        $this->actingAs($user);

        $response = $this->get(route('users.billing-addresses'));

        $response->assertStatus(200);
        $response->assertViewIs('user.billing-addresses');
    }

    /** @test */
    public function it_returns_a_403_error_for_non_pharmacy_users()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $response = $this->get(route('users.billing-addresses'));

        $response->assertStatus(403);
        $response->assertSeeText('Zugriff verweigert');
    }

    /** @test */
    public function user_must_own_billing_address_to_update_it()
    {
        $user = $this->createPharmacyUser();
        $this->actingAs($user);
        $this->createPharmacy();

        $billingAddress = $this->createBillingAddress();
        $this->assertNotSame($user->id, $billingAddress->user_id);

        $response = $this->get(route('users.billing-addresses.upsert', $billingAddress->getRouteKey()));

        $response->assertStatus(403);
    }

    /** @test */
    public function it_shows_billing_address_update_page_for_billing_address_owner()
    {
        $user = $this->createPharmacyUser();
        $this->actingAs($user);

        $billingAddress = BillingAddress::factory()->create(['user_id' => $user->id]);

        $response = $this->get(route('users.billing-addresses.upsert', $billingAddress->getRouteKey()));

        $response->assertStatus(200);
        $response->assertViewIs('user.billing-addresses-upsert');
        $response->assertViewHas('billingAddress', $billingAddress);
    }

    /** @test */
    public function it_shows_the_billing_addresses_create_page_with_no_billing_address_for_pharmacy_users()
    {
        $user = $this->createPharmacyUser();
        $this->actingAs($user);

        $response = $this->get(route('users.billing-addresses.upsert'));

        $response->assertStatus(200);
        $response->assertViewIs('user.billing-addresses-upsert');
        $response->assertViewHas('billingAddress', null);
    }

    /** @test */
    public function it_returns_a_403_error_for_non_pharmacy_users_on_billing_address_create_page()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $response = $this->get(route('users.billing-addresses.upsert'));

        $response->assertStatus(403);
        $response->assertSeeText('Zugriff verweigert');
    }
}
