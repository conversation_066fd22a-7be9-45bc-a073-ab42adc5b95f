<?php

namespace Tests\Unit\Http\Controller;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DashboardControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_redirects_to_authorization(): void
    {
        $this
            ->get(route('dashboard'))
            ->assertRedirectContains(config('oidc-auth.provider.urlAuthorize'));
    }

    public function test_it_redirects_to_brochure_code_edit_because_code_is_not_verified(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        $user->brochureCode()->delete();
        $user->refresh();

        $this
            ->actingAs($user)
            ->get(route('dashboard'))
            ->assertRedirect(route('user.brochureCode.edit'));
    }

    public function test_it_shows_the_dashboard(): void
    {
        $user = $this->createPharmacyUser();

        $this
            ->actingAs($user)
            ->get(route('dashboard'))
            ->assertStatus(200);
    }
}
