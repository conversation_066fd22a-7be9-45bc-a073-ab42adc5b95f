<?php

namespace Tests\Unit\Http\Controller\Api;

use App\Association;
use App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Settings\TermsOfServiceSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Tests\helper\ApiHelper;
use Tests\helper\ResourceTestHelper;
use Tests\TestCase;

class OwnerControllerTest extends TestCase
{
    use ApiHelper, RefreshDatabase, ResourceTestHelper;

    /** @test */
    public function it_can_fetch_all_owners()
    {
        // these should be returned
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription(
            $pharmacy,
            BaseStripeProduct::make()
        );
        $count = 1;

        // these should not be returned
        [$owner1, $pharmacy1] = $this->createPharmacyUserWithPharmacy();
        $pharmacy1->generalSettings()->forceDelete();
        [$owner2, $pharmacy2] = $this->createPharmacyUserWithPharmacy();
        $pharmacy2->generalSettings()->forceDelete();
        $this->createLocalStripeSubscription($pharmacy2, BaseStripeProduct::make());
        $owner2->pharmacyProfile->update(['association_id' => $this->getAssociationWithContract(AssociationFrameworkContractEnum::PlusAssociationFrameworkContract)->id]);

        $this
            ->json('GET', route('api.owners.index'))
            ->assertUnauthorized();

        $this->actingAsClientWithScopes(['owners']);

        $this
            ->json('GET', route('api.owners.index'))
            ->assertJson(['meta' => ['per_page' => 15]])
            ->assertResourceHasCount($count);

        $this
            ->json('GET', route('api.owners.index'), [
                'per_page' => (config('api.pagination.max') + 50),
            ])
            ->assertJson(['meta' => ['per_page' => config('api.pagination.max')]])
            ->assertResourceHasCount($count);

        $this->actingAsClientWithScopes(['languages']);

        $this
            ->json('GET', route('api.owners.index'))
            ->assertStatus(403);
    }

    public function test_framework_contract_pharmacies_after_deadline(): void
    {
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since->clone()->subDay());

        // these should be returned
        [$ownerPlus, $pharmacyPlus] = $this->createPharmacyUserWithPharmacy();
        $ownerPlus->pharmacyProfile()->update(['association_id' => $this->getAssociationWithContract(AssociationFrameworkContractEnum::PlusAssociationFrameworkContract)->id]);
        [$ownerBase, $pharmacyBase] = $this->createPharmacyUserWithPharmacy();
        $ownerBase->pharmacyProfile()->update(['association_id' => $this->getAssociationWithContract(AssociationFrameworkContractEnum::BaseAssociationFrameworkContract)->id]);
        $count = 2;

        // these should not be returned
        [$owner1, $pharmacy1] = $this->createPharmacyUserWithPharmacy();
        $pharmacy1->generalSettings()->forceDelete();
        [$owner2, $pharmacy2] = $this->createPharmacyUserWithPharmacy();
        $pharmacy2->generalSettings()->forceDelete();
        $this->createLocalStripeSubscription($pharmacy2, BaseStripeProduct::make());
        $association = Association::factory()->create();
        $association->associationFrameworkContractHistories()->create([
            'contract' => AssociationFrameworkContractEnum::PlusAssociationFrameworkContract,
            'starts_at' => Carbon::create(2025, 1, 1),
            'ends_at' => Carbon::create(2099, 12, 31),
        ]);
        $owner2->pharmacyProfile->update(['association_id' => $association->id]);

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->addDay());
        $this
            ->json('GET', route('api.owners.index'))
            ->assertUnauthorized();

        $this->actingAsClientWithScopes(['owners']);
        try {
            $this
                ->json('GET', route('api.owners.index'))
                ->assertJson(['meta' => ['per_page' => 15]])
                ->assertResourceHasCount($count);
        } catch (\Exception $e) {
            dd($pharmacyPlus->name, $pharmacyBase->name, $pharmacy1->name, $pharmacy2->name, $e);
        }

        $this
            ->json('GET', route('api.owners.index'), [
                'per_page' => (config('api.pagination.max') + 50),
            ])
            ->assertJson(['meta' => ['per_page' => config('api.pagination.max')]])
            ->assertResourceHasCount($count);

        $this->actingAsClientWithScopes(['languages']);

        $this
            ->json('GET', route('api.owners.index'))
            ->assertStatus(403);
    }
}
