<?php

namespace Tests\Unit\Http\Controller\Api;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\PharmacyRoleEnum;
use App\Enums\Settings\PharmacySettingTypes;
use App\Pharmacy;
use App\Settings\AppSettings;
use App\TelematicsId;
use App\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\helper\ApiHelper;
use Tests\helper\ResourceTestHelper;
use Tests\TestCase;

class PharmacyControllerTest extends TestCase
{
    use ApiHelper, RefreshDatabase, ResourceTestHelper;

    /** @test */
    public function it_can_fetch_all_updated_pharmacies()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy2 = $this->createPharmacyForUser($user);
        $pharmacy3 = $this->createPharmacyForUser($user);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $this->createLocalStripeSubscription($pharmacy2, BaseStripeProduct::make());
        $this->createLocalStripeSubscription($pharmacy3, BaseStripeProduct::make());
        $pharmacy->update(['updated_at' => Carbon::parse('2018-01-01')]);

        $pharmacy->telematicsId()->create(TelematicsId::factory()->raw());
        $pharmacy2->telematicsId()->create(TelematicsId::factory()->raw());
        $pharmacy3->telematicsId()->create(TelematicsId::factory()->raw());

        $this->actingAsClientWithScopes(['pharmacies', 'pharmacies-ngda']);

        $this
            ->json('GET', route('api.pharmacies.index'))
            ->assertJson(['meta' => ['per_page' => 15]])
            ->assertResourceHasCount(3);
        $this
            ->json('GET', route('api.pharmacies.index'), ['per_page' => (config('api.pagination.max') + 50)])
            ->assertJson(['meta' => ['per_page' => config('api.pagination.max')]])
            ->assertResourceHasCount(3);

        $route = route('api.pharmacies.index').'?filter[updated_after]=31.12.2018';

        $this
            ->json('GET', $route)
            ->assertResourceHasCount(2);

        $route = route('api.pharmacies.index').'?filter[updated_after]=31.12.2017&filter[updated_before]=31.12.2018';

        $this
            ->json('GET', $route)
            ->assertResourceHasCount(1);

        $this->actingAsClientWithScopes(['languages', 'pharmacies-ngda']);

        $this
            ->json('GET', route('api.pharmacies.index'))
            ->assertStatus(403);
    }

    /** @test */
    public function the_ndga_has_only_access_to_pharmacies_that_allow_export_and_have_a_valid_tid()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy2 = $this->createPharmacyForUser($user);
        $pharmacy3 = $this->createPharmacyForUser($user);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $this->createLocalStripeSubscription($pharmacy2, BaseStripeProduct::make());
        $this->createLocalStripeSubscription($pharmacy3, BaseStripeProduct::make());

        $pharmacy->telematicsId()->create(TelematicsId::factory()->raw());
        $pharmacy2->telematicsId()->create(TelematicsId::factory()->raw());
        $pharmacy3->telematicsId()->create(TelematicsId::factory()->raw());

        $pharmacy4 = $this->createPharmacyForUser($user);
        $pharmacy4->subscribeToPlan('base');
        $pharmacy5 = $this->createPharmacyForUser($user);
        $pharmacy5->subscribeToPlan('base');

        $this->actingAsClientWithScopes(['pharmacies', 'pharmacies-ngda']);

        $this
            ->json('GET', route('api.pharmacies.index'))
            ->assertResourceHasCount(3);

        $pharmacy->update(['export_added_value_to_gematik' => false]);

        $this
            ->json('GET', route('api.pharmacies.index'))
            ->assertResourceHasCount(2);
    }

    /** @test */
    public function others_than_the_ngda_have_access_to_every_pharmacy()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy2 = $this->createPharmacyForUser($user);
        $pharmacy3 = $this->createPharmacyForUser($user);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $this->createLocalStripeSubscription($pharmacy2, BaseStripeProduct::make());
        $this->createLocalStripeSubscription($pharmacy3, BaseStripeProduct::make());

        $pharmacy->telematicsId()->create(TelematicsId::factory()->raw());
        $pharmacy2->telematicsId()->create(TelematicsId::factory()->raw());
        $pharmacy3->telematicsId()->create(TelematicsId::factory()->raw());

        $pharmacy4 = $this->createPharmacyForUser($user);
        $this->createLocalStripeSubscription($pharmacy4, BaseStripeProduct::make());
        $pharmacy5 = $this->createPharmacyForUser($user);
        $this->createLocalStripeSubscription($pharmacy5, BaseStripeProduct::make());

        $this->actingAsClientWithScopes(['pharmacies', 'pharmacies-all']);

        $this
            ->json('GET', route('api.pharmacies.index'))
            ->assertResourceHasCount(5);
    }

    /** @test */
    public function the_corona_rapid_test_filter_works()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy2 = $this->createPharmacyForUser($user);
        $pharmacy3 = $this->createPharmacyForUser($user);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $this->createLocalStripeSubscription($pharmacy2, BaseStripeProduct::make());
        $this->createLocalStripeSubscription($pharmacy3, BaseStripeProduct::make());

        $pharmacy->update(['corona_rapid_test' => true]);
        $pharmacy2->update(['corona_rapid_test' => false]);
        $pharmacy3->update(['corona_rapid_test' => false]);

        $this->actingAsClientWithScopes(['pharmacies', 'pharmacies-all']);

        $route = route('api.pharmacies.index');

        $this
            ->json('GET', $route)
            ->assertResourceHasCount(3);

        $route = route('api.pharmacies.index').'?filter[corona_rapid_test]=1';

        $this
            ->json('GET', $route)
            ->assertResourceHasCount(1);
    }

    // TODO: The tests run with sqlite, sqlite isn't able to handle points, thats why this tests fails
    // its just a reference for manual testing, if required.
    public function the_radius_filter_works()
    {
        $muenster = Pharmacy::factory()
            ->hasAttached(User::factory(), ['role_name' => PharmacyRoleEnum::OWNER])
            ->create([
                'latitude' => 51.955713,
                'longitude' => 7.627192,
            ]);

        $hamburg = Pharmacy::factory()
            ->hasAttached(User::factory(), ['role_name' => PharmacyRoleEnum::OWNER])
            ->create([
                'latitude' => 53.541326,
                'longitude' => 9.985318,
            ]);

        $this->actingAsClientWithScopes(['pharmacies', 'pharmacies-all']);

        $route = route('api.pharmacies.index');

        $this
            ->json('GET', $route)
            ->assertResourceHasCount(2);

        $route = route('api.pharmacies.index').'?filter[near]=53.02433524661317,20.418165438608085'; // Polen

        $this
            ->json('GET', $route)
            ->assertResourceHasCount(0);

        $route = route(
            'api.pharmacies.index'
        ).'?filter[near]=53.54407976411265,9.987702845330862,5'; // Nahe Hamburg 5 km radius

        $this
            ->json('GET', $route)
            ->assertResourceHasCount(1);

        $route = route(
            'api.pharmacies.index'
        ).'?filter[near]=53.53379005414063,10.057536419830171,1'; // Nahe Hamburg 1 km radius

        $this
            ->json('GET', $route)
            ->assertResourceHasCount(0);
    }

    /** @test */
    public function terms_of_use_must_be_accepted(): void
    {
        $appSettings = app(AppSettings::class);
        $currentTermsOfUseDeadline = $appSettings->terms_of_use_deadline;
        $appSettings->terms_of_use_deadline = Carbon::parse('2022-01-01');
        $appSettings->save();

        Pharmacy::factory()
            ->hasAttached(User::factory(), ['role_name' => PharmacyRoleEnum::OWNER])
            ->count(3)
            ->create();

        $this->actingAsClientWithScopes(['pharmacies', 'pharmacies-all']);

        $this
            ->json('GET', route('api.pharmacies.index'))
            ->assertResourceHasCount(0);

        $appSettings->terms_of_use_deadline = $currentTermsOfUseDeadline;
        $appSettings->save();
    }

    /** @test */
    public function accepted_terms_of_use_can_be_ignored(): void
    {
        $appSettings = app(AppSettings::class);
        $currentTermsOfUseDeadline = $appSettings->terms_of_use_deadline;
        $appSettings->terms_of_use_deadline = Carbon::parse('2022-01-01');
        $appSettings->save();

        Pharmacy::factory()
            ->hasAttached(User::factory(), ['role_name' => PharmacyRoleEnum::OWNER])
            ->count(3)
            ->create();

        $this->actingAsClientWithScopes(['pharmacies', 'pharmacies-ignore-tos', 'pharmacies-all']);

        $this
            ->json('GET', route('api.pharmacies.index'))
            ->assertResourceHasCount(3);

        $appSettings->terms_of_use_deadline = $currentTermsOfUseDeadline;
        $appSettings->save();
    }

    /** @test */
    public function show_pharmacy_is_protected_by_terms_of_use(): void
    {
        $appSettings = app(AppSettings::class);
        $currentTermsOfUseDeadline = $appSettings->terms_of_use_deadline;
        $appSettings->terms_of_use_deadline = Carbon::parse('2022-01-01');
        $appSettings->save();

        $user = $this->createPharmacyUser();

        $pharmacy = Pharmacy::factory()
            ->hasAttached($user, ['role_name' => PharmacyRoleEnum::OWNER])
            ->create();

        $this->actingAsClientWithScopes(['pharmacies', 'pharmacies-all']);

        $this
            ->json('GET', route('api.pharmacies.show', $pharmacy))
            ->assertStatus(403);

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, true);
        $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, true);

        $this
            ->json('GET', route('api.pharmacies.show', $pharmacy))
            ->assertStatus(200);

        $appSettings->terms_of_use_deadline = $currentTermsOfUseDeadline;
        $appSettings->save();
    }

    /**
     * @group AP-487
     *
     * subscriptions: 1. wl_base => ends_at 30.06.
     * 2. base => 02.07.
     */
    public function test_it_can_show_resubscribed_pharmacies(): void
    {
        $appSettings = app(AppSettings::class);
        $appSettings->terms_of_use_deadline = Carbon::parse('2022-01-01');
        $appSettings->save();
        $this->travelTo(Carbon::parse('2023-06-15'));

        $user = $this->createPharmacyUser();

        $pharmacy = Pharmacy::factory()
            ->associationWl()
            ->hasAttached($user, ['role_name' => PharmacyRoleEnum::OWNER])
            ->create();

        $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, true);
        $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, true);

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAsClientWithScopes(['pharmacies', 'pharmacies-all']);

        $this
            ->json('GET', route('api.pharmacies.show', $pharmacy))
            ->assertOk();

        $this->cancelLocalStripeSubscription($pharmacy->subscriptions->first());

        $this->travelTo(Carbon::parse('2025-07-01'));
        $pharmacy = $pharmacy->fresh();

        $this
            ->json('GET', route('api.pharmacies.show', $pharmacy))
            ->assertForbidden();

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $pharmacy->acceptTerms();
        $pharmacy = $pharmacy->fresh();

        $this
            ->json('GET', route('api.pharmacies.show', $pharmacy))
            ->assertOk();
    }
}
