<?php

namespace Tests\Unit\Http\Controller\PharmacyInfluenzaVaccination;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\Vaccinate\InfluenzaVaccinationStepEnum;
use App\Enums\Vaccinate\VaccinationTypeEnum;
use App\HealthInsuranceCompany;
use App\InfluenzaVaccination;
use App\VaccinationPatient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AcceptanceControllerTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function the_request_to_edit_route_is_successful(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $pharmacy->update([
            'does_influenza_vaccination' => true,
        ]);

        $vaccination = $pharmacy->vaccinations()->create([
            'association_id' => $pharmacy->owner()->pharmacyProfile->association_id,
            'user_id' => $user->id,
            'user_name' => $user->name,
            'type' => VaccinationTypeEnum::INFLUENZA,
            'date' => now(),
            'step' => InfluenzaVaccinationStepEnum::EDUCATIONAL,
            'health_insurance_company_id' => HealthInsuranceCompany::factory()->create()->id,
        ]);

        VaccinationPatient::factory()->create([
            'vaccination_id' => $vaccination->id,
        ]);

        InfluenzaVaccination::factory()->create([
            'vaccination_id' => $vaccination->id,
        ]);

        $this->actingAs($user)
            ->get(route('pharmacies.vaccinate.acceptance', [$pharmacy, $vaccination]))
            ->assertRedirect(
                route(InfluenzaVaccinationStepEnum::getRedirectRoute($vaccination), [$pharmacy, $vaccination])
            );

        $vaccination->update([
            'step' => InfluenzaVaccinationStepEnum::ACCEPTANCE,
        ]);

        $this->actingAs($user)
            ->get(route('pharmacies.vaccinate.acceptance', [$pharmacy, $vaccination]))
            ->assertStatus(200);
    }

    /** @test */
    public function the_edit_view_gets_correct_data()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $pharmacy->update([
            'does_influenza_vaccination' => true,
        ]);

        $vaccination = $pharmacy->vaccinations()->create([
            'association_id' => $pharmacy->owner()->pharmacyProfile->association_id,
            'user_id' => $user->id,
            'user_name' => $user->name,
            'type' => VaccinationTypeEnum::INFLUENZA,
            'date' => now(),
            'step' => InfluenzaVaccinationStepEnum::ACCEPTANCE,
            'health_insurance_company_id' => HealthInsuranceCompany::factory()->create()->id,
        ]);

        VaccinationPatient::factory()->create([
            'vaccination_id' => $vaccination->id,
        ]);

        InfluenzaVaccination::factory()->create([
            'vaccination_id' => $vaccination->id,
        ]);

        $this->actingAs($user)
            ->get(route('pharmacies.vaccinate.acceptance', [$pharmacy, $vaccination]))
            ->assertStatus(200)
            ->assertViewIs('pharmacy.vaccinateInfluenza.vaccinate_acceptance')
            ->assertViewHas('pharmacy', $pharmacy)
            ->assertViewHas('vaccination', $vaccination)
            ->assertViewHas('patient', $vaccination->vaccinationPatient);
    }

    /** @test */
    public function the_request_to_update_route_is_successful()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $pharmacy->update([
            'does_influenza_vaccination' => true,
        ]);

        $vaccination = $pharmacy->vaccinations()->create([
            'association_id' => $pharmacy->owner()->pharmacyProfile->association_id,
            'user_id' => $user->id,
            'user_name' => $user->name,
            'type' => VaccinationTypeEnum::INFLUENZA,
            'date' => now(),
            'step' => InfluenzaVaccinationStepEnum::ACCEPTANCE,
            'health_insurance_company_id' => HealthInsuranceCompany::factory()->create()->id,
        ]);

        VaccinationPatient::factory()->create([
            'vaccination_id' => $vaccination->id,
        ]);

        InfluenzaVaccination::factory()->create([
            'vaccination_id' => $vaccination->id,
        ]);

        $this->actingAs($user)
            ->post(route('pharmacies.vaccinate.acceptance-update', [$pharmacy, $vaccination]), [
                'questions' => null,
                'acceptance' => null,
                'noAcceptance' => null,
                'notes' => null,
                'notify_user' => null,
            ])
            ->assertInvalid();

        $this->actingAs($user)
            ->post(route('pharmacies.vaccinate.acceptance-update', [$pharmacy, $vaccination]), [
                'questions' => 'on',
                'acceptance' => 'on',
                'noAcceptance' => null,
                'notes' => null,
                'notify_user' => null,
            ])
            ->assertRedirect(route('pharmacies.vaccinate.documentation', [$pharmacy, $vaccination]));

        $this->actingAs($user)
            ->post(route('pharmacies.vaccinate.acceptance-update', [$pharmacy, $vaccination]), [
                'questions' => 'on',
                'acceptance' => null,
                'noAcceptance' => 'on',
                'notes' => null,
                'notify_user' => null,
            ])
            ->assertRedirect(route('pharmacies.vaccinate.eval-user', [$pharmacy, $vaccination]));
    }
}
