<?php

namespace Tests\Unit\Http\Controller\PharmacyInfluenzaVaccination;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\Vaccinate\VaccinationTypeEnum;
use App\InfluenzaVaccination;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class HealthInsuranceControllerTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function the_request_to_edit_route_is_successful(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $pharmacy->update([
            'does_influenza_vaccination' => true,
        ]);

        $vaccination = $pharmacy->vaccinations()->create([
            'association_id' => $pharmacy->owner()->pharmacyProfile->association_id,
            'user_id' => $user->id,
            'user_name' => $user->name,
            'type' => VaccinationTypeEnum::INFLUENZA,
            'date' => now(),
        ]);

        InfluenzaVaccination::factory()->create([
            'vaccination_id' => $vaccination->id,
        ]);

        $this->actingAs($user)
            ->get(route('pharmacies.vaccinate.healthInsuranceCompany', [$pharmacy, $vaccination]))
            ->assertStatus(200);
    }

    /** @test */
    public function the_edit_view_gets_correct_data()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $pharmacy->update([
            'does_influenza_vaccination' => true,
        ]);

        $vaccination = $pharmacy->vaccinations()->create([
            'association_id' => $pharmacy->owner()->pharmacyProfile->association_id,
            'user_id' => $user->id,
            'user_name' => $user->name,
            'type' => VaccinationTypeEnum::INFLUENZA,
            'date' => now(),
        ]);

        InfluenzaVaccination::factory()->create([
            'vaccination_id' => $vaccination->id,
        ]);

        $this->actingAs($user)
            ->get(route('pharmacies.vaccinate.healthInsuranceCompany', [$pharmacy, $vaccination]))
            ->assertStatus(200)
            ->assertViewIs('pharmacy.vaccinateInfluenza.vaccinate_healthInsuranceCompany')
            ->assertViewHas('companies', function (Collection $companies) {
                return $companies->first()->exists === false;
            })
            ->assertViewHas('pharmacy', $pharmacy)
            ->assertViewHas('vaccination', $vaccination);
    }
}
