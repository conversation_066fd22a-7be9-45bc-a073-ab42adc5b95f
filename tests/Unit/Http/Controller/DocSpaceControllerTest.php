<?php

namespace Tests\Unit\Http\Controller;

use App\DocSpace;
use App\Enums\SessionEnum;
use App\GuidedDocspaceCreationProcess;
use App\Jobs\RefreshDocSpaceUsageJob;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class DocSpaceControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_the_request_to_index_route_is_successful()
    {
        Queue::fake();

        [$owner, $pharmacy] = $this->createOwnerForSDR(
            uses_sdr: true,
            hasExtendedSubscriptionPlan: true, has2faEnabled: true);

        $docSpace = DocSpace::factory()->create([
            'pharmacy_id' => $pharmacy->id,
        ]);

        $this->get(route('sdr.doc-spaces', ['pharmacy' => $pharmacy]))
            ->assertStatus(200)
            ->assertViewIs('pharmacy.docspace.index');

        Queue::assertPushed(RefreshDocSpaceUsageJob::class);
    }

    public function test_the_request_to_create_route_is_successful()
    {
        [$owner, $pharmacy] = $this->createOwnerForSDR(
            uses_sdr: true,
            hasExtendedSubscriptionPlan: true, has2faEnabled: true);

        session()->push(SessionEnum::GUIDED_CREATION_ALREADY_SKIPPED->value, $pharmacy->id);

        $this->get(route('sdr.doc-spaces.create', ['pharmacy' => $pharmacy, 'details' => 0]))
            ->assertStatus(200)
            ->assertViewIs('pharmacy.docspace.form');
    }

    public function test_the_request_to_edit_route_is_successful()
    {
        Queue::fake();

        [$owner, $pharmacy] = $this->createOwnerForSDR(
            uses_sdr: true,
            hasExtendedSubscriptionPlan: true, has2faEnabled: true);

        $docSpace = DocSpace::factory()->create([
            'pharmacy_id' => $pharmacy->id,
        ]);

        $this->get(route('sdr.doc-spaces.show', ['pharmacy' => $pharmacy, 'docSpace' => $docSpace, 'details' => 0]))
            ->assertStatus(200)
            ->assertViewIs('pharmacy.docspace.form');
    }

    public function test_the_request_to_create_guided_route_is_successful()
    {
        [$owner, $pharmacy] = $this->createOwnerForSDR(
            uses_sdr: true,
            hasExtendedSubscriptionPlan: true, has2faEnabled: true);

        $this->get(route('sdr.create-guided-progress', ['pharmacy' => $pharmacy]))
            ->assertStatus(200)
            ->assertViewIs('pharmacy.docspace.create-guided-progress');
    }

    public function test_create_guided_progress_view_can_be_viewed_when_max_count_of_doc_spaces_was_reached(): void
    {
        [$owner, $pharmacy] = $this->createOwnerForSDR(true, true, true);

        DocSpace::factory()->count(DocSpace::MAX_SPACES)->create([
            'pharmacy_id' => $pharmacy->id,
        ]);

        $this->get(route('sdr.create-guided-progress', ['pharmacy' => $pharmacy]))
            ->assertStatus(200)
            ->assertViewIs('pharmacy.docspace.create-guided-progress');
    }

    public function test_the_guided_process_can_be_skipped()
    {
        [$owner, $pharmacy] = $this->createOwnerForSDR(
            uses_sdr: true,
            hasExtendedSubscriptionPlan: true, has2faEnabled: true);

        $this->post(route('sdr.create-guided', ['pharmacy' => $pharmacy]), [
            'selected_doc_space_option' => GuidedDocspaceCreationProcess::CREATE_MANUALLY,
        ])
            ->assertRedirect(route('sdr.doc-spaces', [$pharmacy]));

        $this->assertEquals(in_array($pharmacy->id, session(SessionEnum::GUIDED_CREATION_ALREADY_SKIPPED->value) ?? []), true);
    }

    public function test_the_guided_process_can_be_started()
    {
        Queue::fake();

        [$owner, $pharmacy] = $this->createOwnerForSDR(
            uses_sdr: true,
            hasExtendedSubscriptionPlan: true, has2faEnabled: true);

        $this->post(route('sdr.create-guided', ['pharmacy' => $pharmacy]), [
            'selected_doc_space_option' => \App\Enums\DocSpaceCreateGuidedOptions::PHARMACEUTICAL_DATA_ONLY->value,
        ])
            ->assertRedirect(route('sdr.create-guided-progress', [$pharmacy]));

        $this->assertEquals(GuidedDocspaceCreationProcess::count(), 1);
    }
}
