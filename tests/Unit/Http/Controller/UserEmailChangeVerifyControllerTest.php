<?php

namespace Tests\Unit\Http\Controller;

use App\Mail\UserChangeEmailMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class UserEmailChangeVerifyControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_shows_verified(): void
    {
        $user = $this->createPharmacyUser();

        $this->actingAs($user)->get(route('user.email.showVerified'))
            ->assertOk();
    }

    public function test_it_doesnt_show_verified_because_no_user(): void
    {
        $this->get(route('user.email.showVerified'))
            ->assertFound();
    }

    public function test_it_handles_request(): void
    {
        $user = $this->createPharmacyUser();
        $newEmail = '<EMAIL>';
        $verificationTimeInSeconds = 3600;

        $email = new UserChangeEmailMail($user, $newEmail);

        $this->assertFalse(Cache::has('emailChanges.user.'.$user->id.'.url'));

        Cache::put('emailChanges.email.'.$newEmail, $user->id, $verificationTimeInSeconds);
        Cache::put('emailChanges.user.'.$user->id, $newEmail, $verificationTimeInSeconds);
        Cache::put(
            'emailChanges.user.'.$user->id.'.url',
            sha1($email->actionUrl),
            $verificationTimeInSeconds
        );

        $this->assertTrue(Cache::has('emailChanges.user.'.$user->id.'.url'));

        $this->actingAs($user)->get($email->actionUrl)
            ->assertOk();

        $this->assertFalse(Cache::has('emailChanges.user.'.$user->id.'.url'));
    }

    public function test_it_doesnt_handle_request_because_no_valid_cache_value(): void
    {
        $user = $this->createPharmacyUser();
        $newEmail = '<EMAIL>';

        $email = new UserChangeEmailMail($user, $newEmail);

        $this->actingAs($user)->get($email->actionUrl)
            ->assertForbidden();

        $this->assertFalse(Cache::has('emailChanges.user.'.$user->id.'.url'));
    }
}
