<?php

namespace Tests\Unit\Http\Controller;

use App\Apomail;
use App\Enums\ApomailStatus;
use App\Enums\PharmacyRoleEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApomailRegistrationControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_association_user_cannot_register(): void
    {
        [$user] = $this->createAssociationUser();

        $this->actingAs($user)->get(route('apo-mail-registration'))->assertStatus(403);
    }

    public function test_owner_cannot_register_without_subscription(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->actingAs($user)->get(route('apo-mail-registration'))->assertRedirect(route('pharmacies.subscription', $pharmacy));
    }

    public function test_employee_with_registered_apomail_cannot_register_again(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $employee->usingApomails()->create(Apomail::factory()->make(['status' => ApomailStatus::ACTIVE])->toArray());

        $this->actingAs($employee)->get(route('apo-mail-registration'))->assertStatus(403);
    }

    public function test_employee_with_no_reserved_apomail_cannot_register(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);

        $this->actingAs($employee)->get(route('apo-mail-registration'))->assertStatus(403);
    }

    public function test_employee_with_reserved_apomail_can_register(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $employee->usingApomails()->create(Apomail::factory()->make(['status' => ApomailStatus::RESERVED])->toArray());

        $this->actingAs($employee)->get(route('apo-mail-registration'))->assertRedirectToRoute('apo-mail-registration');
    }
}
