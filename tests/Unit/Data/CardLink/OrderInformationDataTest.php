<?php

namespace Tests\Unit\Data\CardLink;

use App\CardLinkOrder;
use App\Data\CardLink\OrderInformationData;
use App\Enums\CardLink\CardLinkOrderStatusEnum;
use App\Enums\CardLink\CardLinkPackageEnum;
use Carbon\Carbon;
use Tests\TestCase;

class OrderInformationDataTest extends TestCase
{
    public static string $deserializeErrorMessage = 'Be careful when changing the attributes of OrderInformationData.';

    public function test_it_serializes_and_deserializes_order_information_data(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $orderInformation = [
            'package' => CardLinkPackageEnum::Medium->value,
            'order_id' => 4,
            'transmitted_at' => Carbon::yesterday()->setTime(12, 0)->format('Y-m-d\TH:i:sP'),
            'show_in_apoguide' => true,
            'activate_apo_guide_vendor' => true,
            'current_usage' => 13,
            'usage_percentage' => 0.5,
            'limit' => 100,
            'usage_received_at' => Carbon::yesterday()->setTime(20, 0)->format('Y-m-d\TH:i:sP'),
        ];

        $order = $user->cardLinkOrders()->create([
            'status' => CardLinkOrderStatusEnum::Ordered,
            'pharmacy_id' => $pharmacy->id,
            'order_information' => $orderInformation,
        ]);

        $this->assertDatabaseHas('card_link_orders', [
            'id' => $order->id,
            'status' => CardLinkOrderStatusEnum::Ordered,
            'pharmacy_id' => $pharmacy->id,
            'order_information' => json_encode($orderInformation),
        ]);

        $orderFromDb = CardLinkOrder::findOrFail($order->id);

        $orderInformationFromDb = OrderInformationData::fromCardLinkOrder($orderFromDb);

        $this->assertNotNull($orderInformationFromDb);

        $this->assertEquals($orderInformation['package'], $orderInformationFromDb->package->value, 'Asserting package of OrderInformationData failed. '.self::$deserializeErrorMessage);
        $this->assertEquals($orderInformation['transmitted_at'], $orderInformationFromDb->transmittedAt->format('Y-m-d\TH:i:sP'), 'Asserting transmitted_at of OrderInformationData failed. '.self::$deserializeErrorMessage);
        $this->assertEquals($orderInformation['show_in_apoguide'], $orderInformationFromDb->showInApoGuide, 'Asserting show_in_apoguide of OrderInformationData failed. '.self::$deserializeErrorMessage);
        $this->assertEquals($orderInformation['activate_apo_guide_vendor'], $orderInformationFromDb->activateApoGuideVendor, 'Asserting activate_apo_guide_vendor of OrderInformationData failed. '.self::$deserializeErrorMessage);
    }
}
