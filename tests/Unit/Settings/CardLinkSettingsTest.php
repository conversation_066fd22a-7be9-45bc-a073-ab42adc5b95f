<?php

namespace Tests\Unit\Settings;

use App\Enums\CardLink\CardLinkDiscountEnum;
use App\Settings\CardLinkSettings;
use Carbon\Carbon;
use Tests\TestCase;

class CardLinkSettingsTest extends TestCase
{
    public function test_get_discount_id_returns_partner_id_if_pharmacy_is_partner_pharmacy(): void
    {
        $pharmacy = $this->createPharmacy();
        $pharmacy->cardLinkPartnerPharmacy()->create();

        $this->assertSame(CardLinkDiscountEnum::Partner, CardLinkSettings::getDiscount($pharmacy));
    }

    public function test_get_discount_id_returns_pre_activation_id_if_pre_activation_is_present(): void
    {
        Carbon::setTestNow('2024-09-09');

        $pharmacy = $this->createPharmacy();

        /** @var CardLinkSettings $settings */
        $settings = app(CardLinkSettings::class);
        $settings->preActivationDiscountUntil = Carbon::parse('2024-09-10');
        $settings->save();

        $this->assertSame(CardLinkDiscountEnum::ProductStartDiscount, CardLinkSettings::getDiscount($pharmacy));
    }

    public function test_get_discount_id_returns_discount_id_on_last_of_september(): void
    {
        Carbon::setTestNow('2024-09-30 15:00:00');

        $pharmacy = $this->createPharmacy();

        $this->assertSame(CardLinkDiscountEnum::ProductStartDiscount, CardLinkSettings::getDiscount($pharmacy));
    }

    public function test_get_discount_id_returns_null(): void
    {
        $pharmacy = $this->createPharmacy();

        /** @var CardLinkSettings $settings */
        $settings = app(CardLinkSettings::class);
        $settings->preActivationDiscountUntil = Carbon::parse('2024-09-08');
        $settings->save();

        $this->assertNull(CardLinkSettings::getDiscount($pharmacy));
    }
}
