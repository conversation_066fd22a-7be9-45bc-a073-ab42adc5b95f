<?php

namespace Tests\Unit\Nova\Metrics;

use App\CardLinkOrder;
use App\Nova\Metrics\CardLinkApoGuide;
use Tests\TestCase;

class CardLinkApoGuideTest extends TestCase
{
    public function test_it_gets_name(): void
    {
        $metric = new CardLinkApoGuide;

        $this->assertSame('ApoGuide', $metric->name());
    }

    public function test_it_gets_correct_results(): void
    {
        $metric = new CardLinkApoGuide;

        $this->assertSame([
            'Aktiviert' => 0.0,
            'Nicht aktiviert' => 0.0,
        ], $metric->calculate()->value);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        CardLinkOrder::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'user_id' => $user->id,
            'order_information' => [
                'activate_apo_guide_vendor' => true,
            ],
        ]);

        $this->assertSame([
            'Aktiviert' => 1.0,
            'Nicht aktiviert' => 0.0,
        ], $metric->calculate()->value);

        [$user2, $pharmacy2] = $this->createPharmacyUserWithPharmacy();
        CardLinkOrder::factory()->create([
            'pharmacy_id' => $pharmacy2->id,
            'user_id' => $user2->id,
            'order_information' => [
                'activate_apo_guide_vendor' => true,
            ],
        ]);

        $this->assertSame([
            'Aktiviert' => 2.0,
            'Nicht aktiviert' => 0.0,
        ], $metric->calculate()->value);

        [$user3, $pharmacy3] = $this->createPharmacyUserWithPharmacy();
        CardLinkOrder::factory()->create([
            'pharmacy_id' => $pharmacy3->id,
            'user_id' => $user3->id,
            'order_information' => [
                'activate_apo_guide_vendor' => false,
            ],
        ]);

        $this->assertSame([
            'Aktiviert' => 2.0,
            'Nicht aktiviert' => 1.0,
        ], $metric->calculate()->value);
    }
}
