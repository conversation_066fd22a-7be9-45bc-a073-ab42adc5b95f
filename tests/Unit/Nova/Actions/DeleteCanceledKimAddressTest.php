<?php

namespace Tests\Unit\Nova\Actions;

use App\Enums\KimAddressStatus;
use App\Enums\StaffRoleEnum;
use App\KimAddress;
use App\Nova\Actions\KIM\DeleteCanceledKimAddress;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Nova\Fields\ActionFields;
use Tests\TestCase;

class DeleteCanceledKimAddressTest extends TestCase
{
    use RefreshDatabase;

    public function test_kim_address_can_be_deleted()
    {
        $kimAddress = KimAddress::factory()->create([
            'status' => KimAddressStatus::CANCELED->value,
            'activated_at' => null,
        ]);

        $reason = 'Some reason';

        $fields = new ActionFields(collect(['reason' => $reason]), collect([]));

        $action = new DeleteCanceledKimAddress;

        $action->handle($fields, collect([$kimAddress]));

        $this->assertNotNull($kimAddress->deleted_at);
        $this->assertStringContainsString($reason, $kimAddress->deletion_reason);
    }

    public function test_kim_address_can_not_be_deleted_when_previously_activated()
    {
        $this->actingAs($this->createStaff(StaffRoleEnum::SUPPORT), 'staff');

        $kimAddress = KimAddress::factory()->create([
            'status' => KimAddressStatus::CANCELED->value,
            'activated_at' => now()->subDay(),
        ]);

        $fields = new ActionFields(collect([]), collect([]));

        $action = new DeleteCanceledKimAddress;

        $actual = $action->handle($fields, collect([$kimAddress]));

        $this->assertEquals('Bereits aktivierte KIM Adressen können nicht gelöscht werden.', $actual['danger']);
    }

    public function test_kim_address_can_not_be_deleted_when_not_in_canceled_status()
    {
        $this->actingAs($this->createStaff(StaffRoleEnum::SUPPORT), 'staff');

        $kimAddress = KimAddress::factory()->create([
            'status' => KimAddressStatus::ORDERED->value,
            'activated_at' => null,
        ]);

        $fields = new ActionFields(collect([]), collect([]));

        $action = new DeleteCanceledKimAddress;

        $actual = $action->handle($fields, collect([$kimAddress]));

        $this->assertEquals('KIM Adressen, die nicht storniert wurden, können nicht wiederhergestellt werden.', $actual['danger']);
    }
}
