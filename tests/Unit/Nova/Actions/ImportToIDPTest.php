<?php

namespace Tests\Unit\Nova\Actions;

use App\Nova\Actions\ImportToIDP;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Laravel\Nova\Fields\ActionFields;
use Tests\TestCase;

class ImportToIDPTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var array<string, mixed>
     */
    public $response;

    protected function setUp(): void
    {
        parent::setUp();

        $this->response = [
            'uuid' => 'u-u-i-d',
            'groups' => [
                'apotheker',
            ],
        ];
    }

    /**
     * @param  array<string, PromiseInterface>  $merge
     */
    protected function fakeHttp(array $merge = []): void
    {
        Http::fake(array_merge([
            config('oidc-auth.provider.urlAccessToken') => Http::response(
                (string) json_encode([
                    'access_token' => '123',
                ])
            ),
            config('oidc-auth.provider.adminUrl').'/users' => Http::response((string) json_encode($this->response), 404),
            config('oidc-auth.provider.adminUrl').'/users/*' => Http::response(),
            config('oidc-auth.provider.adminUrl').'/users' => Http::response(),
        ], $merge));
    }

    public function test_user_has_not_all_information_provided(): void
    {
        $this->fakeHttp();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        /** @var string $uuid */
        $uuid = $this->response['uuid'];
        $owner->uuid = $uuid;
        $owner->first_name = null;
        $owner->save();

        $models = collect([$owner]);
        $novaAction = new ImportToIDP;

        $fields = new ActionFields(collect(), collect());
        $message = $novaAction->handle($fields, $models);

        /** @phpstan-ignore-next-line ActionResponse implements ArrayAccess and cannot access offset of mixed */
        $this->assertEquals('Dieser Nutzer hat nicht alle Informationen ausgefüllt', $message['danger']);
    }

    public function test_user_exists_at_idp_check_failed(): void
    {
        $this->fakeHttp([
            config('oidc-auth.provider.adminUrl').'/users' => Http::response((string) json_encode($this->response)),
        ]);

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $models = collect([$owner]);
        $novaAction = new ImportToIDP;

        $fields = new ActionFields(collect(), collect());
        $message = $novaAction->handle($fields, $models);

        /** @phpstan-ignore-next-line ActionResponse implements ArrayAccess and cannot access offset of mixed */
        $this->assertEquals('Nutzer im IDP gefunden, UUID verschieden', $message['data']['title']);
    }

    public function test_user_was_successfully_imported(): void
    {
        $this->fakeHttp();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        /** @var string $uuid */
        $uuid = $this->response['uuid'];
        $owner->uuid = $uuid;
        $owner->save();

        $models = collect([$owner]);
        $novaAction = new ImportToIDP;

        $fields = new ActionFields(collect(), collect());
        $message = $novaAction->handle($fields, $models);

        /** @phpstan-ignore-next-line ActionResponse implements ArrayAccess and cannot access offset of mixed */
        $this->assertEquals('Der Nutzer wurde erfolgreich importiert', $message['message']);
    }

    public function test_no_user_model_provided(): void
    {
        $this->fakeHttp();

        $models = collect();
        $novaAction = new ImportToIDP;

        $fields = new ActionFields(collect(), collect());
        $message = $novaAction->handle($fields, $models);

        /** @phpstan-ignore-next-line ActionResponse implements ArrayAccess and cannot access offset of mixed */
        $this->assertEquals('Keine Daten zum Verarbeiten ausgewählt', $message['danger']);
    }
}
