<?php

namespace Tests\Unit\Nova\Actions;

use App\Nova\Actions\TestRestoreVaccinationImports;
use App\Pharmacy;
use App\TelematicsId;
use App\VaccinationImport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Nova\Fields\ActionFields;
use Tests\TestCase;

class TestRestoreVaccinationImportsTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function shows_warning_when_pharmacy_has_no_telematics_id()
    {
        $fields = new ActionFields(collect([]), collect([]));
        $pharmacy = Pharmacy::factory()->create();
        $collection = collect([
            $pharmacy,
        ]);

        $action = new TestRestoreVaccinationImports;

        $actual = $action->handle($fields, $collection);

        $this->assertEquals('Fehler: Apotheke hat keine Telematik-ID.', $actual['danger']);
    }

    /** @test */
    public function shows_warning_when_can_not_find_vaccination_imports_for_pharmacy()
    {
        $fields = new ActionFields(collect([]), collect([]));
        $pharmacy = Pharmacy::factory()->create();
        /** @var TelematicsId $telematicsId */
        $telematicsId = TelematicsId::factory()->create([
            'pharmacy_id' => $pharmacy->id,
        ]);
        $collection = collect([
            $pharmacy,
        ]);

        $action = new TestRestoreVaccinationImports;

        $actual = $action->handle($fields, $collection);

        $this->assertEquals('Keine Zertifikate für diese Apotheke gefunden.', $actual['danger']);

        VaccinationImport::factory()->count(5)->create();
        VaccinationImport::factory()->count(5)->create([
            'pharmacy_id' => $pharmacy->id,
            'telematics_id' => $telematicsId->fullId(),
        ]);

        $actual = $action->handle($fields, $collection);

        $this->assertEquals('Keine Zertifikate für diese Apotheke gefunden.', $actual['danger']);
    }

    /** @test */
    public function shows_correct_number_of_imports_for_pharmacy()
    {
        $fields = new ActionFields(collect([]), collect([]));
        $pharmacy = Pharmacy::factory()->create();
        /** @var TelematicsId $telematicsId */
        $telematicsId = TelematicsId::factory()->create([
            'pharmacy_id' => $pharmacy->id,
        ]);
        $collection = collect([
            $pharmacy,
        ]);

        $action = new TestRestoreVaccinationImports;

        VaccinationImport::factory()->count(5)->create();
        VaccinationImport::factory()->count(5)->create([
            'pharmacy_id' => $pharmacy->id,
            'telematics_id' => $telematicsId->fullId(),
        ]);
        VaccinationImport::factory()->count(5)->create([
            'pharmacy_id' => null,
            'telematics_id' => $telematicsId->fullId(),
        ]);

        $actual = $action->handle($fields, $collection);

        $this->assertEquals('5 Zertifikate können wiederhergestellt werden.', $actual['message']);
    }
}
