<?php

namespace Tests\Unit\Processes;

use App\Data\CardLink\OrderInformationData;
use App\Domains\Subscription\Application\Discounts\CardLinkPartnerPharmacyStripeDiscount;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Domains\Subscription\Domain\Actions\Subscription\AddRecurringProductsToSubscription;
use App\Enums\CardLink\CardLinkOrderStatusEnum;
use App\Enums\CardLink\CardLinkPackageEnum;
use App\Enums\PharmacyRoleEnum;
use App\Exceptions\CardLink\CannotSetCardLinkSettingsException;
use App\Http\Integrations\CardLinkService\Requests\SetCardLinkSettingsRequest;
use App\Mail\CardLink\CardLinkOrdered;
use App\Processes\OrderCardLink;
use App\Processes\Payloads\OrderCardLinkPayload;
use App\Settings\CardLinkSettings;
use Illuminate\Contracts\Debug\ExceptionHandler;
use Illuminate\Support\Facades\Mail;
use Saloon\Http\Faking\MockResponse;
use Saloon\Laravel\Facades\Saloon;
use Tests\TestCase;

class OrderCardLinkTest extends TestCase
{
    public function test_it_creates_a_card_link_order(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $orderInformation = OrderInformationData::from([
            'package' => CardLinkPackageEnum::Small,
        ]);

        $payload = new OrderCardLinkPayload($pharmacy, $owner, $owner, $orderInformation);
        $process = new OrderCardLink;
        $process->run($payload);

        $this->assertNotNull($pharmacy->refresh()->cardLinkOrder);
    }

    public function test_it_converts_a_reserved_card_link_order(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->cardLinkOrder()->create([
            'status' => CardLinkOrderStatusEnum::Reserved,
            'reserved_at' => now(),
            'user_id' => $owner->id,
        ]);

        $orderInformation = OrderInformationData::from([
            'package' => CardLinkPackageEnum::Small,
        ]);

        $payload = new OrderCardLinkPayload($pharmacy, $owner, $owner, $orderInformation);
        $process = new OrderCardLink;
        $process->run($payload);

        $this->assertNotNull($pharmacy->refresh()->cardLinkOrder);
    }

    public function test_it_sends_an_order_confirmation(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createRealStripeSubscription($pharmacy, BaseStripeProduct::make());

        $orderInformation = OrderInformationData::from([
            'package' => CardLinkPackageEnum::Small,
        ]);

        $payload = new OrderCardLinkPayload($pharmacy, $owner, $owner, $orderInformation);
        $process = new OrderCardLink;
        $process->run($payload);

        Mail::assertQueued(CardLinkOrdered::class);
    }

    public function test_subowner_can_order(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->createRealStripeSubscription($pharmacy, BaseStripeProduct::make());

        $orderInformation = OrderInformationData::from([
            'package' => CardLinkPackageEnum::Small,
        ]);

        $payload = new OrderCardLinkPayload($pharmacy, $subOwner, $owner, $orderInformation);
        $process = new OrderCardLink;
        $process->run($payload);

        Mail::assertQueued(CardLinkOrdered::class);
    }

    public function test_subowner_cannot_be_the_subscriber(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);

        $orderInformation = OrderInformationData::from([
            'package' => CardLinkPackageEnum::Small,
        ]);

        $this->expectException(\AssertionError::class);
        new OrderCardLinkPayload($pharmacy, $owner, $subOwner, $orderInformation);
    }

    public function test_discount_applied(): void
    {
        Mail::fake();

        app(CardLinkSettings::class)->transmissionEnabledAt = now()->clone()->subDay();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createRealStripeSubscription($pharmacy, BaseStripeProduct::make());
        $pharmacy->cardLinkPartnerPharmacy()->create();
        $pharmacy->refresh();

        $orderInformation = OrderInformationData::from([
            'package' => CardLinkPackageEnum::Small,
        ]);

        $payload = new OrderCardLinkPayload($pharmacy, $owner, $owner, $orderInformation);
        $process = new OrderCardLink;
        $process->run($payload);

        $this->assertTrue(collect($pharmacy->subscription()->asStripeSubscription()->items->last()->discounts)->contains(fn ($discount) => app(CardLinkPartnerPharmacyStripeDiscount::class)->getSettings()->discount_id));
    }

    public function test_it_does_not_send_to_services_because_transmission_is_disabled(): void
    {
        Mail::fake();

        app(CardLinkSettings::class)->transmissionEnabledAt = now()->clone()->addMonthNoOverflow();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $owner->updateQuietly(['subscription_service_id' => null]);
        $pharmacy->updateQuietly(['subscription_service_id' => null]);

        $owner->refresh();
        $pharmacy->refresh();

        $this->assertNull($owner->subscription_service_id);
        $this->assertNull($pharmacy->subscription_service_id);

        $orderInformation = OrderInformationData::from([
            'package' => CardLinkPackageEnum::Small,
        ]);

        $payload = new OrderCardLinkPayload($pharmacy, $owner, $owner, $orderInformation);
        $process = new OrderCardLink;
        $process->run($payload);

        $this->assertNull($owner->refresh()->subscription_service_id);
        $this->assertNull($pharmacy->refresh()->subscription_service_id);
        $this->assertNotNull($pharmacy->refresh()->cardLinkOrder);

        Mail::assertQueued(CardLinkOrdered::class);
    }

    public function test_rollback_is_performed(): void
    {
        $this->expectException(CannotSetCardLinkSettingsException::class);

        app(CardLinkSettings::class)->transmissionEnabledAt = now()->subMonth();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $orderInformation = OrderInformationData::from([
            'package' => CardLinkPackageEnum::Small,
        ]);

        Saloon::fake([
            SetCardLinkSettingsRequest::class => MockResponse::make([], status: 400),
        ]);

        $payload = new OrderCardLinkPayload($pharmacy, $owner, $owner, $orderInformation);
        $process = new OrderCardLink;
        $process->run($payload);
    }

    public function test_error_message_is_reported(): void
    {
        app(CardLinkSettings::class)->transmissionEnabledAt = now()->subMonth();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createRealStripeSubscription($pharmacy, BaseStripeProduct::make());

        $orderInformation = OrderInformationData::from([
            'package' => CardLinkPackageEnum::Small,
        ]);

        $this->mock(AddRecurringProductsToSubscription::class, function ($mock) {
            $mock->shouldReceive('execute')->andThrow(new \Exception('test error'));
        });

        $this->mock(ExceptionHandler::class, function ($mock) {
            $mock->shouldReceive('report')->once();
        });

        $payload = new OrderCardLinkPayload($pharmacy, $owner, $owner, $orderInformation);
        $process = new OrderCardLink;
        $process->run($payload);
    }
}
