<?php

namespace Tests\Unit\Processes;

use App\Enums\KimAddressStatus;
use App\KimAddress;
use App\Mail\KimAddressCancelledMail;
use App\Processes\CancelKimAddress;
use App\Processes\Payloads\CancelKimAddressPayload;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class CancelKimAddressTest extends TestCase
{
    public function test_reserved_kim_address_can_be_cancelled(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $kimAddress = KimAddress::factory()->for($pharmacy)->create([
            'status' => KimAddressStatus::RESERVED->value,
        ]);

        $payload = new CancelKimAddressPayload($kimAddress, $kimAddress->owner()->name, $kimAddress->email, $kimAddress->status);
        $process = new CancelKimAddress;
        $result = $process->run($payload);

        $this->assertNotFalse($result);

        Mail::assertQueued(KimAddressCancelledMail::class);
    }

    public function test_ordered_kim_address_can_be_cancelled(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $kimAddress = KimAddress::factory()->for($pharmacy)->create([
            'status' => KimAddressStatus::ORDERED->value,
        ]);

        $payload = new CancelKimAddressPayload($kimAddress, $kimAddress->owner()->name, $kimAddress->email, $kimAddress->status);
        $process = new CancelKimAddress;
        $result = $process->run($payload);

        $this->assertNotFalse($result);
    }

    public function test_cancelled_kim_address_cannot_be_cancelled(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $kimAddress = KimAddress::factory()->for($pharmacy)->create([
            'status' => KimAddressStatus::CANCELED->value,
        ]);

        $payload = new CancelKimAddressPayload($kimAddress, $kimAddress->owner()->name, $kimAddress->email, $kimAddress->status);
        $process = new CancelKimAddress;
        $result = $process->run($payload);

        $this->assertFalse($result);
    }

    public function test_activated_kim_address_cannot_be_cancelled(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $kimAddress = KimAddress::factory()->for($pharmacy)->create([
            'status' => KimAddressStatus::ACTIVATED->value,
        ]);

        $payload = new CancelKimAddressPayload($kimAddress, $kimAddress->owner()->name, $kimAddress->email, $kimAddress->status);
        $process = new CancelKimAddress;
        $result = $process->run($payload);

        $this->assertFalse($result);
    }

    public function test_scheduled_kim_address_cannot_be_cancelled(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $kimAddress = KimAddress::factory()->for($pharmacy)->create([
            'status' => KimAddressStatus::SCHEDULED->value,
        ]);

        $payload = new CancelKimAddressPayload($kimAddress, $kimAddress->owner()->name, $kimAddress->email, $kimAddress->status);
        $process = new CancelKimAddress;
        $result = $process->run($payload);

        $this->assertFalse($result);
    }

    public function test_cancellation_id_is_generated_if_not_set(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $kimAddress = KimAddress::factory()->for($pharmacy)->create([
            'status' => KimAddressStatus::RESERVED->value,
        ]);

        $payload = new CancelKimAddressPayload($kimAddress, $kimAddress->owner()->name, $kimAddress->email, $kimAddress->status);
        $process = new CancelKimAddress;

        /** @var CancelKimAddressPayload $payload */
        $payload = $process->run($payload);

        $this->assertNotNull($payload->cancellationId);
    }

    public function test_reserved_kim_address_is_deleted(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $kimAddress = KimAddress::factory()->for($pharmacy)->create([
            'status' => KimAddressStatus::RESERVED->value,
        ]);

        $payload = new CancelKimAddressPayload($kimAddress, $kimAddress->owner()->name, $kimAddress->email, $kimAddress->status);
        $process = new CancelKimAddress;
        $process->run($payload);

        $this->assertDatabaseMissing(
            'kim_addresses',
            $kimAddress->toArray(),
        );
    }

    public function test_ordered_kim_address_is_marked_as_deleted(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $kimAddress = KimAddress::factory()->for($pharmacy)->create([
            'status' => KimAddressStatus::ORDERED->value,
        ]);

        $payload = new CancelKimAddressPayload($kimAddress, $kimAddress->owner()->name, $kimAddress->email, $kimAddress->status);
        $process = new CancelKimAddress;
        $process->run($payload);

        $kimAddress->refresh();

        $this->assertSame($kimAddress->status, KimAddressStatus::CANCELED->value);
        $this->assertNotNull($kimAddress->cancellation_id);
        $this->assertNotNull($kimAddress->deactivated_at);
    }

    public function test_mail_is_sent_for_cancelled_kim_address(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $kimAddress = KimAddress::factory()->for($pharmacy)->create([
            'status' => KimAddressStatus::RESERVED->value,
        ]);

        $payload = new CancelKimAddressPayload($kimAddress, $kimAddress->owner()->name, $kimAddress->email, $kimAddress->status);
        $process = new CancelKimAddress;
        $process->run($payload);

        Mail::assertQueued(KimAddressCancelledMail::class);
    }

    public function test_cannot_reorder_a_cancelled_kim_address(): void
    {
        $this->expectException(UniqueConstraintViolationException::class);

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $address = new KimAddress([
            'email' => $email = '<EMAIL>',
            'status' => KimAddressStatus::CANCELED->value,
        ]);
        $address->billingAddress()->associate($pharmacy->billingAddress);
        $address->pharmacy()->associate($pharmacy);
        $address->save();

        KimAddress::create([
            'email' => $email,
            'status' => KimAddressStatus::RESERVED->value,
        ]);
    }

    public function test_mail_is_not_sent_when_there_is_no_related_pharmacy(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $kimAddress = KimAddress::factory()->create([
            'status' => KimAddressStatus::RESERVED->value,
        ]);

        $payload = new CancelKimAddressPayload($kimAddress, 'Some Owner', $kimAddress->email, $kimAddress->status);
        $process = new CancelKimAddress;
        $process->run($payload);

        Mail::assertNotSent(KimAddressCancelledMail::class);
    }
}
