<?php

namespace Tests\Unit\Console\Commands;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CreateDefaultBillingAddressesTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_creates_default_billing_addresses_for_all_pharmacies_without_one(): void
    {
        [, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->billingAddress()->delete();
        $pharmacy->billingAddress()->dissociate();
        $pharmacy->save();

        $this->assertNull($pharmacy->fresh()->billingAddress);

        $this->artisan('payment:create-default-billing-addresses');

        $this->assertNotNull($pharmacy->fresh()->billingAddress);
    }
}
