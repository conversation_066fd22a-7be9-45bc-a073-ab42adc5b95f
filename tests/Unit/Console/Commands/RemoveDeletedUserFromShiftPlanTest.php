<?php

namespace Tests\Unit\Console\Commands;

use App\Enums\PharmacyRoleEnum;
use App\Shift;
use App\ShiftPlan;
use App\ShiftPlanGroup;
use App\ShiftPlanGroupUser;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RemoveDeletedUserFromShiftPlanTest extends TestCase
{
    use RefreshDatabase;

    public function test_shift_plan_data_gets_deleted()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $employee1 = $this->createPharmacyUser(isOwner: false);
        $employee2 = $this->createPharmacyUser(isOwner: false);

        $pharmacy->assignUser($employee1, PharmacyRoleEnum::EMPLOYEE);
        $pharmacy->assignUser($employee2, PharmacyRoleEnum::EMPLOYEE);

        $shiftPlan = ShiftPlan::factory()->create([
            'owner_id' => $user->id,
        ]);

        $shiftPlanGroup = ShiftPlanGroup::factory()->create([
            'name' => 'Test Gruppe 123',
            'shift_plan_id' => $shiftPlan->id,
        ]);

        $shiftPlanGroupUser1 = ShiftPlanGroupUser::factory()->create([
            'shift_plan_group_id' => $shiftPlanGroup->id,
            'user_id' => $employee1->id,
        ]);

        $shiftPlanGroupUser2 = ShiftPlanGroupUser::factory()->create([
            'shift_plan_group_id' => $shiftPlanGroup->id,
            'user_id' => $employee2->id,
        ]);

        $shift1 = Shift::factory()->create([
            'name' => 'abc',
            'shift_plan_id' => $shiftPlan->id,
            'shift_plan_group_user_id' => $shiftPlanGroupUser1->id,
        ]);

        $shift2 = Shift::factory()->create([
            'name' => 'abc',
            'shift_plan_id' => $shiftPlan->id,
            'shift_plan_group_user_id' => $shiftPlanGroupUser2->id,
        ]);

        $employee1->deleteQuietly();
        $employee2->deleteQuietly();

        $this->artisan('app:remove-deleted-user-from-shift-plan');

        $this->assertEquals(0, ShiftPlanGroupUser::where('user_id', $employee1->id)->count());
        $this->assertEquals(0, ShiftPlanGroupUser::where('user_id', $employee2->id)->count());

        $this->assertEquals(0, Shift::where('shift_plan_group_user_id', $shiftPlanGroupUser1->id)->count());
        $this->assertEquals(0, Shift::where('shift_plan_group_user_id', $shiftPlanGroupUser2->id)->count());
    }
}
