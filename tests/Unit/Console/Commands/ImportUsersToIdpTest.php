<?php

namespace Tests\Unit\Console\Commands;

use App\Console\Commands\ImportUsersToIdp;
use App\Jobs\CreateOrUpdateUserAtIdp;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class ImportUsersToIdpTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_dispatches_for_all_users_that_meet_the_requirements()
    {
        $users = User::factory()->count(10)->create();
        $brokenUserFirstName = User::factory()->create(['first_name' => null]);
        $brokenUserLastName = User::factory()->create(['last_name' => null]);
        $brokenUserEmail = User::factory()->create(['email' => null]);
        $brokenUserEmailVerified = User::factory()->create(['email_verified_at' => null]);

        Queue::fake();

        $this->artisan(ImportUsersToIdp::class);

        Queue::assertPushed(CreateOrUpdateUserAtIdp::class, fn ($job) => $job->user->is($users->first()));
        Queue::assertNotPushed(CreateOrUpdateUserAtIdp::class, fn ($job) => $job->user->is($brokenUserEmail));
        Queue::assertNotPushed(CreateOrUpdateUserAtIdp::class, fn ($job) => $job->user->is($brokenUserFirstName));
        Queue::assertNotPushed(CreateOrUpdateUserAtIdp::class, fn ($job) => $job->user->is($brokenUserLastName));
        Queue::assertNotPushed(CreateOrUpdateUserAtIdp::class, fn ($job) => $job->user->is($brokenUserEmailVerified));
    }
}
