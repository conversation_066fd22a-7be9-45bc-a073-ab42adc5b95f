<?php

namespace Tests\Unit\Console\Commands;

use App\CovidVaccination;
use App\Enums\Vaccinate\VaccinationStatus;
use App\Enums\Vaccinate\VaccinationTypeEnum;
use App\Enums\VaccinationRKIStatus;
use App\Jobs\SendVaccinationToRki;
use App\Vaccination;
use App\VaccinationPatient;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;
use Tests\Unit\Traits\UsesRKIApi;

class DispatchSendingVaccinationsToRKITest extends TestCase
{
    use RefreshDatabase, UsesRKIApi;

    public function test_sends_failed_vaccinations()
    {
        Queue::fake();

        $this->createAllTestVaccinations();

        $this->artisan('app:dispatch-sending-vaccinations-to-rki --retry')->assertSuccessful();

        Queue::assertPushed(SendVaccinationToRki::class, 4);
        Queue::assertPushed(SendVaccinationToRki::class, function ($job) {
            return $job->vaccination->covidVaccination->rki_status == VaccinationRKIStatus::FAILED;
        });
    }

    public function test_sends_failed_vaccinations_with_dates()
    {
        Queue::fake();

        $this->createAllTestVaccinations();

        $this->artisan('app:dispatch-sending-vaccinations-to-rki --retry --from=2023-12-01 --to=2023-12-30')->assertSuccessful();

        Queue::assertPushed(SendVaccinationToRki::class, 2);
        Queue::assertPushed(SendVaccinationToRki::class, function ($job) {
            return $job->vaccination->covidVaccination->rki_status == VaccinationRKIStatus::FAILED;
        });
    }

    public function test_sends_vaccinations()
    {
        $this->createAllTestVaccinations();

        $this->artisan('app:dispatch-sending-vaccinations-to-rki')->assertSuccessful();

        Queue::assertPushed(SendVaccinationToRki::class, 4);
        Queue::assertPushed(SendVaccinationToRki::class, function ($job) {
            return $job->vaccination->covidVaccination->rki_status == VaccinationRKIStatus::CREATED;
        });
    }

    public function test_sends_vaccinations_with_dates()
    {
        Queue::fake();

        $this->createAllTestVaccinations();

        $this->artisan('app:dispatch-sending-vaccinations-to-rki --from=2023-12-01 --to=2023-12-30')->assertSuccessful();

        Queue::assertPushed(SendVaccinationToRki::class, 2);
        Queue::assertPushed(SendVaccinationToRki::class, function ($job) {
            return $job->vaccination->covidVaccination->rki_status == VaccinationRKIStatus::CREATED;
        });
    }

    public function test_does_not_send_influenza_vaccination_before_2023(): void
    {
        Queue::fake();

        $this->createAllTestVaccinations();

        $this->artisan('app:dispatch-sending-vaccinations-to-rki')->assertSuccessful();

        $this->createVaccination(VaccinationTypeEnum::INFLUENZA, VaccinationStatus::FINISHED, VaccinationRKIStatus::CREATED, '2022-12-15');

        Queue::assertPushed(SendVaccinationToRki::class, 4);
        Queue::assertPushed(SendVaccinationToRki::class, function ($job) {
            return $job->vaccination->covidVaccination->rki_status == VaccinationRKIStatus::CREATED;
        });
    }

    private function createAllTestVaccinations()
    {
        foreach ([VaccinationTypeEnum::COVID, VaccinationTypeEnum::INFLUENZA] as $vaccinationType) {
            foreach ([VaccinationRKIStatus::CREATED, VaccinationRKIStatus::FAILED] as $rkiStatus) {
                $this->createVaccination($vaccinationType, VaccinationStatus::FINISHED, $rkiStatus, Carbon::now()->format('Y-m-d'));
                $this->createVaccination($vaccinationType, VaccinationStatus::DRAFT, $rkiStatus, Carbon::now()->format('Y-m-d'));

                $this->createVaccination($vaccinationType, VaccinationStatus::FINISHED, $rkiStatus, '2023-12-15');
                $this->createVaccination($vaccinationType, VaccinationStatus::DRAFT, $rkiStatus, '2023-12-15');
            }
        }
    }

    private function createVaccination(string $type, int $status, int $rkiStatus, string $date)
    {
        Vaccination::factory()
            ->has(
                CovidVaccination::factory([
                    'rki_status' => $rkiStatus,
                ])
            )
            ->has(VaccinationPatient::factory())
            ->create([
                'type' => $type,
                'status' => $status,
                'reasons_to_abort' => null,
                'date' => $date,
            ]);
    }
}
