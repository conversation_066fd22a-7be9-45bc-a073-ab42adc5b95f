<?php

namespace Tests\Unit\Console\Commands;

use App\Pharmacy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SetPharmaciesIbmRegisteredTest extends TestCase
{
    use RefreshDatabase;

    public function test_set_pharmacies_ibm_registered()
    {
        Pharmacy::factory()->create();

        $this->artisan('pharmacies:set-ibm-registered')->assertExitCode(0);
    }

    public function test_set_pharmacies_ibm_registered_no_pharmacies()
    {
        $this->artisan('pharmacies:set-ibm-registered')->assertExitCode(0);
    }
}
