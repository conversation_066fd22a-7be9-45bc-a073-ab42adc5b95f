<?php

namespace Tests\Unit\Console\Commands;

use App\Console\Commands\DeleteOrphanedIntegrations;
use App\Integration;
use App\Integrations\NGDAIntegration;
use Illuminate\Support\Str;
use Tests\TestCase;

class DeleteOrphanedIntegrationsTest extends TestCase
{
    public function test_it_deletes_orphaned_integrations(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->setIntegration(
            new NGDAIntegration(
                id: '123',
                accessToken: Str::random(),
                refreshToken: Str::random(),
                refreshAt: now()->addWeek(),
                acceptedNnf: true
            )
        );

        $pharmacy->deleteQuietly();

        $this->assertEquals(1, Integration::query()->count());

        $this->artisan(DeleteOrphanedIntegrations::class);

        $this->assertEquals(0, Integration::query()->count());
    }

    public function test_it_does_not_delete_not_orphaned_integrations(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->setIntegration(
            new NGDAIntegration(
                id: '123',
                accessToken: Str::random(),
                refreshToken: Str::random(),
                refreshAt: now()->addWeek(),
                acceptedNnf: true
            )
        );

        $this->assertEquals(1, Integration::query()->count());

        $this->artisan(DeleteOrphanedIntegrations::class);

        $this->assertEquals(1, Integration::query()->count());
    }
}
