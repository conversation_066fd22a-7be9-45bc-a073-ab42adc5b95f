<?php

namespace Tests\Unit\Console\Commands;

use App\Jobs\CreateAndSendInvoice;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class DispatchInvoicesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Queue::fake();
    }

    public function test_missing_arguments(): void
    {
        $this->expectExceptionMessage(
            'Not enough arguments (missing: "from, till")'
        );
        $this->artisan('payment:dispatch-invoices')
            ->execute();
    }

    public function test_missing_till_argument(): void
    {
        $this->expectExceptionMessage(
            'Not enough arguments (missing: "till")'
        );
        $this->artisan('payment:dispatch-invoices 2021-01-01')
            ->execute();
    }

    public function test_invalid_from_argument(): void
    {
        $this->artisan('payment:dispatch-invoices invalid 2021-01-01')
            ->expectsOutput(
                'from entspricht nicht dem gültigen Format für Y-m-d.'
            );
    }

    public function test_invalid_till_argument(): void
    {
        $this->artisan('payment:dispatch-invoices 2021-01-01 invalid')
            ->expectsOutput(
                'till entspricht nicht dem gültigen Format für Y-m-d.'
            );
    }

    public function test_from_is_greater_than_till(): void
    {
        $this->artisan('payment:dispatch-invoices 2021-01-02 2021-01-01')
            ->expectsOutput(
                'till muss ein Datum nach dem from oder gleich dem from sein.'
            );
    }

    public function test_from_is_equal_to_till(): void
    {
        $this->artisan('payment:dispatch-invoices 2021-01-01 2021-01-01')
            ->expectsConfirmation(
                'Dispatching all invoices for orders between 2021-01-01 and 2021-01-01',
                'yes'
            )
            ->assertExitCode(0);
        Queue::assertNothingPushed();
    }

    public function test_orders_are_outside_of_date_range(): void
    {
        $this->createManySubscriptionOrders([
            [
                'started_at' => '2020-12-31 23:59:59',
            ],
            [
                'started_at' => '2021-02-01 00:00:00',
            ],
        ]);
        $this->artisan('payment:dispatch-invoices 2021-01-01 2021-01-31')
            ->expectsConfirmation(
                'Dispatching all invoices for orders between 2021-01-01 and 2021-01-31',
                'yes'
            )
            ->assertExitCode(0);
        Queue::assertPushed(CreateAndSendInvoice::class, 0);
    }

    public function test_two_jobs_dispatched(): void
    {
        $subscriptionOrder = $this->createSubscriptionOrder([
            'started_at' => '2021-01-01 00:00:00',
        ]);
        $this->createManySubscriptionOrders([
            [
                'started_at' => '2021-01-01 00:00:00',
                'orderable_id' => $subscriptionOrder->orderable_id,
                'orderable_type' => $subscriptionOrder->orderable_type,
            ],
            [
                'started_at' => '2021-01-02 00:00:00',
            ],
            [
                'started_at' => '2021-01-02 00:00:00',
                'invoice_id' => $this->createInvoice()->id,
            ],
        ]);
        $this->artisan('payment:dispatch-invoices 2021-01-01 2021-01-31')
            ->expectsConfirmation(
                'Dispatching all invoices for orders between 2021-01-01 and 2021-01-31',
                'yes'
            )
            ->assertExitCode(0);
        Queue::assertPushed(CreateAndSendInvoice::class, 2);
    }

    public function test_limited_jobs_dispatched(): void
    {
        $subscriptionOrder = $this->createSubscriptionOrder([
            'started_at' => '2021-01-01 00:00:00',
        ]);
        $this->createManySubscriptionOrders([
            [
                'started_at' => '2021-01-01 00:00:00',
                'orderable_id' => $subscriptionOrder->orderable_id,
                'orderable_type' => $subscriptionOrder->orderable_type,
            ],
            [
                'started_at' => '2021-01-02 00:00:00',
            ],
            [
                'started_at' => '2021-01-02 00:00:00',
                'invoice_id' => $this->createInvoice()->id,
            ],
        ]);
        $this->artisan('payment:dispatch-invoices 2021-01-01 2021-01-31 1')
            ->expectsConfirmation(
                'Dispatching 1 invoices for orders between 2021-01-01 and 2021-01-31',
                'yes'
            )
            ->assertExitCode(0);
        Queue::assertPushed(CreateAndSendInvoice::class, 1);
    }

    public function test_bundled_by_billing_address(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        [$user2, $pharmacy2] = $this->createPharmacyUserWithPharmacy();
        $pharmacy2->billingAddress()->associate($pharmacy->billingAddress);
        $pharmacy2->save();

        $this->createManySubscriptionOrders(
            [
                [
                    'started_at' => '2021-01-01 00:00:00',
                    'orderable_id' => $pharmacy->id,
                    'orderable_type' => 'App\Pharmacy',
                ],
                [
                    'started_at' => '2021-01-01 00:00:00',
                    'orderable_id' => $pharmacy2->id,
                    'orderable_type' => 'App\Pharmacy',
                ],
            ]
        );
        $this->artisan('payment:dispatch-invoices 2021-01-01 2021-01-31')
            ->expectsConfirmation(
                'Dispatching all invoices for orders between 2021-01-01 and 2021-01-31',
                'yes'
            )
            ->assertExitCode(0);
        Queue::assertPushed(CreateAndSendInvoice::class, 1);
    }
}
