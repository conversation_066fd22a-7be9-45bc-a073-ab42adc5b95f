<?php

namespace Tests\Unit\Helper;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\KimAddressStatus;
use App\Enums\KimVendorEnum;
use App\Helper\KimAddressHelper;
use App\KimAddress;
use App\Settings\KimSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class KimAddressHelperTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_correctly_gets_the_vendor(): void
    {
        $kimSettings = app(KimSettings::class);
        $kimSettings->kim_vendor = KimVendorEnum::AKQUINET->value;
        $kimSettings->save();

        $this->assertEquals(
            KimVendorEnum::AKQUINET->value,
            KimAddressHelper::getVendor()
        );

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->assertEquals(
            KimVendorEnum::AKQUINET->value,
            KimAddressHelper::getVendor($pharmacy->refresh())
        );

        $kimAddress = KimAddress::factory()->for($pharmacy)->create([
            'vendor' => KimVendorEnum::RISE->value,
            'status' => KimAddressStatus::RESERVED,
        ]);

        $this->assertEquals(
            KimVendorEnum::RISE->value,
            KimAddressHelper::getVendor($pharmacy->refresh())
        );

        $kimAddress->update([
            'vendor' => KimVendorEnum::AKQUINET->value,
        ]);

        $this->assertNotEquals(
            KimVendorEnum::RISE->value,
            KimAddressHelper::getVendor($pharmacy->refresh())
        );

        $this->assertEquals(
            KimAddressHelper::getVendor($pharmacy->refresh()),
            KimVendorEnum::tryFrom($kimAddress->vendor)->value
        );
    }

    public function test_it_correctly_gets_the_kim_address_domain(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->assertEquals(
            config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.domain'),
            KimAddressHelper::getKimAddressDomain($pharmacy)
        );

        $kimAddress = KimAddress::factory()->for($pharmacy)->create([
            'vendor' => KimVendorEnum::RISE->value,
            'status' => KimAddressStatus::RESERVED,
        ]);
        $this->assertEquals(
            config('kim.vendors.'.KimVendorEnum::RISE->value.'.domain'),
            KimAddressHelper::getKimAddressDomain($pharmacy->refresh())
        );

        $kimAddress->update([
            'vendor' => KimVendorEnum::AKQUINET->value,
        ]);
        $this->assertEquals(
            config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.domain'),
            KimAddressHelper::getKimAddressDomain($pharmacy->refresh())
        );
    }

    public function test_does_not_need_confirmation_when_no_kim_addresses_exists(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->subscribeToPlan('extended');

        $this->actingAs($user);

        $this->assertFalse(KimAddressHelper::needsModalConfirmation($pharmacy));
    }

    public function test_needs_confirmation_when_reserved_kim_addresses_exists(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);

        $pharmacy->kimAddresses()->create([
            'email' => fake()->email(),
            'status' => KimAddressStatus::RESERVED->value,
        ]);

        $this->assertTrue(KimAddressHelper::needsModalConfirmation($pharmacy));
    }

    public function test_needs_confirmation_when_ordered_kim_addresses_exists(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);

        $pharmacy->kimAddresses()->create([
            'email' => fake()->email(),
            'status' => KimAddressStatus::ORDERED->value,
        ]);

        $this->assertTrue(KimAddressHelper::needsModalConfirmation($pharmacy));
    }

    public function test_does_not_need_confirmation_when_activated_kim_addresses_exists(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);

        $pharmacy->kimAddresses()->create([
            'email' => fake()->email(),
            'status' => KimAddressStatus::ACTIVATED->value,
        ]);

        $this->assertFalse(KimAddressHelper::needsModalConfirmation($pharmacy));
    }

    public function test_does_not_need_confirmation_when_confirmation_exists(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $pharmacy->setGeneralSetting('confirmed_kim_booking_modal', true);

        $this->actingAs($user);

        $pharmacy->kimAddresses()->create([
            'email' => fake()->email(),
            'status' => KimAddressStatus::ORDERED->value,
        ]);

        $this->assertFalse(KimAddressHelper::needsModalConfirmation($pharmacy));
    }
}
