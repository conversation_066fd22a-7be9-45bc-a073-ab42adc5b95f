<?php

namespace Tests\Unit\Support;

use App\Support\RemoveAttributesFromHTML;
use Tests\TestCase;

class RemoveAttributesFromHTMLTest extends TestCase
{
    public function test_remove_attributes_from_html_tag_removes_attributes(): void
    {
        $html = RemoveAttributesFromHTML::make(
            '<p class="font-bold">Test</p>',
        )->run();

        $this->assertSame('<p>Test</p>', $html);
    }

    public function test_remove_attributes_from_html_tag_removes_attributes_with_whitelist(): void
    {
        $html = RemoveAttributesFromHTML::make(
            '<a href="https://google.com" class="font-bold">Test</a>',
            'href',
        )->run();

        $this->assertSame('<a href="https://google.com">Test</a>', $html);
    }

    public function test_remove_attributes_from_html_tag_removes_attributes_with_multiple_whitelists(): void
    {
        $html = RemoveAttributesFromHTML::make(
            '<a href="https://google.com" class="font-bold">Test</a>',
            ['href', 'class'],
        )->run();

        $this->assertSame('<a href="https://google.com" class="font-bold">Test</a>', $html);
    }

    public function test_remove_attributes_from_multiple_html_tags_removes_attributes(): void
    {
        $html = RemoveAttributesFromHTML::make(
            '<p class="font-bold"><a href="https://google.com" class="underline">Test</a></p>',
        )->run();

        $this->assertSame('<p><a>Test</a></p>', $html);
    }
}
