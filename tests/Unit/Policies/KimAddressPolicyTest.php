<?php

namespace Tests\Unit\Policies;

use App\Enums\AssociationEnum;
use App\Enums\PermissionEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\StaffRoleEnum;
use App\KimAddress;
use App\Policies\KimAddressPolicy;
use App\Staff;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\helper\SubscriptionHelper;
use Tests\TestCase;

class KimAddressPolicyTest extends TestCase
{
    use RefreshDatabase, SubscriptionHelper;

    public function test_view_any_only_allowed_for_owner_and_subowner()
    {
        $this->checkOnlyAllowedForOwnerAndSubsowner('viewAny');
    }

    public function test_create_is_only_allowed_for_owner_and_subowner()
    {
        $this->checkOnlyAllowedForOwnerAndSubsowner('create');
    }

    public function test_create_is_only_allowed_for_max_of_four_pharmacies()
    {
        [$owner, $pharmacy1] = $this->createPharmacyUserWithPharmacy();
        $pharmacy2 = $this->createPharmacyForUser($owner);
        $pharmacy3 = $this->createPharmacyForUser($owner);
        $pharmacy4 = $this->createPharmacyForUser($owner);
        $pharmacy5 = $this->createPharmacyForUser($owner);
        $pharmacies = [$pharmacy1, $pharmacy2, $pharmacy3, $pharmacy4, $pharmacy5];
        $owner->pharmacyProfile->update([
            'association_id' => AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
        ]);

        foreach ($pharmacies as $pharmacy) {
            $this->assertTrue($owner->can('create', [KimAddress::class, $pharmacy]));
        }
        KimAddress::factory()->for($pharmacy1)->create();
        KimAddress::factory()->for($pharmacy2)->create();
        KimAddress::factory()->for($pharmacy3)->create();

        foreach ($pharmacies as $pharmacy) {
            $this->assertTrue($owner->can('create', [KimAddress::class, $pharmacy]));
        }

        KimAddress::factory()->for($pharmacy4)->create();
        $this->assertTrue($owner->can('create', [KimAddress::class, $pharmacy4]));
        $this->assertFalse($owner->can('create', [KimAddress::class, $pharmacy5]));
    }

    public function test_update_is_not_allowed()
    {
        [$owner, $pharmacy1] = $this->createPharmacyUserWithPharmacy();
        $kim = KimAddress::factory()->for($pharmacy1)->create();
        $this->assertFalse($owner->can('update', [$kim, $pharmacy1]));
    }

    public function test_delete_is_not_allowed()
    {
        [$owner, $pharmacy1] = $this->createPharmacyUserWithPharmacy();
        $owner->pharmacyProfile->update([
            'association_id' => AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
        ]);
        $kim = KimAddress::factory()->for($pharmacy1)->create();
        $this->assertTrue($owner->can('delete', $kim));
    }

    private function checkOnlyAllowedForOwnerAndSubsowner($action): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyEmployee($pharmacy);
        $owner->pharmacyProfile->update([
            'association_id' => AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
        ]);

        $this->assertTrue($owner->can($action, [KimAddress::class, $pharmacy]));
        $this->assertFalse($employee->can($action, [KimAddress::class, $pharmacy]));

        $ohgOwner = $this->createPharmacyUser(null, true);
        $ohgPharmacy = $this->createPharmacy();
        $ohgPharmacy->users()->attach($ohgOwner, ['role_name' => PharmacyRoleEnum::OWNER]);
        $ohgOwner->pharmacyProfile->update([
            'association_id' => AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
        ]);
        $this->acceptTermsForPharmacy($ohgPharmacy);

        $subOwner = $this->createPharmacyEmployee($ohgPharmacy, PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($ohgPharmacy, PharmacyRoleEnum::EMPLOYEE);

        $this->assertTrue($ohgOwner->can($action, [KimAddress::class, $ohgPharmacy]));
        $this->assertFalse($subOwner->can($action, [KimAddress::class, $ohgPharmacy]));
        $this->assertFalse($employee->can($action, [KimAddress::class, $ohgPharmacy]));
    }

    public function test_support_has_no_general_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new KimAddressPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_support_has_read_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new KimAddressPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
    }

    public function test_support_has_no_write_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new KimAddressPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::CREATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::UPDATE));
    }

    public function test_support_has_delete_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new KimAddressPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::DELETE));
    }

    public function test_support_has_restore_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new KimAddressPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::RESTORE));
    }

    public function test_operations_has_no_general_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new KimAddressPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_operations_has_read_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new KimAddressPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
    }

    public function test_operations_has_no_write_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new KimAddressPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::CREATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::UPDATE));
    }

    public function test_operations_has_delete_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new KimAddressPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::DELETE));
    }

    public function test_operations_has_restore_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new KimAddressPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::RESTORE));
    }
}
