<?php

namespace Tests\Unit\Policies;

use App\Enums\StaffRoleEnum;
use App\Policies\SystemSettingPolicy;
use App\Staff;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SystemSettingPolicyTest extends TestCase
{
    use RefreshDatabase;

    public function test_support_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new SystemSettingPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_operations_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new SystemSettingPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_editor_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::EDITOR]);

        $policy = new SystemSettingPolicy;
        $this->assertFalse($policy->before($staff));
    }
}
