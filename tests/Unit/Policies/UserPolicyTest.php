<?php

namespace Tests\Unit\Policies;

use App\Enums\AssociationPermissionsEnum;
use App\Enums\AssociationRoleEnum;
use App\Enums\PermissionEnum;
use App\Enums\PharmacyPermissionsEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\StaffRoleEnum;
use App\Policies\UserPolicy;
use App\Staff;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserPolicyTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function a_user_can_update_himself()
    {
        /** @var User $user */
        $user = User::factory()->create();
        $this->assertTrue($user->can('update', $user));
    }

    /** @test */
    public function another_user_cant_update_an_other_user()
    {
        /** @var User $actingUser */
        $actingUser = User::factory()->create();
        /** @var User $userToUpdate */
        $userToUpdate = User::factory()->create();

        $this->assertFalse($actingUser->can('update', $userToUpdate));
    }

    /** @test */
    public function association_admins_can_update_their_employees()
    {
        [$associationAdminUser, $association] = $this->createAssociationUser();
        [$associationEmployeeWithoutPermission, $association] = $this->createAssociationUser($association, AssociationRoleEnum::EMPLOYEE);
        [$associationEmployeeWithPermission, $association] = $this->createAssociationUser($association, AssociationRoleEnum::EMPLOYEE, [AssociationPermissionsEnum::ADMINISTRATE_USERS]);
        [$anotherAssociationEmployeeWithPermission, $association] = $this->createAssociationUser($association, AssociationRoleEnum::EMPLOYEE, [AssociationPermissionsEnum::ADMINISTRATE_USERS]);
        [$associationAdminFromAnotherAssociation, $association] = $this->createAssociationUser(null, AssociationRoleEnum::ADMIN);
        [$associationEmployeeWithoutPermissionFromAnotherAssociation, $association] = $this->createAssociationUser(null, AssociationRoleEnum::EMPLOYEE);
        [$associationEmployeeWithPermissionFromAnotherAssociation, $association] = $this->createAssociationUser(null, AssociationRoleEnum::EMPLOYEE, [AssociationPermissionsEnum::ADMINISTRATE_USERS]);

        // An admin can update their employee
        $this->assertTrue($associationAdminUser->can('update', $associationEmployeeWithoutPermission));
        // An employee can not update their admin
        $this->assertFalse($associationEmployeeWithoutPermission->can('update', $associationAdminUser));
        // An employee with permission can not update their admin
        $this->assertFalse($associationEmployeeWithPermission->can('update', $associationAdminUser));
        // An employee without permission can not update other employees
        $this->assertFalse($associationEmployeeWithoutPermission->can('update', $associationEmployeeWithPermission));
        // An employee with permission can update other employees
        $this->assertTrue($associationEmployeeWithPermission->can('update', $associationEmployeeWithoutPermission));
        // An employee with permission can not update other employees with permission
        $this->assertTrue($associationEmployeeWithPermission->can('update', $anotherAssociationEmployeeWithPermission));
        // An employee with permission can not update employees from another association
        $this->assertFalse($associationEmployeeWithPermission->can('update', $associationEmployeeWithoutPermissionFromAnotherAssociation));
        // An admin can not update employees from another association
        $this->assertFalse($associationAdminUser->can('update', $associationAdminFromAnotherAssociation));
        $this->assertFalse($associationAdminUser->can('update', $associationEmployeeWithPermissionFromAnotherAssociation));

        $normalUser = User::factory()->create();
        $pharmacyUser = $this->createPharmacyUser();

        $this->assertFalse($associationAdminUser->can('update', $normalUser));
        $this->assertFalse($associationAdminUser->can('update', $pharmacyUser));
    }

    /** @test */
    public function pharmacy_admins_can_update_their_employees()
    {
        [$pharmacyAdmin, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacyAdmin2 = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::OWNER);
        $pharmacySubowner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $pharmacySubowner2 = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $pharmacyBranchManager = $this->createPharmacyEmployee($pharmacy);
        $pharmacyBranchManagerWithPermission = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::BRANCH_MANAGER, [PharmacyPermissionsEnum::ADMINISTRATE_USERS]);
        $pharmacyBranchManager2 = $this->createPharmacyEmployee($pharmacy);
        $pharmacyEmployee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $pharmacyEmployee2 = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$anotherPharmacyAdmin, $anotherPharmacy] = $this->createPharmacyUserWithPharmacy();
        $anotherPharmacyBranchManager = $this->createPharmacyEmployee($anotherPharmacy);
        $anotherPharmacyEmployee = $this->createPharmacyEmployee($anotherPharmacy, PharmacyRoleEnum::EMPLOYEE);

        // Subowner

        // A subowner can not update their Subowner
        $this->assertFalse($pharmacySubowner->can('update', $pharmacySubowner2));
        // A subowner can update their Branch Manager
        $this->assertTrue($pharmacySubowner->can('update', $pharmacyBranchManager));
        // A subowner can update their Employees
        $this->assertTrue($pharmacySubowner->can('update', $pharmacyEmployee));
        // A subowner can not update other Admins
        $this->assertFalse($pharmacySubowner->can('update', $pharmacyAdmin2));
        // A subowner can not update Admins other Pharmacies
        $this->assertFalse($pharmacySubowner->can('update', $anotherPharmacyAdmin));
        // A subowner can not update BranchManager other Pharmacies
        $this->assertFalse($pharmacySubowner->can('update', $anotherPharmacyBranchManager));
        // A subowner can not update Employees other Pharmacies
        $this->assertFalse($pharmacySubowner->can('update', $anotherPharmacyEmployee));

        // Admins

        // An admin can update their Subowner
        $this->assertTrue($pharmacyAdmin->can('update', $pharmacySubowner));
        // An admin can update their Branch Manager
        $this->assertTrue($pharmacyAdmin->can('update', $pharmacyBranchManager));
        // An admin can update their Employees
        $this->assertTrue($pharmacyAdmin->can('update', $pharmacyEmployee));
        // An admin can not update other Admins
        $this->assertFalse($pharmacyAdmin->can('update', $pharmacyAdmin2));
        // An admin can not update Admins other Pharmacies
        $this->assertFalse($pharmacyAdmin->can('update', $anotherPharmacyAdmin));
        // An admin can not update BranchManager other Pharmacies
        $this->assertFalse($pharmacyAdmin->can('update', $anotherPharmacyBranchManager));
        // An admin can not update Employees other Pharmacies
        $this->assertFalse($pharmacyAdmin->can('update', $anotherPharmacyEmployee));

        // Branch Manager

        // a branch manager can not update their Admins
        $this->assertFalse($pharmacyBranchManager->can('update', $pharmacyAdmin));
        //        // a branch manager can not update their Employees
        $this->assertFalse($pharmacyBranchManager->can('update', $pharmacyEmployee));
        // a branch manager can not update other Branch Manager
        $this->assertFalse($pharmacyBranchManager->can('update', $pharmacyBranchManager2));
        // a branch manager can not update Admins other Pharmacies
        $this->assertFalse($pharmacyBranchManager->can('update', $anotherPharmacyAdmin));
        // a branch manager can not update BranchManager other Pharmacies
        $this->assertFalse($pharmacyBranchManager->can('update', $anotherPharmacyBranchManager));
        // a branch manager can not update Employees other Pharmacies
        $this->assertFalse($pharmacyBranchManager->can('update', $anotherPharmacyEmployee));

        // Branch Manager with Permission

        // a branch manager with permission can not update their Admins
        $this->assertFalse($pharmacyBranchManagerWithPermission->can('update', $pharmacyAdmin));
        // a branch manager with permission can update their Employees
        $this->assertTrue($pharmacyBranchManagerWithPermission->can('update', $pharmacyEmployee));
        // a branch manager with permission can not update other Branch Manager
        $this->assertFalse($pharmacyBranchManagerWithPermission->can('update', $pharmacyBranchManager2));
        // a branch manager with permission can not update Admins other Pharmacies
        $this->assertFalse($pharmacyBranchManagerWithPermission->can('update', $anotherPharmacyAdmin));
        // a branch manager with permission can not update BranchManager other Pharmacies
        $this->assertFalse($pharmacyBranchManagerWithPermission->can('update', $anotherPharmacyBranchManager));
        // a branch manager with permission can not update Employees other Pharmacies
        $this->assertFalse($pharmacyBranchManagerWithPermission->can('update', $anotherPharmacyEmployee));

        // Employee

        // an employee can not update their Admins
        $this->assertFalse($pharmacyEmployee->can('update', $pharmacyAdmin));
        // an employee can update their Employees
        $this->assertFalse($pharmacyEmployee->can('update', $pharmacyEmployee2));
        // an employee can not update other Branch Manager
        $this->assertFalse($pharmacyEmployee->can('update', $pharmacyBranchManager));
        // an employee can not update Admins other Pharmacies
        $this->assertFalse($pharmacyEmployee->can('update', $anotherPharmacyAdmin));
        // an employee can not update BranchManager other Pharmacies
        $this->assertFalse($pharmacyEmployee->can('update', $anotherPharmacyBranchManager));
        // an employee can not update Employees other Pharmacies
        $this->assertFalse($pharmacyEmployee->can('update', $anotherPharmacyEmployee));
    }

    public function test_support_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new UserPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_support_has_read_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new UserPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
    }

    public function test_support_has_write_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new UserPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::CREATE));
        $this->assertTrue($policy->before($staff, PermissionEnum::UPDATE));
    }

    public function test_support_has_delete_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new UserPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::DELETE));
    }

    public function test_support_has_no_restore_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new UserPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
    }

    public function test_editor_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::EDITOR]);

        $policy = new UserPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_operations_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new UserPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_operations_has_read_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new UserPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
    }

    public function test_operations_has_write_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new UserPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::CREATE));
        $this->assertTrue($policy->before($staff, PermissionEnum::UPDATE));
    }

    public function test_operations_has_delete_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new UserPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::DELETE));
    }

    public function test_operations_has_no_restore_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new UserPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
    }
}
