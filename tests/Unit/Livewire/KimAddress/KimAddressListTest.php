<?php

namespace Tests\Unit\Livewire\KimAddress;

use App\Enums\KimAddressReportStatus;
use App\Enums\KimAddressStatus;
use App\Livewire\KimAddress\KimAddressesList;
use Livewire\Livewire;
use Tests\TestCase;

class KimAddressListTest extends TestCase
{
    public function test_does_not_show_report_status_when_not_reported(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->kimAddresses()->create([
            'email' => '<EMAIL>',
            'status' => KimAddressStatus::ORDERED->value,
            'report_status' => KimAddressReportStatus::NOT_REPORTED,
        ]);

        Livewire::actingAs($user)
            ->test(KimAddressesList::class, ['pharmacy' => $pharmacy])
            ->assertDontSee('an NNF übertragen');
    }

    public function test_does_not_show_report_status_when_pending(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->kimAddresses()->create([
            'email' => '<EMAIL>',
            'status' => KimAddressStatus::ORDERED->value,
            'report_status' => KimAddressReportStatus::PENDING,
        ]);

        Livewire::actingAs($user)
            ->test(KimAddressesList::class, ['pharmacy' => $pharmacy])
            ->assertDontSee('an NNF übertragen');
    }

    public function test_does_not_show_report_status_when_report_failed(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->kimAddresses()->create([
            'email' => '<EMAIL>',
            'status' => KimAddressStatus::ORDERED->value,
            'report_status' => KimAddressReportStatus::REPORT_FAILED,
        ]);

        Livewire::actingAs($user)
            ->test(KimAddressesList::class, ['pharmacy' => $pharmacy])
            ->assertDontSee('an NNF übertragen');
    }

    public function test_does_show_report_status_when_reported(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->kimAddresses()->create([
            'email' => '<EMAIL>',
            'status' => KimAddressStatus::ORDERED->value,
            'report_status' => KimAddressReportStatus::REPORTED,
        ]);

        Livewire::actingAs($user)
            ->test(KimAddressesList::class, ['pharmacy' => $pharmacy])
            ->assertSee('an NNF übertragen');
    }
}
