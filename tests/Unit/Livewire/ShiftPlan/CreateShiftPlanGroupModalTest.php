<?php

namespace Tests\Unit\Livewire\ShiftPlan;

use App\Livewire\ShiftPlan\CreateShiftPlanGroupModal;
use App\ShiftPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

/** @group shiftplan */
class CreateShiftPlanGroupModalTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_validates_shift_plan_group_name(): void
    {
        Livewire::test(CreateShiftPlanGroupModal::class)
            ->call('addGroup')
            ->assertHasErrors(['title' => 'required']);
    }

    public function test_it_creates_a_shift_plan_and_group(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();
        $shiftPlan = ShiftPlan::factory()->create([
            'owner_id' => $user->id,
        ]);
        $this->actingAs($user);
        $nameShiftPlanGroup = 'Test Shift Plan Group';

        $livewire = Livewire::test(CreateShiftPlanGroupModal::class)
            ->set('shiftPlan', $shiftPlan)
            ->set('title', $nameShiftPlanGroup)
            ->call('addGroup');

        $this->assertDatabaseHas('shift_plan_groups', [
            'name' => $nameShiftPlanGroup,
            'shift_plan_id' => $shiftPlan->id,
        ]);

        $livewire->assertSet('name', '');
        $livewire->assertDispatched('shiftplan-group-created');
        $livewire->assertRedirect(route('shiftplans.view', $shiftPlan->uuid));
    }
}
