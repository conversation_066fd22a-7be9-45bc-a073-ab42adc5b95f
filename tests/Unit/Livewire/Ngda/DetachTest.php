<?php

namespace Tests\Unit\Livewire\Ngda;

use App\Integrations\IaIntegration;
use App\Integrations\IntegrationTypeEnum;
use App\Integrations\NGDAIntegration;
use App\Livewire\Ngda\Detach;
use Livewire\Livewire;
use Tests\TestCase;

class DetachTest extends TestCase
{
    public function test_it_detaches_integration(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $pharmacy->setIntegration(
            new NGDAIntegration(
                id: 'APO123',
                accessToken: '123',
                refreshToken: 'ABC',
                refreshAt: now()->addWeek(),
                acceptedNnf: false,
            )
        );

        $this->assertNotNull($pharmacy->getIntegration(IntegrationTypeEnum::NGDA));

        Livewire::actingAs($owner)
            ->test(Detach::class)
            ->call('detach')
            ->assertOk();

        $this->assertNull($pharmacy->getIntegration(IntegrationTypeEnum::NGDA));
    }

    public function test_nothing_happens_to_ia_integration(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $pharmacy->setIntegration(new IaIntegration(iaPharmacyId: '123'));

        $this->assertNotNull($pharmacy->getIntegration(IntegrationTypeEnum::IhreApotheken));

        Livewire::actingAs($owner)
            ->test(Detach::class)
            ->call('detach')
            ->assertOk();

        $this->assertNotNull($pharmacy->getIntegration(IntegrationTypeEnum::IhreApotheken));
    }
}
