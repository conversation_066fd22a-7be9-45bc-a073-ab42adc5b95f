<?php

namespace Tests\helper;

use App\Actions\IhreApotheken\SetPermissionsForIaRoleAction;
use App\Domains\Subscription\Application\StripeProducts\AddOns\IAStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\Ia\IaRoleEnum;
use App\Enums\PharmacyRoleEnum;
use App\IaPreflightUser;
use App\Integration;
use App\Integrations\IaIntegration;
use App\Pharmacy;
use App\Settings\IaSettings;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Testing\Assert as PHPUnit;
use Illuminate\Testing\TestResponse;
use Laravel\Pennant\Feature;

trait IaTestCase
{
    protected Carbon $beforeFeature;

    protected Carbon $preflightPhase;

    protected Carbon $released;

    protected function setUp(): void
    {
        parent::setUp();

        TestResponse::macro('assertSeeWizardStep', function (string $step) {
            PHPUnit::assertStringContainsString(sprintf('dusk="wizard-step-%s"', $step::kebab()), $this->getContent());

            return $this;
        });

        TestResponse::macro('assertDontSeeWizardStep', function (string $step) {
            PHPUnit::assertStringNotContainsString(sprintf('dusk="wizard-step-%s"', $step::kebab()), $this->getContent());

            return $this;
        });

        Feature::flushCache();
        Feature::purge();
        Cache::clear();

        app(IaSettings::class)->enabledAt = now()->subWeek();
        app(IaSettings::class)->preflightEnabledUntil = now()->addWeek();

        $this->beforeFeature = now()->subWeeks(2);
        $this->preflightPhase = now();
        $this->released = now()->addWeeks(2);
    }

    protected function travelToAndClearCache(Carbon $date): void
    {
        Feature::flushCache();
        Feature::purge();
        Cache::clear();

        $this->travelTo($date);
    }

    protected function setupWorld(
        bool $isOwner,
        bool $hasAccessRights,
        bool $integrationEnabled,
        bool $isPreflightUser,
        ?string $role = null
    ): Pharmacy {
        /** @var Pharmacy $pharmacy */
        $pharmacy = Pharmacy::factory()
            ->when(
                $integrationEnabled,
                fn ($factory) => $factory->has(Integration::factory(1, [
                    'settings' => new IaIntegration('9029'),
                ]))
            )->create();

        $this->createLocalStripeSubscription($pharmacy, [BaseStripeProduct::make(), IAStripeProduct::make()]);

        if (! $isOwner) {
            $owner = $this->createPharmacyUser();
            $pharmacy->assignUser($owner, PharmacyRoleEnum::OWNER);
        }

        $user = $this->createPharmacyUser(isOwner: $isOwner);

        $pharmacy->assignUser(
            $user,
            $isOwner
                ? PharmacyRoleEnum::OWNER
                : ($role ?? PharmacyRoleEnum::EMPLOYEE)
        );

        if ($hasAccessRights) {
            (new SetPermissionsForIaRoleAction)->execute(
                $user,
                $pharmacy,
                IaRoleEnum::IaAdmin
            );
        }

        if ($isPreflightUser) {
            IaPreflightUser::create([
                'user_id' => $user->id,
                'enabled' => true,
            ]);
        }

        $this->actingAs($user);

        $this->acceptTermsForPharmacy($pharmacy);

        return $pharmacy;
    }
}
