<?php

return [
    'vendors' => [
        App\Enums\KimVendorEnum::RISE->value => [
            'domain' => env('KIM_RISE_DOMAIN'),
            'api' => [
                'access_token_url' => env('KIM_RISE_ACCESS_TOKEN_URL'),
                'client_id' => env('KIM_RISE_CLIENT_ID'),
                'client_secret' => env('KIM_RISE_CLIENT_SECRET'),
                'api_url' => env('KIM_RISE_URL'),
                'product_uuid' => env('KIM_RISE_PRODUCT_UUID'),
            ],
        ],
        App\Enums\KimVendorEnum::AKQUINET->value => [
            'domain' => env('KIM_AKQUINET_DOMAIN'),
            'api' => [
                'api_url' => env('KIM_AKQUINET_URL'),
                'client_id' => env('KIM_AKQUINET_CLIENT_ID'),
                'client_username' => env('KIM_AKQUINET_CLIENT_USERNAME'),
                'client_password' => env('<PERSON>IM_AKQUINET_CLIENT_PASSWORD'),
                'product_uuid' => env('KIM_AKQUINET_PRODUCT_UUID'),
            ],
            'appointment_booking_url' => env('KIM_AKQUINET_APPOINTMENT_BOOKING_URL'),
        ],
    ],
    'webhook' => [
        env('KIM_AKQUINET_WEBHOOK_TOKEN') => \App\Actions\Kim\HandleAkquinetWebhook::class,
    ],
];
