<template>
    <div>
        <div class="absolute" style="left: -99999px">
            <label>
                <input :name="inputName" v-model='states.formString' tabindex="-1" autocomplete="off" class="form-input">
            </label>
        </div>
        <div>
            <div
                v-if="states.formString"
            >
                <table>
                    <tbody>
                        <tr
                            v-for="day in 7"
                            v-if="states.submittedTimes[day - 1].active"
                            class="font-medium text-gray-700"
                        >
                            <td class="align-top pr-4 pb-3">
                                {{ weekday(day - 1) + ':' }}
                            </td>
                            <td class="align-top pr-4 pb-3 text-right">
                                <div
                                    v-for="(timeBlock) in states.submittedTimes[day -1].data"
                                >
                                    {{ stringifyTimeBlock(timeBlock) }}
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div>
                <button
                    @click="states.modalOpen = true"
                    type="button"
                    class="inline-flex justify-center rounded-md border border-transparent px-4 py-2 bg-red-600 text-base leading-6 font-medium text-white shadow-sm hover:bg-red-500 focus:outline-none focus:border-red-700 focus:shadow-outline-red transition ease-in-out duration-150 sm:text-sm sm:leading-5"
                >
                    {{ messages['edit'] }}
                </button>
            </div>
        </div>
        <div>
            <div v-show='states.modalOpen'  class="z-50 fixed bottom-0 inset-x-0 px-4 pb-6 sm:inset-0 sm:p-0 sm:flex sm:items-center sm:justify-center">
                <div class="fixed inset-0 transition-opacity">
                    <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
                </div>
                <div class="z-10 bg-white rounded-lg px-4 pt-5 pb-4 shadow-xl transform transition-all max-h-full sm:max-w-screen-sm sm:w-full sm:p-6">
                    <div class="overflow-x-auto h-96 sm:h-128 max-h-full">
                        <div
                            v-for="day in 7"
                            class="py-2 border-b border-gray-200"
                        >
                            <div
                                class="flex items-center"
                            >
                                <input
                                    v-model="states.times[day - 1].active"
                                    @change="dayToggled(states.times[day - 1])"
                                    :id="'day-' + (day-1).toString() + '-active'"
                                    type="checkbox"
                                    class="form-checkbox h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500 ml-1"
                                />
                                <label :for="'day-' + (day-1).toString() + '-active'" class="ml-2 block text-sm font-medium leading-5 text-gray-900">
                                    {{ weekday(day -1) }}
                                    <svg v-show="!states.times[day - 1].valid" class="w-5 h-5 text-red-600 inline-block" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" stroke="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>

                                </label>
                            </div>
                            <div v-show="states.times[day -1]['active']">
                                <div
                                    v-for="(timeBlock, index) in states.times[day -1]['data']"
                                    :key="`time-block-${index}`"
                                    class="pt-1 text-gray-700"
                                >
                                    <div class="sm:flex">
                                        <div class="sm:w-2/6 inline-block">
                                            <div class="relative rounded-md shadow-sm inline-block">
                                                <input
                                                    v-model="timeBlock['start']['hour']"
                                                    :class="{'border-red-600': !timeBlock.valid}"
                                                    type="text"
                                                    class="form-input w-12 sm:w-16 text-center block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm sm:leading-5 disabled:opacity-50"
                                                />
                                            </div>
                                            <span class="sm:px-3 font-bold py-2">:</span>
                                            <div class="relative rounded-md shadow-sm inline-block">
                                                <input
                                                    v-model="timeBlock['start']['minute']"
                                                    :class="{'border-red-600': !timeBlock.valid}"
                                                    type="text"
                                                    class="form-input w-12 sm:w-16 text-center block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm sm:leading-5 disabled:opacity-50"
                                                />
                                            </div>
                                        </div>
                                        <div class="sm:px-6 font-bold sm:w-1/6 inline-block py-2">
                                            <span class="">-</span>
                                        </div>
                                        <div class="sm:w-2/6 inline-block">
                                            <div class="relative rounded-md shadow-sm inline-block">
                                                <input
                                                    v-model="timeBlock['end']['hour']"
                                                    :class="{'border-red-600': !timeBlock.valid}"
                                                    type="text"
                                                    class="form-input w-12 sm:w-16 text-center block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm sm:leading-5 disabled:opacity-50"
                                                />
                                            </div>
                                            <span class="sm:px-3 font-bold py-2">:</span>
                                            <div class="relative rounded-md shadow-sm inline-block">
                                                <input
                                                    v-model="timeBlock['end']['minute']"
                                                    :class="{'border-red-600': !timeBlock.valid}"
                                                    type="text"
                                                    class="form-input w-12 sm:w-16 text-center block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm sm:leading-5 disabled:opacity-50"
                                                />
                                            </div>
                                        </div>
                                        <div class="sm:w-1/6 inline-block">
                                            <svg @click="deleteBlock(day - 1, index)" class="h-6 w-6 inline-block cursor-pointer sm:ml-4 mt-1" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor">
                                                <path d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    v-if="states.times[day -1]['data'].length < 2"
                                    class="pt-1"
                                >
                                    <button
                                        @click="addBlock(states.times[day -1])"
                                        type="button"
                                        class="justify-center w-full text-gray-700 hover:text-gray-500 rounded-md border border-transparent border-gray-300 px-4 py-2 text-base leading-6 font-medium focus:outline-none focus:border-gray-700 focus:shadow-outline-gray transition ease-in-out duration-150 sm:text-sm sm:leading-5"
                                    >
                                        {{ messages['add'] }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-5 sm:mt-6 text-right">
                        <span
                            v-if="this.states.dirty"
                            class="px-1 py-1 sm:py-0 inline-flex"
                        >
                            <button
                                @click="reset"
                                type="button"
                                class="justify-center w-full text-gray-700 hover:text-gray-500 rounded-md border border-transparent border-gray-300 px-4 py-2 text-base leading-6 font-medium focus:outline-none focus:border-gray-700 focus:shadow-outline-gray transition ease-in-out duration-150 sm:text-sm sm:leading-5"
                            >
                                {{ messages['reset'] }}
                            </button>
                        </span>
                        <span class="px-1 py-1 sm:py-0 inline-flex">
                            <button
                                @click="states.modalOpen = !states.modalOpen"
                                type="button"
                                class="justify-center w-full text-gray-700 hover:text-gray-500 rounded-md border border-transparent border-gray-300 px-4 py-2 text-base leading-6 font-medium focus:outline-none focus:border-gray-700 focus:shadow-outline-gray transition ease-in-out duration-150 sm:text-sm sm:leading-5"
                            >
                                {{ messages['cancel'] }}
                            </button>
                        </span>
                        <span class="px-1 py-1 sm:py-0 inline-flex">
                            <button
                                @click="apply"
                                :class="{
                                    'bg-red-600 hover:bg-red-500 text-white focus:border-red-700 focus:shadow-outline-red': this.states.valid,
                                    'bg-gray-300 text-gray-500 cursor-default': !this.states.valid,
                                }"
                                type="button"
                                class="justify-center w-full rounded-md border border-transparent px-4 py-2 text-base leading-6 font-medium shadow-sm focus:outline-none transition ease-in-out duration-150 sm:text-sm sm:leading-5"
                            >
                                {{ messages['apply'] }}
                            </button>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'BusinessHours',
        props: {
            inputName: {
                type: String,
                required: true,
            },
            inputId: {
                type: String,
                required: true,
            },
            initialState: {
                type: String,
                default: '',
            },
            messagesProp: {
                type: String,
                required: true,
            },
        },
        computed: {
            times() {
                return this.states.times;
            },
        },
        watch: {
            times: {
                handler() {
                    this.states.dirty = true;

                    this.states.valid = true;
                    for (const day in this.states.times) {
                        this.states.times[day].data.forEach((segment) => {
                            this.processInputs(segment);
                            this.validateTimeBlock(segment);

                            this.states.valid = this.states.valid && segment.valid;
                        });

                        this.validateDay(this.states.times[day]);

                        this.states.valid = this.states.valid && this.states.times[day].valid;
                    }
                },
                deep: true,
            },
        },
        data() {
            return {
                states: {
                    modalOpen: false,
                    formString: this.initialState,
                    times: this.stringToData(this.initialState),
                    submittedTimes: this.stringToData(this.initialState),
                    dirty: false,
                    valid: true,
                },
                messages: JSON.parse(this.messagesProp),
            }
        },
        methods: {

            apply() {
                if (!this.states.valid) {
                    return;
                }

                this.states.formString = this.dataToString(this.states.times);
                this.states.submittedTimes = this.states.times;
                this.states.dirty = true;
                this.states.modalOpen = false;
            },

            /**
             * @param {Object} day
             */
            addBlock(day) {
                day.data.push({
                    valid: true,
                    start: {
                        hour: '9',
                        minute: '00',
                    },
                    end: {
                        hour: '18',
                        minute: '00',
                    },
                })
            },

            /**
             * @param {Object} day
             * @param {Number} index
             */
            deleteBlock(day, index) {
                this.states.times[day].data.splice(index, 1);
                if (this.states.times[day].data.length === 0) {
                    this.states.times[day].active = false;
                }
            },

            /**
             * @param {Object} day
             */
            dayToggled(day) {
                if (day.active && day.data.length === 0) {
                    this.addBlock(day);
                }
                if (!day.active) {
                    day.data = [];
                }
            },

            /**
             * @param {String} value
             * @returns {Object}
             */
            stringToData(value) {
                const data = {};

                for (let i = 0; i < 7; i++) {
                    data[i] = {
                        active: false,
                        valid: true,
                        data: [],
                    };
                }
                if (value === '') {
                    return data;
                }
                value.split(';').forEach((segment) => {
                    if (segment === '') {
                        return;
                    }
                    const segmentParts = segment.split(',');
                    const day = parseInt(segmentParts[0]);

                    const startSegments = segmentParts[1].split(':');
                    const endSegments = segmentParts[2].split(':');

                    data[day].active = true;

                    data[day].data.push({
                        valid: true,
                        start: {
                            hour: startSegments[0],
                            minute: startSegments[1],
                        },
                        end: {
                            hour: endSegments[0],
                            minute: endSegments[1],
                        },
                    });
                });
                return data;
            },

            /**
             * @param {Object} data
             * @returns {String}
             */
            dataToString(data) {
                let result = '';

                for (let day in data) {
                    data[day].data.forEach((segment) => {
                        result += `${day},${segment.start.hour}:${segment.start.minute},${segment.end.hour}:${segment.end.minute};`
                    });
                }
                result = result.slice(0, -1);

                return result;
            },

            /**
             * @param {Object} timeBlock
             * @returns {string}
             */
            stringifyTimeBlock(timeBlock) {
                return `${timeBlock.start.hour}:${timeBlock.start.minute}-${timeBlock.end.hour}:${timeBlock.end.minute}`;
            },

            /**
             * @param {Object} timeBlock
             */
            processInputs(timeBlock) {
                let startHour = parseInt(timeBlock.start.hour) || 0;
                let startMinute = parseInt(timeBlock.start.minute) || 0;
                let endHour = parseInt(timeBlock.end.hour) || 0;
                let endMinute = parseInt(timeBlock.end.minute) || 0;

                startHour = Math.min(startHour, 23);
                startHour = Math.max(startHour, 0);

                startMinute = Math.min(startMinute, 59);
                startMinute = Math.max(startMinute, 0);

                endHour = Math.min(endHour, 23);
                endHour = Math.max(endHour, 0);

                endMinute = Math.min(endMinute, 59);
                endMinute = Math.max(endMinute, 0);

                timeBlock.start.hour = startHour.toString();
                timeBlock.start.minute = (startMinute < 10 ? '0' : '') + startMinute.toString();
                timeBlock.end.hour = endHour.toString();
                timeBlock.end.minute = (endMinute < 10 ? '0' : '') + endMinute.toString();
            },

            /**
             * @param {Object} timeBlock
             */
            validateTimeBlock(timeBlock) {
                const startHour = parseInt(timeBlock.start.hour);
                const startMinute = parseInt(timeBlock.start.minute);
                const endHour = parseInt(timeBlock.end.hour);
                const endMinute = parseInt(timeBlock.end.minute);

                timeBlock.valid = !(
                    startHour < 0 ||
                    startHour > 23 ||
                    endHour < 0 ||
                    endHour > 23 ||
                    startMinute < 0 ||
                    startMinute > 59 ||
                    endMinute < 0 ||
                    endMinute > 59 ||
                    startHour > endHour ||
                    (startHour === endHour && startMinute >= endMinute)
                );
            },

            /**
             * @param {Object} day
             */
            validateDay(day) {
                if (day.data.length <= 1) {
                    day.valid = true;
                    return;
                }

                for (let i = 0; i < day.data.length -1; i++) {
                    for (let j = i + 1; j < day.data.length; j++) {
                        const startFirst = parseInt(day.data[i].start.hour) * 60 +  parseInt(day.data[i].start.minute);
                        const endFirst = parseInt(day.data[i].end.hour) * 60 +  parseInt(day.data[i].end.minute);
                        const startSecond = parseInt(day.data[j].start.hour) * 60 +  parseInt(day.data[j].start.minute);
                        const endSecond = parseInt(day.data[j].end.hour) * 60 +  parseInt(day.data[j].end.minute);

                        const overallTimeSpan = Math.max(endFirst, endSecond) - Math.min(startFirst, startSecond);
                        const cumulatedTime = endFirst - startFirst + endSecond - startSecond;

                        if (cumulatedTime >= overallTimeSpan) {
                            day.valid = false;
                            return;
                        }
                    }
                }
                day.valid = true;
            },

            reset() {
                this.states.times = this.stringToData(this.initialState);
                this.states.submittedTimes = this.stringToData(this.initialState);
                this.states.dirty = false;
            },

            /**
             * @param {Number} index
             * @returns {String}
             */
            weekday(index) {
                let day = ''
                switch (index) {
                    case 0:
                        day = 'monday';
                        break;

                    case 1:
                        day = 'tuesday';
                        break;

                    case 2:
                        day = 'wednesday';
                        break;

                    case 3:
                        day = 'thursday';
                        break;

                    case 4:
                        day = 'friday';
                        break;

                    case 5:
                        day = 'saturday';
                        break;

                    case 6:
                        day = 'sunday';
                        break;

                }
                return this.messages['weekdays'][day]
            },
        },
    }
</script>
