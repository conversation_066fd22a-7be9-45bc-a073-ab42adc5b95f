<nav x-data="{ open: false }" class="fixed top-0 left-0 w-full bg-white shadow-sm z-40">
    @if(!auth()->check() && request()->routeIs(['home']) && \App\Banner::query()->active()->published()->top()->exists())
        <div class="bg-red-600">
            <div class="max-w-screen-xl mx-auto py-3 px-3 sm:px-6 lg:px-8">
                <div class="flex items-center justify-center flex-wrap">
                    <div class="flex items-center">
                        <div dusk="home-app-name" id="homepage-header-hint-text"
                             class="mx-3 font-medium text-white text-center text-xs">
                            {!! \App\Support\RemoveAttributesFromHTML::make(strip_tags(\App\Banner::query()->active()->published()->top()->latest('starts_at')->first()->content, '<a>'), ['href', 'class'])->run() !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <div class="max-w-7xl mx-auto px-4 md:px-6 lg:px-8" id="homepage-top-nav-bar-no-login">
        <div class="flex flex-col md:flex-row">
            {{--  Logo, Mobile menu button --}}
            <div class="flex-shrink-0 flex items-center justify-between h-16">
                {{--  Logo --}}
                <a href="{{ route('home') }}" id="homepage-logo-no-login">
                    <x:logo class="block h-10 w-auto"></x:logo>
                </a>

                {{-- Mobile menu button --}}
                <button x-on:click="open = !open"
                        class="inline-flex md:hidden items-center justify-center p-2 -mr-2 md:mr-0 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition duration-150 ease-in-out"
                        x-bind:aria-label="open ? 'Close main menu' : 'Main menu'" x-bind:aria-expanded="open"
                        aria-label="Close main menu" aria-expanded="true">
                    <svg x-state:on="Menu open" x-state:off="Menu closed"
                         x-bind:class="{ 'hidden': open, 'block': !open }" class="h-6 w-6 hidden">
                        <use href="/icons.svg#bars-3"/>
                    </svg>
                    <svg x-state:on="Menu open" x-state:off="Menu closed"
                         x-bind:class="{ 'hidden': !open, 'block': open }" class="h-6 w-6 block">
                        <use href="/icons.svg#x-mark"/>
                    </svg>

                </button>
            </div>

            {{--  Menu --}}
            <div x-bind:class="{ 'hidden': !open }"
                 class="hidden md:flex flex-col flex-grow justify-between -mx-4 md:mx-0 md:flex-row z-40">
                {{-- Menu left --}}
                <div
                        class="relative flex pb-3 md:pb-0 md:ml-8 flex-col md:flex-row border-b border-gray-200 md:border-transparent">
                    <a href="{{ route('news.index', ['type' => 'news']) }}"
                       class="mt-1 md:mt-0 md:ml-8 inline-flex items-center pl-3 pr-4 py-2 md:px-1 md:pt-1 md:pb-0 border-l-4 md:border-l-0 md:border-b-2 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out @if (request()->routeIs(['news*', 'authors*', 'categories*', 'tags*'])) border-red-500 text-gray-900 focus:border-red-700  @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:text-gray-700 focus:border-gray-30 @endif">
                        News
                    </a>
                    <a href="{{ route('support') }}"
                       class="mt-1 md:mt-0 md:ml-8 inline-flex items-center pl-3 pr-4 py-2 md:px-1 md:pt-1 md:pb-0 border-l-4 md:border-l-0 md:border-b-2 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out @if (request()->routeIs(['support'])) border-red-500 text-gray-900 focus:border-red-700  @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:text-gray-700 focus:border-gray-30 @endif">
                        Support
                    </a>
                </div>

                {{-- Menu right --}}
                <div
                        class="flex flex-row items-center justify-center py-2 space-x-8 md:py-0 md:justify-end md:flex-1 md:flex-row">
                    @if (Auth::guest())
                        <a href="{{ route('login') }}", id="homepage-login-link"
                           class="block px-4 py-2 whitespace-no-wrap text-sm leading-5 font-medium text-gray-500 hover:text-gray-700 transition ease-in-out duration-150">
                            @lang('auth.login')
                        </a>
                        <span class="inline-flex rounded-md shadow-sm">
                            <a href="{{ route('register-form') }}"
                               class="inline-flex items-center px-4 py-2 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-red-600 hover:bg-red-500 focus:outline-none focus:border-red-700 focus:shadow-outline-red active:bg-red-700 transition ease-in-out duration-150">
                                @lang('auth.register')
                            </a>
                        </span>
                    @else
                        <div class="md:hidden w-full">
                            <div class="flex items-center px-4 pt-4 pb-3">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full overflow-hidden bg-gray-200">
                                        <svg class="h-full w-full text-gray-400">
                                            <use href="/icons.svg#profile-dark"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm leading-5">
                                        @lang('auth.signed_in_as')
                                    </p>
                                    <p class="text-sm leading-5 font-medium text-gray-900 truncate">
                                        {{ user()->name }}
                                    </p>
                                </div>
                            </div>
                            <a href="{{ route('dashboard') }}"
                               class="block px-4 py-2 text-sm leading-5 text-gray-500 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                               role="menuitem">
                                @lang('navigation.dashboard')
                            </a>
                            <div class="border-t border-gray-100"></div>
                            <div class="py-1">
                                <a href="{{ route('users.edit') }}"
                                   class="block px-4 py-2 text-sm leading-5 text-gray-500 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                                   role="menuitem">
                                    @lang('navigation.account')
                                </a>
                                <a href="{{ route('support') }}"
                                   class="block px-4 py-2 text-sm leading-5 text-gray-500 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                                   role="menuitem">
                                    @lang('navigation.support')
                                </a>
                            </div>
                            <div class="border-t border-gray-100"></div>
                            <div class="pt-1">
                                <form action="{{ route('logout') }}" method="POST">
                                    @csrf

                                    <button type="submit"
                                            class="block w-full text-left px-4 py-2 text-sm leading-5 text-gray-500 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                                            role="menuitem">
                                        @lang('auth.logout')
                                    </button>
                                </form>
                            </div>
                        </div>

                        <div class="hidden md:block">
                            <x:profileDropdown/>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</nav>
