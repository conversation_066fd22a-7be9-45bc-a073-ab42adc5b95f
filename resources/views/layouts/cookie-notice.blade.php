<div id="cn-wrapper" class="hidden fixed bottom-0 inset-x-0 pb-2 sm:pb-5 transform translate-y-full z-50">
    <div class="max-w-screen-xl mx-auto px-2 sm:px-6 lg:px-8">
        <div class="rounded-lg bg-white shadow-lg">
            <div class="rounded-lg shadow-xs px-4 pt-5 pb-4 overflow-hidden sm:p-6">
                <p class="text-lg leading-6 font-medium text-gray-900">
                    @lang('cookie_notice.privacy_settings')
                </p>
                <div class="mt-2">
                    <p class="text-sm leading-5 text-gray-500">
                        Cookies helfen uns bei der Bereitstellung unserer Online-Dienste. Wir verwenden daher Cookies, um Ihre Nutzererfahrung auf unserer Webseite zu verbessern. Wählen Sie "Alle akzeptieren", damit die Cookies verwendet werden können. Weitere Informationen befinden sich in unserer <a href="{{ route('privacy') }}" target="_blank" class="underline text-gray-900">Datenschutzerklärung</a>.
                    </p>
                </div>
                <div class="xl:flex xl:justify-between xl:items-center">
                    <div class="mt-2 flex flex-col flex-wrap sm:flex-row">
                        <div class="relative flex items-start mt-2 mr-6">
                            <div class="flex items-center h-5">
                                <input id="cn-essential-cookies" type="checkbox" checked disabled class="form-checkbox h-4 w-4 text-gray-300 transition duration-150 ease-in-out" />
                            </div>
                            <div class="ml-3 text-sm leading-5">
                                <label for="cn-essential-cookies" class="font-medium text-gray-700">@lang('cookie_notice.essential_cookies')</label>
                            </div>
                            <div>
                                <svg class="cn-tooltip ml-1 h-5 w-5 text-gray-700 cursor-pointer" data-tippy-content="Diese Cookies sind für das Funktionieren der Website erforderlich und können in unseren Systemen nicht ausgeschaltet werden. Sie werden in der Regel nur als Reaktion auf von Ihnen durchgeführte Aktionen festgelegt, die einer Anforderung von Diensten gleichkommen, wie z. B. das Festlegen Ihrer Datenschutzeinstellungen, das Anmelden oder das Ausfüllen von Formularen. Dazu gehören auch Cookies, auf die wir möglicherweise zur Betrugsprävention angewiesen sind. Sie können Ihren Browser so einstellen, dass er diese Cookies blockiert oder Sie darüber benachrichtigt. Allerdings werden dann einige Teile der Website nicht funktionieren."><use href="/icons.svg#information-circle"/></svg>
                            </div>
                        </div>

                        {{--
                        <div class="relative flex items-start mt-2 mr-6">
                            <div class="flex items-center h-5">
                                <input id="cn-functional-cookies" name="" type="checkbox" class="form-checkbox h-4 w-4 text-red-600 transition duration-150 ease-in-out" />
                            </div>
                            <div class="ml-3 text-sm leading-5">
                                <label for="cn-functional-cookies" class="font-medium text-gray-700">@lang('cookie_notice.functional_cookies')</label>
                            </div>
                            <div>
                                <svg class="cn-tooltip ml-1 h-5 w-5 text-gray-700 cursor-pointer" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" data-tippy-content="Durch diese Cookies können wir uns Ihre Entscheidungen merken, die Sie in der Vergangenheit getroffen haben, um Ihnen erweiterte Funktionen und Personalisierung (wie z. B. Ihre Spracheinstellungen) zu bieten. Wenn Sie diese Cookies nicht zulassen, funktionieren einige oder alle dieser Dienste möglicherweise nicht ordnungsgemäß.">
                                    <path d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>

                        <div class="relative flex items-start mt-2 mr-6">
                            <div class="flex items-center h-5">
                                <input id="cn-performance-cookies" name="" type="checkbox" class="form-checkbox h-4 w-4 text-red-600 transition duration-150 ease-in-out" />
                            </div>
                            <div class="ml-3 text-sm leading-5">
                                <label for="cn-performance-cookies" class="font-medium text-gray-700">@lang('cookie_notice.performance_cookies')</label>
                            </div>
                            <div>
                                <svg class="cn-tooltip ml-1 h-5 w-5 text-gray-700 cursor-pointer" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" data-tippy-content="Diese Cookies dienen dazu, Besuche und Datenverkehrsquellen zu erfassen und so die Leistung unserer Website zu messen und verbessern. Sie helfen uns dabei, zu verstehen, welche Seiten am beliebtesten und unbeliebtesten sind. Ebenfalls werden Daten dazu erfasst, wie sich Besucher auf der Website bewegen. Alle Informationen, die diese Cookies sammeln, werden in aggregierter Form verarbeitet und sind daher anonym. Wenn Sie diese Cookies nicht zulassen, wissen wir nicht, wann Sie unsere Website besucht haben, und können deren Leistung nicht überwachen.">
                                    <path d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>

                        <div class="relative flex items-start mt-2 mr-6">
                            <div class="flex items-center h-5">
                                <input id="cn-targeting-cookies" name="" type="checkbox" class="form-checkbox h-4 w-4 text-red-600 transition duration-150 ease-in-out" />
                            </div>
                            <div class="ml-3 text-sm leading-5">
                                <label for="cn-targeting-cookies" class="font-medium text-gray-700">@lang('cookie_notice.targeting_cookies')</label>
                            </div>
                            <div>
                                <svg class="cn-tooltip ml-1 h-5 w-5 text-gray-700 cursor-pointer" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" data-tippy-content="Diese Cookies können von unseren Werbepartnern über unsere Website festgelegt werden. Sie können von diesen Unternehmen verwendet werden, um ein Profil Ihrer Interessen anzulegen und Ihnen relevante Anzeigen auf anderen Websites anzuzeigen. Es werden durch diese Cookies keine direkten persönlichen Informationen erfasst. Sie sind auf die eindeutige Identifizierung Ihres Browsers und Ihres Internetgeräts ausgelegt. Wenn Sie diese Cookies nicht zulassen, wird Ihnen weniger gezielte Werbung angezeigt.">
                                    <path d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>

                        --}}
                    </div>
                    <div class="mt-6">
                        <span class="inline-flex rounded-md shadow-sm">
                            <button id="cn-save" type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm leading-5 font-medium rounded-md text-gray-700 bg-white hover:text-gray-500 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150">
                                @lang('cookie_notice.save')
                            </button>
                        </span>
                        <span class="ml-2 inline-flex rounded-md shadow-sm hidden"> {{-- Hidden, because we only have essential cookies yet so save all ist not neccessary --}}
                            <button id="cn-save-all" type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-red-600 hover:bg-red-500 focus:outline-none focus:border-red-700 focus:shadow-outline-red active:bg-red-700 transition ease-in-out duration-150">
                                @lang('cookie_notice.save_all')
                            </button>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
