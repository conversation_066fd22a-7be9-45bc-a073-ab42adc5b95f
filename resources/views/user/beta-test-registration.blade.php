@extends('layouts.app')

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                Friendly User Apotheken gesucht
            </x-slot>
            <x-slot name="description">
                <div class="font-bold">
                    ENTWICKELN VON A BIS Z – UND DAS GANZ IN FAMILIE.
                </div>
            </x-slot>
        </x:header>

        <div class="flex flex-wrap -m-4">
            <div class="w-full p-4">
                <x-alert type="warning" class="mb-6" description="Registrieren können sich nur Apothekeninhaberinnen und Apothekeninhaber."/>

                <div class="bg-white shadow overflow-hidden sm:rounded-md">
                    <div class="px-4 py-5 sm:p-6">
                        @if (user()->is_beta_tester)
                            <div class="mt-3 text-sm leading-5 text-gray-500">
                                <p class="mb-4">
                                    <b>Vielen Dank!</b><br>Sie haben sich bereits als Friendly User registriert. Wir
                                    freuen uns auf die Zusammenarbeit.
                                </p>
                            </div>
                        @else
                            <div class="mt-3 text-sm leading-5 text-gray-500">
                                <p class="mb-4">
                                    Als Betreiber Ihres GEDISA ApothekenPortals möchten wir Ihnen immer wieder neue
                                    digitale Anwendungen zur Verfügung stellen, die den Alltag in den Apotheken
                                    erleichtern. Um den Entwicklungsprozess effizient voranzutreiben und nicht am
                                    Bedarf vorbei zu entwickeln, suchen wir digitalaffine Apothekerinnen und Apotheker,
                                    die sich mit ihren eigenen Ideen und Vorschlägen einbringen, damit am Ende des
                                    Prozesses userfreundliche und praxistaugliche Anwendungen stehen.
                                </p>
                                <p class="mb-4">
                                    Gemeinsam mit den Friendly User Apotheken erkennen wir, wo im Einzelnen
                                    Optimierungsbedarf besteht, um Anwendungen noch besser zu machen und auf den
                                    Bedarf auszurichten. Wir testen z. B. gemeinsam die Funktionalität unserer Software.
                                </p>
                                <p class="font-bold mb-4">
                                    Deshalb suchen wir Sie als interessierte Apothekerin bzw. Apotheker, um als
                                    Friendly User Apotheke bei der Entwicklung von A – Z dabei zu sein. Und wir freuen
                                    uns sehr, wenn Sie unser Team dauerhaft unterstützen würden.
                                </p>
                                @can('betaTestStoreRegistration', user())
                                    <p class="font-bold">
                                        Haben Sie Interesse? Nutzen Sie zur aktiven Beteiligung den Button "Registrieren"
                                        und werden Sie Friendly User.
                                    </p>
                                @endif
                            </div>
                            @can('betaTestStoreRegistration', user())
                                <div class="mt-5">
                                    <form action="{{ route('beta-test.store-registration') }}" method="post">
                                        @csrf
                                        <button type="submit"
                                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-red-600 hover:bg-red-500 focus:outline-none focus:shadow-outline-red focus:border-red-700 active:bg-red-700 transition duration-150 ease-in-out">
                                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke-linecap="round"
                                                 stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                                 stroke="currentColor">
                                                <path d="M12 4v16m8-8H4"></path>
                                            </svg>
                                            Registrieren
                                        </button>
                                    </form>
                                </div>
                            @endcan
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </x:content>
@endsection
