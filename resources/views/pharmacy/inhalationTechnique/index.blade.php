@extends('layouts.app')

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                Übersicht Inhalationstechnik
            </x-slot>
            <x-slot name="section">
                {{ $pharmacy->name }}
            </x-slot>
            <x-slot name="action">
                <a href="{{ route('pharmacies.pharmaceutical-services.inhalation-techniques.start', [$pharmacy]) }}">
                    <x:button>
                        <svg class="-ml-1 mr-2 h-5 w-5"><use href="/icons.svg#plus"/></svg>
                        Neue Inhalationstechnik starten
                    </x:button>
                </a>

<!--                <form method="POST" action="{{ route('pharmacies.pharmaceutical-services.inhalation-techniques.start', [$pharmacy]) }}">
                    @csrf
                    <x:button>
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor"><path d="M12 4v16m8-8H4"></path></svg>
                        Neue Messung starten
                    </x:button>
                </form>-->
            </x-slot>
        </x:header>
        <div class="mb-5">
            <x:alert type="error" class="mb-6" description="Aufgrund datenschutzrechtlicher Bestimmungen werden alle nicht vollständig durchgeführten Dokumentationen nach 24h gelöscht. Wir bitten um Ihr Verständnis.">
            </x:alert>
        </div>

        <div class="flex flex-wrap -m-2">
            <div class="w-full sm:w-1/2 p-2">
                <div class="bg-white overflow-hidden shadow rounded-lg h-full">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                                <svg class="w-6 h-6 text-white"><use href="/icons.svg#arrow-path"/></svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm leading-5 font-medium text-gray-500 truncate">
                                        Durchgeführte Inhalationstechniken
                                    </dt>
                                    <dd class="flex items-baseline">
                                        <div class="text-2xl leading-8 font-semibold text-gray-900 ">
                                            {{ number_format($pharmacy->pharmaceuticalServices()->successfullyFinishedByInhalationTechnique()->count(), 0, '', '.') }}
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <div class="flex flex-wrap -m-4">
                <div class="w-full p-4">
                    @if (count($inhalation_techniques) > 0)
                        <x:stackedList>
                            <x:stackedListHead caret>
                                <div class="w-4/12">Mitarbeiter/in</div>
                                <div class="w-4/12">Status</div>
                                <div class="w-4/12">Durchgeführt am</div>
                            </x:stackedListHead>

                            <x:stackedListBody>
                                @foreach ($inhalation_techniques as $inhalation_technique)
                                    <x:stackedListRow href="{{ route('pharmacies.pharmaceutical-services.inhalation-techniques.edit', [$pharmacy, $inhalation_technique]) }}" caret>

                                        {{-- Apotheker --}}
                                        <div class="w-full md:w-4/12">
                                            <div class="flex items-center">
                                                <div class="text-sm leading-5 text-gray-900">
                                                    {{ $inhalation_technique->user_name }}
                                                </div>
                                            </div>
                                        </div>

                                        {{-- Status --}}
                                        <div class="w-full md:w-4/12">
                                            @if ($inhalation_technique->reasons_to_abort != null)
                                                {{-- abgebrochen --}}
                                                @if(collect($inhalation_technique->reasons_to_abort)->contains(\App\Enums\InhalationTechnique\ReasonToAbortEnum::TEST))
                                                    <div class="text-sm leading-5 text-gray-800">
                                                        Testmessung
                                                    </div>
                                                @else
                                                    <div class="text-sm leading-5 text-red-400">
                                                        Abgebrochen
                                                    </div>
                                                @endif
                                            @elseif ($inhalation_technique->status == \App\Enums\Vaccinate\VaccinationStatus::DRAFT)
                                                {{-- in bearbeitung --}}
                                                <div class="text-sm leading-5 text-yellow-400">
                                                    In Bearbeitung
                                                </div>
                                            @elseif ($inhalation_technique->status == \App\Enums\Vaccinate\VaccinationStatus::FINISHED)
                                                {{-- abgeschlossen --}}
                                                <div class="text-sm leading-5 text-green-400">
                                                    Abgeschlossen
                                                </div>
                                            @endif
                                            <div class="text-sm leading-5 text-gray-500">
                                                Schritt {{ $inhalation_technique->step }}
                                            </div>
                                        </div>

                                        {{-- Date --}}
                                        <div class="w-full md:w-4/12">
                                            <div class="text-sm leading-5 text-gray-900">
                                                {{ $inhalation_technique->localized_date->format('d.m.Y') }}
                                            </div>
                                        </div>
                                    </x:stackedListRow>
                                @endforeach
                            </x:stackedListBody>
                        </x:stackedList>

                        <div class="mt-6">
                            {{ $inhalation_techniques->render() }}
                        </div>
                    @endif

                    @if ($inhalation_techniques->total() === 0)
                        <div class="bg-white shadow overflow-hidden sm:rounded-md">
                            <div class="px-4 py-5 sm:p-6">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    Bisher wurde noch keine Inhalationstechniken durchgeführt
                                </h3>
                                <div class="mt-2 text-sm leading-5 text-gray-500">
                                    <p>
                                        Klicken Sie auf "Neue Inhalationstechnik starten", um Ihre erste Inhalationstechnik zu starten.
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </x:content>
@endsection
