@extends('layouts.app')

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                Mi<PERSON><PERSON><PERSON>/in bearbeiten
            </x-slot>
            <x-slot name="action">
                <x:modal name="delete-user">
                    <x-slot name="activator">
                        <x:button>
                            <svg class="-ml-1 mr-2 h-5 w-5"><use href="/icons.svg#trash"/></svg>
                            Löschen
                        </x:button>
                    </x-slot>
                    <div>
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                            <svg class="h-6 w-6 text-red-600"><use href="/icons.svg#trash"/></svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-5">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-headline">
                                Mi<PERSON><PERSON><PERSON>/in löschen?
                            </h3>
                            <div class="mt-2 text-left">
                                <p class="text-sm leading-5 text-gray-500">
                                <div class="alert alert-warning border-l-4 border-yellow-500 bg-yellow-50 p-4 mb-4">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-yellow-800">
                                                Warnung: Diese Aktion kann nicht rückgängig gemacht werden.
                                            </h3>
                                            <div class="mt-2 text-sm text-yellow-700">
                                                <p class="mb-3">Beim Löschen des Mitarbeiters werden folgende Daten automatisch verarbeitet:</p>
                                                <ul class="list-disc list-inside space-y-1">
                                                    <li>Alle zugehörigen Daten des Dienstplans, inkl. Schichten, werden unwiderruflich gelöscht</li>
                                                    <li>Pharmazeutische Dienstleistungen werden anonymisiert (Patientendaten bleiben erhalten)</li>
                                                    <li>Impfungen werden anonymisiert (Patientendaten bleiben erhalten)</li>
                                                    <li>Persönliche Mitarbeiterdaten werden vollständig entfernt</li>
                                                </ul>
                                                <p class="mt-3 text-xs text-yellow-600">
                                                    <strong>Hinweis:</strong> Die medizinischen Aufzeichnungen bleiben für statistische und rechtliche Zwecke erhalten, können aber nicht mehr dem Mitarbeiter zugeordnet werden.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </p>
                            </div>

                            <div class="mt-2">
                                @if(\App\ShiftPlanGroupUser::where('user_id', $user->id)->exists())
                                    <p class="text-sm leading-5 text-gray-500">
                                        Bitte beachten Sie: Der Mitarbeiter ist in einem Dienstplan vermerkt. Wenn Sie den Mitarbeiter-Account löschen, werden auch alle zugehörigen Daten im Dienstplan, inkl. Schichten, entfernt.
                                    </p>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="mt-5 sm:mt-6 flex flex-wrap">
                        <div class="w-1/2 px-1">
                            <x:button x-on:click="open = false" appearance="secondary" wrapperClass="w-full" class="w-full">
                                Abbrechen
                            </x:button>
                        </div>
                        <div class="w-1/2 px-1">
                            <form method="POST" action="{{ route('pharmacies.users.destroy', [$pharmacy, $user]) }}" class="w-full">
                                @csrf
                                @method('DELETE')
                                <x:button type="submit" appearance="primary" wrapperClass="w-full" class="w-full">
                                    Löschen
                                </x:button>
                            </form>
                        </div>
                    </div>
                </x:modal>
            </x-slot>
        </x:header>

        @livewire('pharmacy.components.edit-user', ['pharmacy' => $pharmacy, 'user' => $user])
    </x:content>
@endsection
