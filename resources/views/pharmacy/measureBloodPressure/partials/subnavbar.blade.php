@section('subnavbar')
    <div class="bg-gray-900 z-40 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="relative flex justify-center items-center">
                @isset($backLink)
                    <div class="absolute left-0 top-3">
                        <x:button href="{{ $backLink }}" size="xs">
                            <svg class="-ml-0.5 mr-2 h-4 w-4">
                                <use href="/icons.svg#arrow-long-left"/>
                            </svg>
                            Zurück
                        </x:button>
                    </div>
                @endisset

                <div class="py-4">
                    <nav class="flex items-center justify-center space-x-8">
                        <p class="text-sm leading-5 font-medium">Schritt {{ $currentStep }} von 5</p>
                        <ul class="items-center space-x-5 hidden md:flex">
                            @for($x = 1; $x <= 5; $x++)

                                <li>
                                    <div class="group">
                                        <div class="flex-shrink-0 relative h-5 w-5 flex items-center justify-center">
                                            <!-- Heroicon name: check-circle -->
                                            @if($x <= $savedStep)
                                                <!-- Complete Step -->
                                                <a href="{{ route(\App\Enums\MeasureBloodPressure\MeasureBloodPressureStepEnum::getRedirectRouteArray()[$x - 1], [request()->pharmacy, request()->pharmaceuticalService]) }}"
                                                   class="tooltip"
                                                   data-tippy-content="@lang('measureBloodPressure.steps.' . $x)">
                                                    @if($x != $currentStep && $x != $savedStep)
                                                        <svg class="h-full w-full text-green-400 group-hover:text-green-600 group-focus:text-green-600 transition ease-in-out duration-150"><use href="/icons.svg#check-circle-dark"/></svg>
                                                    @elseif($x == $currentStep && $x != $savedStep)
                                                        <div class="flex-shrink-0 h-5 w-5 relative flex items-center justify-center">
                                                            <span class="absolute h-4 w-4 rounded-full bg-green-200"></span>
                                                            <span class="relative block w-2 h-2 bg-green-600 rounded-full"></span>
                                                        </div>
                                                    @elseif($x != $currentStep && $x == $savedStep)
                                                        <div class="flex-shrink-0 h-5 w-5 relative flex items-center justify-center">
                                                            <span class="absolute h-4 w-4 rounded-full bg-gray-200"></span>
                                                            <span class="relative block w-2 h-2 bg-gray-600 rounded-full"></span>
                                                        </div>
                                                    @elseif($x == $currentStep && $x == $savedStep)
                                                        <div class="flex-shrink-0 h-5 w-5 relative flex items-center justify-center">
                                                            <span class="absolute h-4 w-4 rounded-full bg-green-200"></span>
                                                            <span class="relative block w-2 h-2 bg-green-600 rounded-full"></span>
                                                        </div>
                                                    @endif
                                                </a>
                                            @else
                                                <div class="flex-shrink-0 h-5 w-5 relative flex items-center justify-center">
                                                    <span class="block h-2 w-2 bg-gray-300 rounded-full group-hover:bg-gray-400 group-focus:bg-gray-400 transition ease-in-out duration-150"></span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </li>
                            @endfor
                        </ul>
                    </nav>

                    {{--                    <x:stepper steps="3" :current="$currentStep" :saved="$savedStep"/>--}}
                </div>

                @isset($forwardLink)
                    <div class="absolute right-0 top-3">
                        <x:button href="{{ $forwardLink }}" size="xs">
                            Weiter
                            <svg class="-mr-0.5 ml-2 h-4 w-4"><use href="/icons.svg#arrow-long-right"/></svg>
                        </x:button>
                    </div>
                @endisset
            </div>
        </div>
    </div>
@endsection
