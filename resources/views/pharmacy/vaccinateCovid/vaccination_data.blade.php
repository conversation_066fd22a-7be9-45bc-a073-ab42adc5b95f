@extends('layouts.app')

@section('content')
    <x:content>
        @if ($vaccination['step'] > 3)
            @include('pharmacy.vaccinateCovid.partials.subnavbar', [
                'currentStep' => 3,
                'savedStep' => $vaccination['step'],
                'backLink' => route('pharmacies.vaccinate-covid.personal-data', [$vaccination->pharmacy, $vaccination]),
                'forwardLink' => route('pharmacies.vaccinate-covid.finished', ['pharmacy' => $vaccination->pharmacy, 'vaccination' => $vaccination])
            ])
        @else
            @include('pharmacy.vaccinateCovid.partials.subnavbar', [
                'currentStep' => 3,
                'savedStep' => $vaccination['step'],
                'backLink' => route('pharmacies.vaccinate-covid.personal-data', [$vaccination->pharmacy, $vaccination]),
            ])
        @endif

        <x:vaccinate.headline>
            Informationen zur Impfung
        </x:vaccinate.headline>

        <p class="text-base text-center pb-5 leading-6 text-gray-500">
            Es müssen alle Felder ausgefüllt werden
        </p>

        <x:layout.container size="max-w-2xl">
            <form method="POST">
                @csrf
                <div x-data="{
                    date: '{{ old('date', $vaccination->date ? \Carbon\Carbon::parse($vaccination->date)->format('Y-m-d') : '') }}',
                    vaccination_type: '{{ old('vaccination_type', $vaccination->covidVaccination->vaccination_type) }}',
                    pharmaceutical_id: '{{ addslashes(old('pharmaceutical_id', $vaccination->pharmaceutical_id)) }}',
                    batch_number: '{{ addslashes(old('batch_number', $vaccination->batch_number)) }}',
                    home_visit: {{ (old('home_visit') ? old('home_visit') == 'on' : (bool) $vaccination->covidVaccination->home_visit) ? 'true' : 'false' }},
                    home_visit_type: '{{ (old('home_visit_type', $vaccination->covidVaccination->home_visit_type)) }}',
                    user_id: '{{ (old('user_id', $vaccination->user_id ?? user()->id)) }}',
                }"
                     x-init="$watch('home_visit', value => home_visit_type = (value ? home_visit_type : ''))"
                >
                    <x:card>
                        <form>
                            <x:row>
                                <x:col>
                                    <x:input.checkbox
                                        id="home_visit"
                                        name="home_visit"
                                        x-model="home_visit"
                                        :error="$errors->first('home_visit')"
                                    >
                                        Diese Impfung ist ein Hausbesuch
                                    </x:input.checkbox>
                                    <div x-show="home_visit" x-cloak class="mt-4">
                                        <x:input.radio-group
                                            name="home_visit_type"
                                            :error="$errors->first('home_visit_type')"
                                            x-model="home_visit_type"
                                            :labels="\App\Enums\Vaccinate\HomeVisitTypeEnum::getForDropdownWithTranslation()"
                                        />
                                    </div>
                                </x:col>
                                <x:col>
                                    <x:input.text
                                        label="Impfdatum"
                                        name="date"
                                        x-model="date"
                                        placeholder="DD / MM / YYYY"
                                        type="date"
                                        :value="old('date', $vaccination->date ? \Carbon\Carbon::parse($vaccination->date)->format('Y-m-d') : '')"
                                        :error="$errors->first('date')"
                                    />
                                </x:col>
                                <x:col>
                                    <x:input.select
                                            label="Impfende/r Mitarbeiter/in"
                                            :options="$users"
                                            name="user_id"
                                            x-model="user_id"
                                            :value="old('user_id', $vaccination->user_id)"
                                            :error="$errors->first('user_id')"
                                    >
                                    </x:input.select>
                                </x:col>
                                <x:col>
                                    <x:input.select
                                        label="Impfserie"
                                        name="vaccination_type"
                                        x-model="vaccination_type"
                                        :value="old('vaccination_type', $vaccination->covidVaccination->vaccination_type)"
                                        :error="$errors->first('vaccination_type')"
                                    >
                                        <option selected value="none"></option>
                                        @foreach(\App\Enums\Vaccinate\CovidVaccinationType::getForDropdown() as $value => $name)
                                            <option value="{{ $value }}">{{ $name }}</option>
                                        @endforeach
                                    </x:input.select>

                                    <div class="mt-8">
                                        <x:alert title="Nummer der Impfserie" type="info">
                                            <x:slot name="description">
                                                <p>
                                                    Für die Eingabe der Impfungen zur Meldung an das DIM ist die folgende Systematik zu berücksichtigen. Bitte beachten Sie, dass diese Einteilung nicht der Zuordnung bei der Ausstellung eines digitalen COVID-19-Zertifikates entspricht.
                                                </p>
                                                <div class="mt-4">
                                                    <div class="w-full flex flex-wrap border border-blue-200 rounded overflow-hidden">
                                                        <div class="w-full bg-blue-200 p-2 overflow-hidden">
                                                            "Erstimpfung"
                                                        </div>
                                                        <div class="w-full flex flex-wrap">
                                                            <div class="w-1/2">

                                                            </div>
                                                            <div class="w-1/2 p-2">
                                                                <ul class="list-disc">
                                                                    <li>Erste Impfdosis ohne Vorerkrankung</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="w-full bg-blue-200 p-2 overflow-hidden">
                                                            "Zweitimpfung"
                                                        </div>
                                                        <div class="w-full flex flex-wrap">
                                                            <div class="w-1/2">

                                                            </div>
                                                            <div class="w-1/2 p-2">
                                                                <ul class="list-disc">
                                                                    <li>Zweite Impfdosis ohne Vorerkrankung</li>
                                                                    <li>Erste Impfdosis nach Genesung</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="w-full bg-blue-200 p-2 overflow-hidden">
                                                            "Auffrischungsimpfung"
                                                        </div>
                                                        <div class="w-full flex flex-wrap">
                                                            <div class="w-1/2 p-2">
                                                                "1. Auffrischungsimpfung"
                                                            </div>
                                                            <div class="w-1/2 p-2">
                                                                <ul class="list-disc">
                                                                    <li>dritte Impfdosis</li>
                                                                    <li>zweite Impfdosis nach Genesung</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="w-full flex flex-wrap">
                                                            <div class="w-1/2 p-2">
                                                                "2. Auffrischungsimpfung"
                                                            </div>
                                                            <div class="w-1/2 p-2">
                                                                <ul class="list-disc">
                                                                    <li>vierte Impfdosis</li>
                                                                    <li>dritte Impfdosis nach Genesung</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="w-full flex flex-wrap">
                                                            <div class="w-1/2 p-2">
                                                                "3. Auffrischungsimpfung"
                                                            </div>
                                                            <div class="w-1/2 p-2">
                                                                <ul class="list-disc">
                                                                    <li>fünfte Impfdosis</li>
                                                                    <li>vierte Impfdosis nach Genesung</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="w-full flex flex-wrap">
                                                            <div class="w-1/2 p-2">
                                                                "4. oder höhere Auffrischungsimpfung"
                                                            </div>
                                                            <div class="w-1/2 p-2">
                                                                <ul class="list-disc">
                                                                    <li>sechste Impfdosis</li>
                                                                    <li>fünfte Impfdosis nach Genesung</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </x:slot>
                                        </x:alert>
                                    </div>
                                </x:col>
                            </x:row>
                            <x:row class="mt-4">
                                <x:col>
                                    <div>
                                    <x:input.select
                                        label="Impfstoff"
                                        name="pharmaceutical_id"
                                        x-model="pharmaceutical_id"
                                        :value="old('pharmaceutical_id', $vaccination->pharmaceutical_id)"
                                        :error="$errors->first('pharmaceutical_id')"
                                    >
                                        <option selected value="none"></option>
                                        @foreach(\App\Pharmaceutical::where('type', \App\Enums\PharmaceuticalsTypeEnum::VACCINE_COVID)->where('active', true)->orderBy('display_name')->get() as $pharmaceutical)
                                            <option value="{{ $pharmaceutical->id }}">{{ $pharmaceutical->display_name }}</option>
                                        @endforeach
                                    </x:input.select>

                                        @if(\Carbon\Carbon::parse($vaccination->vaccinationPatient->birthdate)->age < 30)
                                            <div x-show="pharmaceutical_id != '' && pharmaceutical_id != 'none' && (pharmaceutical_id == '{{ optional(\App\Pharmaceutical::where('name', 'Moderna')->first())->id ?? 'abc' }}' ||  pharmaceutical_id == '{{ optional(\App\Pharmaceutical::where('name', 'Spikevax')->first())->id ?? 'abc' }}')" class="mt-8">
                                                <x:alert type="warning" title="ACHTUNG! COVID-19-Impfempfehlung der STIKO">
                                                    <x:slot name="description">
                                                        <p>Personen unter 30 Jahren sollten nicht mit Spikevax® (Moderna) geimpft werden. Dies gilt für die Grundimmunisierung als auch für mögliche  Auffrischimpfungen und auch, wenn zuvor ein anderer Impfstoff verwendet wurde.</p>
                                                    </x:slot>
                                                </x:alert>
                                            </div>
                                        @endif

                                        @if(\Carbon\Carbon::parse($vaccination->vaccinationPatient->birthdate)->age < 60)
                                            <div x-show="pharmaceutical_id != '' && pharmaceutical_id != 'none' && pharmaceutical_id == '{{ optional(\App\Pharmaceutical::where('name', 'Janssen')->first())->id ?? 'abc' }}'" class="mt-8">
                                                <x:alert type="warning" title="ACHTUNG! COVID-19-Impfempfehlung der STIKO">
                                                    <x:slot name="description">
                                                        <p>Personen unter 60 Jahren sollten nicht mit COVID-19-Vaccine Janssen geimpft werden.</p>
                                                    </x:slot>
                                                </x:alert>
                                            </div>
                                        @endif

                                        @if(\Carbon\Carbon::parse($vaccination->vaccinationPatient->birthdate)->age < 18)
                                            <div x-show="pharmaceutical_id != '' && pharmaceutical_id != 'none' && pharmaceutical_id == '{{ optional(\App\Pharmaceutical::where('name', 'Nuvaxovid')->first())->id ?? 'abc' }}'" class="mt-8">
                                                <x:alert type="warning" title="ACHTUNG! COVID-19-Impfempfehlung der STIKO">
                                                    <x:slot name="description">
                                                        <p>Nuvaxovid® ist für Personen ab 12 Jahren zur Grundimmunisierung zugelassen und von der STIKO für Personen ab 18 Jahren empfohlen.</p>
                                                    </x:slot>
                                                </x:alert>
                                            </div>
                                        @endif

                                        @if(\Carbon\Carbon::parse($vaccination->vaccinationPatient->birthdate)->age > 18)
                                            <div x-show="pharmaceutical_id != '' && pharmaceutical_id != 'none' && pharmaceutical_id == '{{ optional(\App\Pharmaceutical::where('name', 'VidPrevtyn Beta')->first())->id ?? 'abc' }}'" class="mt-8">
                                                <x:alert type="warning" title="ACHTUNG! COVID-19-Impfempfehlung der STIKO">
                                                    <x:slot name="description">
                                                        <p>VidPrevtyn Beta® ist für Personen ab 18 Jahren zur Auffrischimpfung zugelassen, jedoch von der STIKO nicht empfohlen.</p>
                                                    </x:slot>
                                                </x:alert>
                                            </div>
                                        @endif
                                    </div>
                                </x:col>
                            </x:row>

                            <x:row class="mt-4">
                                <x:col>
                                    <x:input.text
                                        label="Chargennummer"
                                        type="text"
                                        name="batch_number"
                                        x-model="batch_number"
                                        :value="old('batch_number', $vaccination->batch_number)"
                                        :error="$errors->first('batch_number')"
                                    />
                                </x:col>
                            </x:row>
                        </form>
                    </x:card>

                    <div class="mt-12 w-full flex justify-center">
                        <x-button
                            x-bind:disabled="
                            !date ||
                            !batch_number ||
                            !user_id ||
                            (!vaccination_type || vaccination_type == 'none') ||
                            (!pharmaceutical_id || pharmaceutical_id == 'none') ||
                            (home_visit && !home_visit_type)
                            "
                            size="lg" class="w-72">
                            Speichern
                        </x-button>
                    </div>
                </div>
            </form>
        </x:layout.container>
    </x:content>
@endsection
