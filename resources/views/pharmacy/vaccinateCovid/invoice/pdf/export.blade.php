<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        @include('pharmacy.vaccinateCovid.invoice.pdf.style.style')
    </head>

    <body class="text-gray-600">
        {{-- Cover --}}
        <div class="cover page-break">
            <div class="cover-logo clearfix">
                <img src="{{ public_path('assets/img/gedisa-logo.png') }}"/>
            </div>

            <div class="headline">
                <h1 class="title">
                    Abrechnung COVID-19-Impfung
                </h1>
                <span class="subtitle">{{ $start_date->format('d.m.Y') }} - {{ $end_date->format('d.m.Y') }}</span>
            </div>

            <div class="cover-summary">
                <div class="cover-pharmacy">
                    <span class="font-bold">{{ $pharmacy->name }}</span><br>
                    @if ($pharmacy->optional_address_line )
                        {{ $pharmacy->optional_address_line }}<br>
                    @endif
                    {{ $pharmacy->street }} {{ $pharmacy->house_number }}<br>
                    {{ $pharmacy->postcode }} {{ $pharmacy->city }}
                </div>

                <div class="cover-table">
                    <table>
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="text-left uppercase">
                                    Kennzeichen
                                </th>
                                <th class="text-left uppercase">
                                    Tätigkeit
                                </th>
                                <th class="text-right uppercase">
                                    Anzahl
                                </th>
                                <th class="text-right uppercase">
                                    Vergütung
                                </th>
                                <th class="text-right uppercase">
                                    Gesamt
                                </th>
                            </tr>
                        </thead>

                        <tbody>
                            @foreach($priceSummary['actions'] as $action => $prices)
                                @foreach ($prices as $price => $details)
                                    <tr>
                                        <td>{{ $action }}</td>
                                        <td>{{ trans('vaccination.covid.invoice.action_names.' . $action) }}</td>
                                        <td class="text-right">{{ $priceSummary['actions'][$action][$price]['total']['count'] }}</td>
                                        <td class="text-right">{{ money($price) }}</td>
                                        <td class="text-right">{{ money($priceSummary['actions'][$action][$price]['total']['price']) }}</td>
                                    </tr>
                                @endforeach
                            @endforeach
                        </tbody>

                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-left">
                                    {{ trans('vaccination.covid.invoice.footer-hint') }}
                                </td>
                                <td class="text-right">
                                    Summe
                                </td>
                                <td class="text-right">{{ money($priceSummary['total']['price']) }}</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        {{-- Footer --}}
        <footer class="clearfix">
            <div class="footer-left">
                Abrechnung COVID-19-Zertifikat
            </div>
            <div class="footer-right">
                <span class="page-number"></span>
            </div>
        </footer>

        {{-- Main Content --}}
        <table>
            <thead>
                <tr class="bg-gray-100">
                    <th class="text-left uppercase">
                        Datum
                    </th>
                    <th class="text-left uppercase">
                        Pseudo_ID
                    </th>
                    <th class="text-left uppercase">
                        Kennzeichen
                    </th>
                    <th class="text-left uppercase">
                        Tätigkeit
                    </th>
                    <th class="text-right uppercase">
                        Betrag
                    </th>
                </tr>
            </thead>

            <tbody>
                @foreach($query->get() as $cursor)
                    @foreach($cursor->covidVaccination->invoiceableActions as $action)
                        <tr>
                            <td class="text-left">
                                {{ $cursor->date->format('d.m.Y') }}
                            </td>
                            <td class="text-left">
                                {{ $cursor->covidVaccination->psn['psn'] ?? '' }}
                            </td>
                            <td class="text-left">
                                {{ $action }}
                            </td>
                            <td class="text-left">
                                {{ trans('vaccination.covid.invoice.action_names_short.' . $action) }}
                            </td>
                            <td class="text-right">
                                {{ money($actionPricing->getPricesByDate($cursor->date)[$action]) }}
                            </td>
                        </tr>
                    @endforeach
                @endforeach
            </tbody>

            <tfoot>
                <tr>
                    <td colspan="3" class="text-left text-gray-900">
                        {{ trans('vaccination.covid.invoice.footer-hint') }}
                    </td>
                    <td class="text-right text-gray-900">
                        Summe
                    </td>
                    <td class="text-right text-gray-900">
                        {{ money($priceSummary['total']['price']) }}
                    </td>
                </tr>
            </tfoot>
        </table>
    </body>
</html>


