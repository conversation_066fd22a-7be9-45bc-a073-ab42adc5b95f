<button x-data
    class="text-left ml-1 pl-5 pr-5 pt-4 pb-4 rounded-md bg-green-100 hover:bg-green-400"
    x-on:click="Livewire.dispatch('{{ \App\Livewire\FeatureRecommendation::EVENT_FORCE_OPEN }}', {featureId: '{{ \App\Support\Feature\ChatEnd2EndAndApoPortalRelease::getFeatureId()->value }}'})"
>
    <p class="font-medium mb-3 text-sm text-green-800">
        ApoConnect heißt jetzt ApoPortal.
    </p>
    <p class="text-sm text-green-700">Der Name ändert sich, Ihre Möglichkeiten bleiben. Schalten Sie jetzt den sicheren Chat für Ihre Apotheken frei.</p>
</button>
