@props([
    'close' => true,
])

<div x-data="{ open: true }" x-show="open" x-transition:leave="transition ease-in-out duration-300" x-transition:leave-end="opacity-0 transform translate-y-3" {{ $attributes->merge(['class' => 'fixed inset-x-0 bottom-0 hidden lg:block']) }}>
    <div class="bg-red-600">
        <div class="max-w-4xl mx-auto py-3 px-3 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between flex-wrap">
                {{ $slot }}
                @if ($close)
                    <div class="order-2 flex-shrink-0 sm:order-3 sm:ml-3">
                        <button x-on:click="open = false" type="button" class="-mr-1 flex p-2 rounded-md hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-white sm:-mr-2">
                            <span class="sr-only">Dismiss</span>
                            <svg class="h-6 w-6 text-white"><use href="/icons.svg#x-mark-bold"/></svg>
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
