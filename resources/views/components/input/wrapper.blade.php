@props([
    'options' => [],
    'label' => false,
    'name' => false,
    'error' => false,
    'helpText' => false,
    'cornerHint' => false,
    'type' => 'text',
    'required' => false,
    'width' => 'w-full',
])

<div>
    <div class="flex justify-between">
        @if ($label)
            <label
                    class="block text-sm font-medium leading-5 text-gray-700"
                    for="{{ $name }}"
            >{{ $label }} {{ $required ? '*' : '' }}</label>
        @endif
        @if ($cornerHint)
            <span class="text-sm leading-5 text-gray-500">{{ $cornerHint }}</span>
        @endif
    </div>

    <div class="relative mt-1 rounded-md shadow-sm">
        {{ $slot }}
    </div>

    @if ($helpText)
        <p class="mt-2 text-sm text-gray-500">{{ $helpText }}</p>
    @endif

    @if ($error)
        <p class="{{ $width }} mt-2 text-sm text-red-600">{{ $error }}</p>
    @endif
</div>
