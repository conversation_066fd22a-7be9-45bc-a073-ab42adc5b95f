@props([
    'id' => null
])
<div {{ $attributes->merge(['class' => 'bg-white shadow rounded-md']) }}>
    @isset($title)
        <div class="py-6 px-4 sm:px-10 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                {{ $title }}
            </h3>
            @isset($subTitle)
                <p class="mt-1 text-sm leading-5 text-gray-500">
                    {{ $subTitle }}
                </p>
            @endisset
        </div>
    @endisset
    @isset($header)
            <div class="py-6 px-4 sm:px-10 border-b border-gray-200">
                {{ $header }}
            </div>
    @endisset

    <div wire:key="{{ $id }}-slot" class="{{ !isset($noPadding) ? 'py-8 pb-10 px-4 sm:px-10' : '' }}">
        {{ $slot }}
    </div>

    @isset($footer)
        {{ $footer }}
    @endisset
</div>
