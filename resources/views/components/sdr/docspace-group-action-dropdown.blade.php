@props([
    'docSpaceGroup' => null,
])
<div class="hidden sm:block">
    <div>
        <x-input.more-options-dropdown :options="array_filter([
                                                user()->can('administrate', $docSpaceGroup) ? [
                                                    'name' => 'Bearbeiten',
                                                    'event' => 'open-doc-space-create-group-modal',
                                                    'event_data' => '{ id: '.$docSpaceGroup->id.'}',
                                                    'icon' => 'pencil'
                                                ] : null,
                                                user()->can('administrate', $docSpaceGroup) ? [
                                                    'name' => 'Löschen',
                                                    'event' => 'open-doc-space-delete-group-modal',
                                                    'event_data' => '{ id: '.$docSpaceGroup->id.'}',
                                                    'icon' => 'trash'
                                                ] : null,
                                            ])">
        </x-input.more-options-dropdown>
    </div>
</div>
