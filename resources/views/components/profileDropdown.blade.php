<div x-on:click.outside="profileOpen = false" class="ml-3 relative" x-data="{ profileOpen: false }">
    <div>
        <button x-on:click="profileOpen = !profileOpen"
                class="flex text-sm border-2 border-transparent rounded-full focus:outline-none focus:border-gray-300 transition duration-150 ease-in-out"
                id="user-menu" aria-label="User menu" aria-haspopup="true" x-bind:aria-expanded="profileOpen">
            <span class="inline-block h-8 w-8 rounded-full overflow-hidden bg-gray-200">
                <svg class="h-full w-full text-gray-400"><use href="/icons.svg#profile-dark"/></svg>
            </span>
        </button>
    </div>
    <div x-show="profileOpen" x-cloak x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg z-10">
        <div class="py-1 rounded-md bg-white shadow-xs">
            <div class="px-4 py-3">
                <p class="text-sm leading-5">
                    @lang('auth.signed_in_as')
                </p>
                <p class="text-sm leading-5 font-medium text-gray-900 truncate">
                    {{ user()->name }}
                </p>
            </div>
            <div class="border-t border-gray-100"></div>
            <div class="py-1">
                <a href="{{ route('dashboard') }}"
                   class="block px-4 py-2 text-sm leading-5 text-gray-500 md:text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                   role="menuitem">
                    @lang('navigation.dashboard')
                </a>
            </div>
            <div class="border-t border-gray-100"></div>
            <div class="py-1">
                <a href="{{ route('users.edit') }}"
                   class="block px-4 py-2 text-sm leading-5 text-gray-500 md:text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                   role="menuitem">
                    @lang('navigation.account')
                </a>
                @can('viewAny', \App\BillingAddress::class)
                    <a href="{{ route('users.billing-addresses') }}"
                       class="block px-4 py-2 text-sm leading-5 text-gray-500 md:text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                       role="menuitem">
                        Rechnungsadressen
                    </a>
                @endcan
                @if (user()?->isOwner() || user()?->isSubOwner())
                    <a href="{{ route('users.contracts') }}"
                       class="block px-4 py-2 text-sm leading-5 text-gray-500 md:text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                       role="menuitem">
                        Vertragsunterlagen
                    </a>
                @endif
                <a href="{{ route('support') }}"
                   class="block px-4 py-2 text-sm leading-5 text-gray-500 md:text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                   role="menuitem">
                    @lang('navigation.support')
                </a>
                @can('betaTestShow', user())
                    <a href="{{ route('beta-test.show') }}" class="block px-4 py-2 text-sm leading-5 text-gray-500 md:text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out" role="menuitem">
                        Friendly User
                    </a>
                @endif
            </div>
            <div class="border-t border-gray-100"></div>
            <div class="py-1">
                <a href="{{ route('imprint') }}"
                   class="block px-4 py-2 text-sm leading-5 text-gray-500 md:text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                   role="menuitem">
                    Impressum
                </a>
                <a href="{{ route('privacy') }}"
                   class="block px-4 py-2 text-sm leading-5 text-gray-500 md:text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                   role="menuitem">
                    Datenschutz
                </a>
            </div>
            <div class="border-t border-gray-100"></div>
            <div class="py-1">
                <form action="{{ route('logout') }}" method="POST" class="mb-0 pb-0">
                    @csrf

                    <button type="submit"
                            class="block w-full text-left px-4 py-2 text-sm leading-5 text-gray-500 md:text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 transition duration-150 ease-in-out"
                            role="menuitem">
                        @lang('auth.logout')
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
