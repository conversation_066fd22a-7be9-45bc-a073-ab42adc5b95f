@props(['subtitlePlaceholder'])

@aware(['multiple', 'valueKey', 'placeholder'])

<button
	x-listbox:button
	class="relative w-full cursor-default rounded-md bg-white pl-4 pr-10 py-4 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-600 sm:text-sm sm:leading-6"
>

		<div>
				<span x-text="selectedOption ? selectedOption.{{ $valueKey }} : @js($placeholder)" class="block text-[16px] font-medium
				 truncate"
		            :class="{'text-gray-900': selectedOption !== null, 'text-gray-500': selectedOption === null}"></span>
				<span x-text="selectedOption ? selectedOption.address : @js($subtitlePlaceholder)" class="block truncate"
				      :class="{'text-gray-500': selectedOption !== null, 'text-gray-400': selectedOption === null}"></span>
		</div>

		<span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
        <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
        <path fill-rule="evenodd"
            d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z"
            clip-rule="evenodd"/>
        </svg>
    </span>
</button>