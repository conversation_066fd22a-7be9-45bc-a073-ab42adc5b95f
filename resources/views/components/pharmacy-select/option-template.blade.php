@aware(['valueKey'])

<span
		x-text="option.{{ $valueKey }}"
		class="block truncate"
		x-bind:class="{
				'font-semibold': $listboxOption.isSelected,
				'font-normal': ! $listboxOption.isSelected
		}"
></span>

<span x-show="$listboxOption.isSelected"
		class="absolute inset-y-0 left-0 flex items-center pl-1.5"
		x-bind:class="{
			'text-white': $listboxOption.isActive,
			'text-primary-600': ! $listboxOption.isActive
		}"
>
		<svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
			<path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
		</svg>
</span>