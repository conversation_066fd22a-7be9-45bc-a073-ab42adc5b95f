@props([
    'title',
    'subtitle' => null,

    // props
    'icon' => null,
])

<div class="absolute right-0 top-0 pr-4 pt-4">
    <button
        class="rounded-lg bg-gray-25 p-2 text-gray-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2"
        type="button"
        x-on:click="$dialog.close()"
    >
        <span class="sr-only">Schließen</span>
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M18 6L6 18M6 6L18 18"
                stroke="#667085"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
        </svg>
    </button>
</div>

<div class="flex space-x-6 border-b border-gray-200 bg-gray-25 p-6">
    <div class="flex h-12 w-12 items-center justify-center rounded-md border border-gray-200 bg-white shadow-sm">
        {{ $slot }}
    </div>
    <div class="space-y-2">
        <p class="text-lg font-medium text-gray-900">{{ $title }}</p>
        <p class="text-sm text-gray-600">{{ $subtitle }}</p>
    </div>
</div>
