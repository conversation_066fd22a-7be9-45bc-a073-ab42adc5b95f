<div
    class="sticky inset-x-0 bottom-0 bg-gray-50 tabular-nums shadow-[0_2px_5px_rgba(0,0,0,0.3)] ring-2 ring-black ring-opacity-5">
    <div x-data="{ expanded: true }">
        <div class="flex items-center justify-between border-b border-gray-200 bg-gray-50 px-6 py-2">
            <button
                class="inline-flex items-center gap-x-1.5"
                type="button"
                @click="expanded = ! expanded"
            >
                <svg
                    class="-ml-1 h-5 w-5 text-gray-900"
                    x-show="!expanded"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                >
                    <path
                        fill-rule="evenodd"
                        d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
                        clip-rule="evenodd"
                    />
                </svg>

                <svg
                    class="-ml-1 h-5 w-5 text-gray-900"
                    x-show="expanded"
                    x-cloak
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                >
                    <path
                        fill-rule="evenodd"
                        d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z"
                        clip-rule="evenodd"
                    />
                </svg>

                <span class="text-sm font-semibold leading-6 text-gray-900">Insights</span>
            </button>
        </div>

        <div
            x-show="expanded"
            x-collapse
        >
            <div class="grid grid-cols-9 divide-x divide-gray-100 border-b border-gray-200 bg-white text-xs">
                <div class="col-span-2 flex items-center px-3 px-6">
                    <div class="min-w-0 flex-1">
                        <p class="uppercase text-gray-600">Zugewiesene Stunden</p>
                    </div>

                    <div class="flex-shrink-0">
                        <p
                            class="block text-xs font-medium tabular-nums text-gray-900"
                            href="#"
                        >{{ isset($insights['total']) ? formatMinutes($insights['total']) : '--:--' }}</p>
                    </div>
                </div>
                @foreach ($insights as $key => $insight)
                    @if ($key !== 'total')
                        <div class="p-3">
                            <p class="text-right font-medium text-gray-500">{{ formatMinutes($insight) }}</p>
                        </div>
                    @endif
                @endforeach
            </div>

            {{--            <dl class="px-6 divide-y divide-gray-200 text-xs"> --}}
            {{--                <div class="flex items-center justify-between py-2"> --}}
            {{--                    <dt class="text-gray-600 uppercase">Assigned Hours</dt> --}}
            {{--                    <dd class="font-medium text-gray-900">$99.00</dd> --}}
            {{--                </div> --}}
            {{--                <div class="flex items-center justify-between py-2"> --}}
            {{--                    <dt class="text-gray-600 uppercase">Worked Hours</dt> --}}
            {{--                    <dd class="font-medium text-gray-900">$5.00</dd> --}}
            {{--                </div> --}}
            {{--            </dl> --}}
        </div>
    </div>
</div>
