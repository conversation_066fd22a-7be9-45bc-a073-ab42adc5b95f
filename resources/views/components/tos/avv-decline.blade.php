<div>
    <x:subscriptions.section-headline
        headline="Auftragsverarbeitungsvertrag ablehnen"
    />

    <form wire:submit="declineAvv">

        <div class="mt-4">
            <x:input.checkbox id="avvDeclineCheckboxAccepted" wire:model.live="avvDeclineCheckboxAccepted" :error="$errors->first('avvDeclineCheckboxAccepted')">
                Hiermit bestätige ich, dass ich keinen Zugriff auf das Portal mehr habe, wenn ich den Auftragsverarbeitungsvertrag ablehne.
            </x:input.checkbox>
        </div>

        <div class="mt-4 flex justify-end">
            <x:button type="submit">Auftragsverarbeitungsvertrag ablehnen</x:button>
        </div>
    </form>
</div>
