@php
    $hasHref = isset($attributes['href']) && $attributes['href'] !== '';
@endphp

<li class="border-t border-gray-200">
    <{{ $hasHref ? 'a' : 'div' }} {{ $attributes->merge(['class' => $hasHref ? 'block hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition duration-150 ease-in-out' : 'block']) }}>
        <div class="flex items-center px-4 py-4 sm:px-6">
            <div class="min-w-0 flex-1 flex flex-col md:flex-row justify-between items-center">
                {{ $slot }}
            </div>

            @if(isset($attributes['caret']) && $attributes['caret'] !== false)
                <div class="hidden sm:block">
                    <svg class="h-5 w-5 text-gray-400"><use href="/icons.svg#chevron-right"/></svg>
                </div>
            @endif
        </div>
    </{{ $hasHref ? 'a' : 'div'  }}>
</li>
