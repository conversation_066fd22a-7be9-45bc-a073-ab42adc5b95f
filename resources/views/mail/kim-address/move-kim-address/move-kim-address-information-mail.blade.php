<x-mail::message>
@if($case->value === \App\Enums\Kim\MoveKimAddressCaseEnum::S1C1->value)
Anpassen von **customerNumber: {{ $targetSubCustomer->customerNumber }}**
- Neue ```email```: **{{ $targetSubCustomer->email }}**
- Neue ```referenceId```: **{{ $targetSubCustomer->referenceId }}**
@endif

@if($case->value === \App\Enums\Kim\MoveKimAddressCaseEnum::S1C2->value)
Die KIM-Adresse **{{ $kimAddress->email }}** wird umgezogen. Folgende Informationen müssen an Akquinet
weitergeleitet und entsprechend angepasst werden:

Anpassen von **customerNumber: {{ $sourceSubCustomer->customerNumber }}**
- Neue ```note```: **{{ \App\Support\AkquinetSubCustomerNote::build($sourceSubCustomer->customerNumber) }}**

Anpassen des **Registrierungscodes: {{ $kimAddress->additional['voucherCode'] }}**
- Neue ```referenceId```: **{{ $targetSubCustomer->customerNumber }}**
@endif

@if($case->value === \App\Enums\Kim\MoveKimAddressCaseEnum::S2C1->value)
Anpassen von **customerNumber: {{ $targetSubCustomer->customerNumber }}**
- Neue ```note```: **{{ \App\Support\AkquinetSubCustomerNote::build($targetSubCustomer->customerNumber) }}**

Anpassen des **Registrierungscodes: {{ $kimAddress->additional['voucherCode'] }}**
- Neue ```referenceId```: **{{ $targetSubCustomer->customerNumber }}**

**Der subCustomer {{ $sourceSubCustomer->customerNumber }} kann gelöscht werden (sofern es keine weiteren KIM
Adressen dazu gibt).**
@endif

@if($case->value === \App\Enums\Kim\MoveKimAddressCaseEnum::S2C2->value)
Anpassen von **customerNumber: {{ $targetSubCustomer->customerNumber }}**
- Neue ```note```: **{{ \App\Support\AkquinetSubCustomerNote::build($targetSubCustomer->customerNumber) }}**

Anpassen von **customerNumber: {{ $sourceSubCustomer->customerNumber }}**
- Neue ```note```: **{{ \App\Support\AkquinetSubCustomerNote::build($sourceSubCustomer->customerNumber) }}**

Anpassen des **Registrierungscodes: {{ $kimAddress->additional['voucherCode'] }}**
- Neue ```referenceId```: **{{ $targetSubCustomer->customerNumber }}**
@endif

@if($case->value === \App\Enums\Kim\MoveKimAddressCaseEnum::SPECIAL_CASE_1->value)
Anpassen von **customerNumber: {{ $sourceSubCustomer->customerNumber }}**
- Neue ```email```: **{{ $targetPharmacy->uuid . '@gedisa.de' }}**
- Neue ```referenceId```: **{{ $targetPharmacy->uuid }}**
- Neue ```Organisation```: **{{ $targetPharmacy->name }}**
- Neue ```note```: **{{ \App\Support\AkquinetSubCustomerNote::build($targetSubCustomer->customerNumber) }}**
@endif

@if($case->value === \App\Enums\Kim\MoveKimAddressCaseEnum::SPECIAL_CASE_2->value)
Anpassen von **customerNumber: {{ $sourceSubCustomer->customerNumber }}**
- Neue ```note```: **{{ \App\Support\AkquinetSubCustomerNote::build($sourceSubCustomer->customerNumber) }}**

Anpassen des **Registrierungscodes: {{ $kimAddress->additional['voucherCode'] }}**
- Neue ```referenceId```: **{{ $targetSubCustomer->customerNumber }}**
@endif
</x-mail::message>
