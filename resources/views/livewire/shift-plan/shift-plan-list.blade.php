<div class="flex flex-wrap -m-4 justify-center">
    <div class="w-full p-4">
        @if ($this->shiftPlans->count() > 0)
            <x-stackedList>
                <x-stackedListHead>
                    <div class="w-full">@lang('validation.attributes.name')</div>
                </x-stackedListHead>

                <x-stackedListBody>
                    @foreach ($this->shiftPlans as $shiftPlan)
                        <x-stackedListRow href="{{ route('shiftplans.view', $shiftPlan->uuid) }}">
                            <div class="w-full">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img src="{{ asset('assets/img/deutsche-apotheke-logo.svg') }}" alt=""
                                                 class="h-10 w-10 rounded-full">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm leading-5 font-medium text-gray-900">
                                                {{ $shiftPlan->name }}
                                            </div>
                                            <div class="text-sm leading-5 text-gray-500">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ml-4 flex-shrink-0">
                                        {{-- <svg class="w-4 h-4 text-gray-400">
                                            <use xlink:href="#icon-carret-mini" />
                                        </svg> --}}
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                             class="size-5">
                                            <path fill-rule="evenodd"
                                                  d="M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </x-stackedListRow>
                    @endforeach
                </x-stackedListBody>
            </x-stackedList>

            <div class="mt-4">
                {{ $this->shiftPlans->links() }}
            </div>
        @else
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:p-6 text-center">
                    <div class="flex justify-center mb-8">
                        <svg class="h-10 w-10 border border-gray-200 rounded p-2">
                            <use href="/icons.svg#clock"/>
                        </svg>
                    </div>

                    <h3 class="text-xl leading-6 font-medium text-gray-900 mb-8">
                        Willkommen beim Management-Tool Dienstplan
                    </h3>
                    <div class="mt-2 text-md leading-5 text-gray-500 mb-8">
                        <p>
                            Lösen Sie Ihre Dienstpläne in Excel und auf Papiervordrucken durch dieses Tool ab. Mit dem
                            Dienstplan verwalten Sie die Schichten für Ihr Team. Legen Sie individuell Ihre Dienstpläne
                            an, tragen Schichten ein und weisen diese den Mitarbeitenden zu. Schnell können Sie
                            entsprechende Anpassungen an den Schichten vornehmen und die Dienstpläne als Ausdruck oder
                            PDF exportieren.
                        </p>
                    </div>
                    <div class="mt-5 flex justify-center space-x-4">
                        <x-button appearance="light" :href="route('uploads.show', 'dienstplan-handout')" target="_blank">
                            Mehr erfahren
                        </x-button>
                        <x-button appearance="primary" x-data="{}"
                                  x-on:click="$dispatch('change-modal-state', { name: 'manageShiftPlan', state: 'open' })">
                            <svg class="-ml-1 mr-2 h-5 w-5">
                                <use href="/icons.svg#plus"/>
                            </svg>
                            Neuen Dienstplan erstellen
                        </x-button>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
