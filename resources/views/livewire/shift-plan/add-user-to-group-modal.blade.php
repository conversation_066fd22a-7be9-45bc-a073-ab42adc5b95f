<x-modal
    name="add-user-to-shift-plan-group-modal"
    x-init="$watch('open', value => !value ? $dispatch('shiftplan-add-employee-to-group.modal.on-close') : '')"
>
    <x-slot name="heading"><PERSON><PERSON><PERSON><PERSON> zu einer Gruppe hinzufügen</x-slot>
    <x-slot name="body">
        <div class="mb-4 text-base">
            <x-input.select
                label="Nutzer"
                wire:model="userId"
                :error="$errors->first('user')"
            >
                <option value="">Bitte wählen</option>
                @if (!empty($users))
                    @foreach ($users as $user)
                        <option value="{{ $user->id }}">{{ $user->full_name }}</option>
                    @endforeach
                @endif
            </x-input.select>
        </div>
    </x-slot>
    <x-slot name="footer">
        <div class="flex justify-end space-x-2">
            <x-button
                @click="open = false"
                appearance="secondary"
            >
                Abbrechen
            </x-button>
            <x-button
                wire:click="save"
                appearance="primary"
            >
                Speichern
            </x-button>
        </div>
    </x-slot>
</x-modal>
