<x-modal
    name="editShiftPlanGroup"
    :closeOnBackgroundClick="false"
    x-data="{ open: @entangle('modalOpen') }"
    x-show="open"
    @shiftplan-group-edited.window="open = false"
>

    <x-slot name="heading">Gruppe bearbeiten</x-slot>
    <x-slot name="body">
        <div class="flex min-h-72 justify-between gap-4">
            <div class="mb-4 w-full text-base">
                <x-input.text
                    id="title"
                    required
                    label="Titel"
                    wire:model="title"
                />
                @error('title')
                    <div class="mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
            <div class="mb-4 text-base">
                <x-input.color-picker
                    wire:model="color"
                    :color="!empty($color) ? $color : null"
                />

                @error('color')
                    <div class="mt-1 text-sm text-red-500">{{ $message }}</div>
                @enderror
            </div>
        </div>
    </x-slot>
    <x-slot name="footer">
        <div class="flex justify-end space-x-2">
            <x-button
                wire:click="onCloseModal"
                appearance="secondary"
            >
                Abbrechen
            </x-button>
            <x-button
                wire:click="updateGroup"
                appearance="primary"
            >
                Speichern
            </x-button>
        </div>
    </x-slot>
</x-modal>
