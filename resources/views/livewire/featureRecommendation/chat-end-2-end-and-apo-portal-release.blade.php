<div wire:key="chat-end-2-end-apo-portal-release">
    <div class="mb-5">
        <p>Die Sicherheit Ihrer Daten ist uns wichtig! Der Chat im GEDISA ApothekenPortal ist Ende-zu-Ende verschlüsselt.
            Nutzen Sie für Ihre Apotheken ab sofort die ApoPortal App (ehem. ApoConnect) für eine Zwei-Faktor-Authentifizierung im Chat.</p>
    </div>

    <x-advertisement.apoportal-information-links/>
    @if($this::showCheckboxGroup())
        <p class="mt-4">
            Bieten Sie den Ende-zu-Ende verschlüsselten Chat jetzt auch Ihren Kundinnen und Kunden an. Aktivieren
            Sie
            hier
            direkt den Kundenchat für Ihre Apotheken:
        </p>
        <div id="checkbox-group" class="mt-4">
            @foreach($this->pharmacies as $pharmacy)
                <x-input.checkbox
                    wire:model="selectedPharmacies"
                    :id="$pharmacy->id"
                    :value="$pharmacy->id"
                >
                    {{ $pharmacy->name }}
                </x-input.checkbox>
            @endforeach
            @error('selectedPharmacies')
            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>
    @endif
</div>

