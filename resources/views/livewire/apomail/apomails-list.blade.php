<div>
    @if (count($apomails))
        <x:stackedList>
            <x:stackedListHead :caret="user()->cannot('updateTos', \App\Apomail::class) && ! user()->pharmacies->every('uses_apomail', false) ? true : null">
                <div class="w-full md:w-10/12">E-Mail-Adresse</div>
                <div class="w-full md:w-2/12">Status</div>
            </x:stackedListHead>

            <x:stackedListBody>
                @foreach ($apomails as $apomail)
                    <x:stackedListRow :href="user()->can('update', $apomail) ? route('apomails.edit', $apomail) : null" :caret="user()->can('update', $apomail) ? true : null">
                        {{-- Email --}}
                        <div class="w-full md:w-10/12">
                            <div class="text-sm">
                                {{ $apomail->email }}
                                @if(! $apomail->notOnBlacklist())
                                    <p class="text-gray-500 text-xs">Die Verwendung der ApoMail-Adresse ist nicht zulässig, weil sie nicht unseren Namenskonventionen entspricht. Wenn Sie weitere Fragen haben, dann wenden Sie sich an unseren Support.</p>
                                @elseif(! $apomail->hasOnlyAllowedCharacters())
                                    <p class="text-gray-500 text-xs">Die Verwendung der ApoMail-Adresse ist nicht zulässig, weil sie nicht unseren Namenskonventionen entspricht (enthält nicht erlaubte Zeichen). Wenn Sie weitere Fragen haben, dann wenden Sie sich an unseren Support.</p>
                                @endif
                            </div>
                        </div>

                        {{-- Status --}}
                        <div class="w-full pt-1 md:w-2/12 md:pt-0">
                            @if(user()->can('update', $apomail))
                                @if($apomail->status === \App\Enums\ApomailStatus::RESERVED)
                                    <x-badge color="gray" class="opacity-50">@lang('apomail.status.reserved')</x-badge>
                                @endif
                                @if($apomail->status === \App\Enums\ApomailStatus::ACTIVE)
                                    <x-badge color="green">@lang('apomail.status.active')</x-badge>
                                @endif
                                @if($apomail->status === \App\Enums\ApomailStatus::DISABLED)
                                    <x-badge color="yellow">@lang('apomail.status.disabled')</x-badge>
                                @endif
                            @endif
                        </div>
                    </x:stackedListRow>
                @endforeach
            </x:stackedListBody>
        </x:stackedList>

        <div class="mt-6">
            {{ $apomails->links() }}
        </div>
    @else
        <x:alert type="info" class="mb-6" :title="__('apomail.no_apomails_found')" />
    @endif
</div>
