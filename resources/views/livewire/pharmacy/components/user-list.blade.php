<div>
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        {{-- List Header --}}
        <div class="hidden md:flex items-center justify-between px-6 py-3 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
            <div class="w-8/12">Name</div>
            <div class="w-3/12">Rolle</div>
            <div class="w-5"></div>
        </div>
        <ul>
            @foreach($users as $user)
                @if($user->isCompany())
                    @continue {{-- Don't show user if he is company user owner --}}
                @endif

                <li class="border-t border-gray-200">
                    @if(user()->can('update', $user) && user()->id !== $user->id && ! $user->hasPharmacyRole($pharmacy, \App\Enums\PharmacyRoleEnum::SUB_OWNER))
                        <a
                            href="{{ route('pharmacies.users.edit', [$pharmacy, $user]) }}"
                            class="block hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition duration-150 ease-in-out"
                        >
                    @else
                        <span class="block">
                    @endif
                    <div class="flex items-center px-4 py-4 sm:px-6">
                        <div class="min-w-0 flex-1 flex flex-col md:flex-row justify-between items-center">
                            {{-- Name --}}
                            <div class="flex items-center w-full md:w-8/12">
                                <div>
                                    <div class="text-sm leading-5 font-medium text-gray-900">
                                        {{ trim($user->title . ' ' . $user->last_name) . ', ' . $user->first_name }}

                                        @if($user->last_login === null)
                                            <span class="tooltip ml-1" data-tippy-content="@lang('messages.userNeverLoggedIn')">
                                                <svg class="inline-block h-4 w-4 text-gray-400 hover:text-gray-500"><use href="/icons.svg#signal-slash"/></svg>
                                            </span>
                                        @endif
                                    </div>
                                    @if($user->email)
                                        <div class="text-sm leading-5 text-gray-600">
                                            {{ $user->email }}
                                        </div>
                                    @endif
                                </div>
                            </div>

                            {{-- Rolle --}}
                            <div class="flex items-center w-full md:w-3/12">
                                <div>
                                    <div class="text-sm leading-5 font-medium text-gray-900">
                                        {{ \App\Enums\PharmacyRoleEnum::getLabel($user->getPharmacyRole($pharmacy)) }}
                                    </div>
                                </div>
                            </div>

                            {{-- Symbol --}}
                            @if(user()->can('update', $user) && user()->id !== $user->id && ! $user->hasPharmacyRole($pharmacy, \App\Enums\PharmacyRoleEnum::SUB_OWNER))
                                <div class="hidden sm:block">
                                    <svg class="h-5 w-5 text-gray-400"><use href="/icons.svg#chevron-right"/></svg>
                                </div>
                            @else
                                <div class="w-5"></div>
                            @endif
                        </div>
                    </div>
                    @if(user()->can('update', $user) && user()->id !== $user->id && ! $user->hasPharmacyRole($pharmacy, \App\Enums\PharmacyRoleEnum::SUB_OWNER))
                        </a>
                    @else
                        </span>
                    @endif
                </li>
            @endforeach
        </ul>
    </div>
    <div class="mt-6">
        {{ $users->links() }}
    </div>
</div>
