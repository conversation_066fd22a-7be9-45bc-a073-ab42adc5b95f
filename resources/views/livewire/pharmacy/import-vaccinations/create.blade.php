<div>
    @if($editMode)
        <p class="text-base text-center pb-5 leading-6 text-gray-500">
            <PERSON><PERSON> müssen alle Felder ausgefüllt werden
        </p>

        <x:layout.container size="max-w-2xl">
            @if ($errors->any())
                <x:row>
                    <x:col>
                        <div>
                            <x:alert-validation-error />
                        </div>
                    </x:col>
                </x:row>
            @endif

            <form wire:submit="submit">
                <x:card>
                    @can('recoveredCertificate', [\App\VaccinationImport::class, $pharmacy])
                        <x:row>
                            <x:col class="md:w-1/2">
                                <x:input.toggle
                                    wire:model.live="recoveredCertificate"
                                    wire:change="vaccineChange"
                                    :label="__('validation.attributes.recoveredCertificate')"
                                    name="recoveredCertificate"
                                    :error="$errors->first('recoveredCertificate')"
                                    size="lg"
                                    color="red"
                                />
                            </x:col>
                        </x:row>
                    @endcan
                    <x:row>
                        <x:col>
                            <div class="block w-full font-semibold text-xl pr-4 py-1 leading-5 text-gray-700">
                                Daten des Antragsstellers (Kunde)
                            </div>
                        </x:col>
                    </x:row>
                    <x:row class="mt-1">
                        <x:col class="md:w-1/2">
                            <x:input.text
                                wire:model="firstName"
                                :label="__('validation.attributes.firstName')"
                                name="firstName"
                                :error="$errors->first('firstName')"
                            />
                        </x:col>
                        <x:col class="md:w-1/2">
                            <x:input.text
                                wire:model="lastName"
                                :label="__('validation.attributes.lastName')"
                                name="lastName"
                                :error="$errors->first('lastName')"
                            />
                        </x:col>
                    </x:row>
                    <x:row class="mt-4">
                        <x:col class="xl:w-1/2">
                            <x:input.text
                                wire:model="birthdate"
                                :label="__('validation.attributes.birthdate')"
                                type="date"
                                name="birthdate"
                                placeholder="DD / MM / YYYY"
                                :error="$errors->first('birthdate')"
                            />
                        </x:col>
                    </x:row>
                    <x:row class="mt-8 border-t border-gray-200">
                        <x:col class="md:w-1/2">
                            <x:input.select
                                wire:model.live="vaccine"
                                wire:change="vaccineChange"
                                :label="__('validation.attributes.vaccine')"
                                :options="\App\Enums\VaccinationImport\VaccineNameEnum::getForDropdown()"
                                name="vaccine"
                                :error="$errors->first('vaccine')"
                            >
                                <option value="" disabled selected></option>
                            </x:input.select>
                        </x:col>
                        <x:col class="xl:w-1/2">
                            <x:input.text
                                wire:model="vaccinationDate"
                                :label="__('validation.attributes.vaccinationDate')"
                                type="date"
                                name="vaccinationDate"
                                placeholder="DD / MM / YYYY"
                                :error="$errors->first('vaccinationDate')"
                            />
                        </x:col>
                    </x:row>
                    <x:row class="mt-4">
                        <x:col class="md:w-1/2">
                            <x:input.select
                                wire:model.live="doseNumber"
                                :label="__('validation.attributes.doseNumber')"
                                :options="$doseOptions"
                                name="doseNumber"
                                :error="$errors->first('doseNumber')"
                                :disabled="count($doseOptions) < 2"
                            />
                        </x:col>
                    </x:row>

                    <div class="pt-8 mt-8 text-right border-t border-gray-200">
                        <span class="inline-flex rounded-md shadow-sm">
                            <x-button
                                size="md"
                                :href="route('pharmacies.importVaccination', [$pharmacy])"
                                appearance="secondary"
                                class="py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium text-gray-700 hover:text-gray-500 focus:outline-none focus:border-red-300 focus:shadow-outline-red active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out cursor-pointer"
                            >
                                Abbrechen
                            </x-button>
                        </span>
                        <span class="ml-3 inline-flex rounded-md shadow-sm">
                            <x-button
                                size="md"
                                class="inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-red-600 hover:bg-red-500 focus:outline-none focus:border-red-700 focus:shadow-outline-red active:bg-red-700 transition duration-150 ease-in-out"
                            >
                                Hinzufügen
                            </x-button>
                        </span>
                    </div>
                </x:card>
            </form>
        </x:layout.container>

    @else
        <div>
            <x:layout.container size="max-w-2xl">
                <x:card>
                    <div class="mt-4 text-center">
                        <x-button
                            wire:click="generateQr"
                            appearance="dark"
                            size="lg"
                            class="w-72"
                            wire:target="generateQr, generatePdf"
                            wire:loading.attr="disabled"
                        >
                            QR-Code anzeigen
                        </x-button>
                    </div>
                    <div class="mt-4 text-center">
                        <x-button
                            wire:click="generatePdf"
                            appearance="dark"
                            size="lg"
                            class="w-72"
                            wire:target="generateQr, generatePdf"
                            wire:loading.attr="disabled"
                        >
                            PDF herunterladen
                        </x-button>
                    </div>

                    <div class="mt-8">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Daten des Antragsstellers (Kunde)
                            </h3>
                        </div>
                        <div class="mt-2 border-t border-gray-200 pt-2 flex flex-wrap">
                            <dl class="w-full md:w-1/2">
                                <x:listEntry title="Name">
                                    {{ $firstName . ' ' . $lastName }}
                                </x:listEntry>
                            </dl>
                            <dl class="w-full md:w-1/2">
                                <x:listEntry title="{{ __('validation.attributes.birthdate') }}">
                                    {{ \Carbon\Carbon::parse($birthdate)->format('d.m.Y') }}
                                </x:listEntry>
                            </dl>
                        </div>
                    </div>

                    <div class="mt-8">
                        <div class="mt-2 border-t border-gray-200 pt-2 flex flex-wrap">
                            <dl class="w-full md:w-1/2">
                                <x:listEntry title="{{ __('validation.attributes.vaccineTarget') }}">
                                    {{ \App\Enums\VaccinationImport\VaccineTargetEnum::getLabel($vaccineTarget) }}
                                </x:listEntry>
                            </dl>
                            <dl class="w-full md:w-1/2">
                                <x:listEntry title="{{ __('validation.attributes.vaccineType') }}">
                                    {{ \App\Enums\VaccinationImport\VaccineTypeEnum::getLabel($vaccineType) }}
                                </x:listEntry>
                            </dl>
                        </div>
                        <div class="mt-4 pt-2 flex flex-wrap">
                            <dl class="w-full md:w-1/2">
                                <x:listEntry title="{{ __('validation.attributes.vaccineHolder') }}">
                                    {{ \App\Enums\VaccinationImport\VaccineHolderEnum::getLabel($vaccineHolder) }}
                                </x:listEntry>
                            </dl>
                            <dl class="w-full md:w-1/2">
                                <x:listEntry title="{{ __('validation.attributes.vaccine') }}">
                                    {{ \App\Enums\VaccinationImport\VaccineNameEnum::getLabel($vaccine) }}
                                </x:listEntry>
                            </dl>
                        </div>
                        <div class="mt-4 pt-2 flex flex-wrap">
                            <dl class="w-full md:w-1/2">
                                <x:listEntry title="Datum">
                                    {{ \Carbon\Carbon::parse($vaccinationDate)->format('d.m.Y') }}
                                </x:listEntry>
                            </dl>
                            <dl class="w-full md:w-1/2">
                                <x:listEntry title="Dosis">
                                    {{ $doseNumber . '/' . $doseCount }}
                                </x:listEntry>
                            </dl>
                        </div>
                    </div>

                    <div class="mt-10 space-y-4">
                        @unless($importCreated)
                            <div x-data="{show: true}" x-show="show" class="text-center" @hide-edit-button.window="show = false">
                                <x-button
                                    wire:click="edit"
                                    size="lg"
                                    appearance="secondary"
                                    class="w-72"
                                >
                                    Daten anpassen
                                </x-button>
                            </div>
                        @endunless

                        <div class="text-center">
                            <x-button
                                :href="route('pharmacies.importVaccination', [$pharmacy])"
                                size="lg"
                                class="w-72"
                            >
                                Zurück zur Übersicht
                            </x-button>
                        </div>
                    </div>
                </x:card>
            </x:layout.container>
            <x:modal name="qr-code-modal">
                <div class="p-4">
                    <img class="w-full" src="data:image/png;base64, {!! base64_encode(QrCode::format('png')->size(500)->generate($qrCodeString ?? 'empty')) !!} ">
                </div>
                <p>
                    Dieser QR Code kann durch den Patienten mit entsprechender App gescannt werden und so importiert werden.
                </p>
                <div class="mt-5 sm:mt-4 sm:ml-10 sm:pl-4 sm:flex sm:justify-end">
                    <button @click="$dispatch('change-modal-state', { name: 'qr-code-modal', state: 'close' })" type="button" class="inline-flex justify-center w-full rounded-md border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm">
                        Schließen
                    </button>
                </div>
            </x:modal>
        </div>
    @endif
</div>
