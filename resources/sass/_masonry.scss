.pw-hidden .pagination-wrapper {
    display: none;
}

.masonry-loading-animation {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}

.masonry-loading-animation div {
    position: absolute;
    top: 33px;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: #E10019;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.masonry-loading-animation div:nth-child(1) {
    left: 8px;
    animation: masonry-loading-animation1 0.6s infinite;
}

.masonry-loading-animation div:nth-child(2) {
    left: 8px;
    animation: masonry-loading-animation2 0.6s infinite;
}

.masonry-loading-animation div:nth-child(3) {
    left: 32px;
    animation: masonry-loading-animation2 0.6s infinite;
}

.masonry-loading-animation div:nth-child(4) {
    left: 56px;
    animation: masonry-loading-animation3 0.6s infinite;
}

@keyframes masonry-loading-animation1 {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes masonry-loading-animation3 {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(0);
    }
}

@keyframes masonry-loading-animation2 {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(24px, 0);
    }
}
